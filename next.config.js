import createNextIntlPlugin from "next-intl/plugin";

/** @type {import('next').NextConfig} */
const nextConfig = {
  typescript: {
    // !! WARN !!
    // Dangerously allow production builds to successfully complete even if
    // your project has type errors.
    // !! WARN !!
    ignoreBuildErrors: true,
  },
  eslint: {
    // Warning: This allows production builds to successfully complete even if
    // your project has ESLint errors.
    ignoreDuringBuilds: true,
  },
  experimental: {
    // These experimental features might help with Turbopack font issues
    optimizePackageImports: ['@vercel/analytics', '@vercel/speed-insights'],
  },
  webpack: (config, { isServer: _isServer }) => {
    // Handle font loading issues
    config.module.rules.push({
      test: /\.(woff|woff2|eot|ttf|otf)$/i,
      type: 'asset/resource',
    });
    
    // Disable persistent caching to fix "hasStartTime" error
    config.cache = {
      type: 'memory',
      maxGenerations: 1,
    };
    
    return config;
  }
};

const withNextIntl = createNextIntlPlugin();
export default withNextIntl(nextConfig); 
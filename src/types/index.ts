// Re-export all types
export * from './project';
// Import and re-export AssessmentCriteria types
export * from './assessmentCriteria';

export interface User {
  id: string;
  email: string;
  role: 'Client' | 'Consultant' | 'Admin';
  displayName?: string;
  photoURL?: string;
  companyId?: string;
  assignedProjectIds?: string[];
  isActive: boolean;
  createdAt: Date | { toDate: () => Date } | null;
}

export interface Project {
  id: string;
  name: {
    en: string;
    ar: string;
  };
  description?: {
    en: string;
    ar: string;
  };
  status: string;
  startDate: string;
  projectDeadline: string;
  frameworkId?: string;
  clientId?: string;
  consultantId?: string;
  createdAt: Date | { toDate: () => Date } | null;
}

export interface Framework {
  id: string;
  name: {
    en: string;
    ar: string;
  };
  description?: {
    en: string;
    ar: string;
  };
  categories?: FrameworkCategory[];
  createdAt: Date | { toDate: () => Date } | null;
}

export interface FrameworkCategory {
  id: string;
  name: {
    en: string;
    ar: string;
  };
  subcategories?: FrameworkSubcategory[];
}

export interface FrameworkSubcategory {
  id: string;
  name: {
    en: string;
    ar: string;
  };
  questions?: Question[];
}

export interface Question {
  id: string;
  text: {
    en: string;
    ar: string;
  };
  type: 'single' | 'multiple' | 'rating';
  options?: Option[];
}

export interface Option {
  id: string;
  text: {
    en: string;
    ar: string;
  };
  value: number;
}

export interface MaturityAssessment {
  id?: string;
  projectId?: string;
  title: {
    en: string;
    ar: string;
  };
  status: {
    en: string;
    ar: string;
  };
  createdAt?: Date | { toDate: () => Date } | null;
  updatedAt?: Date | { toDate: () => Date } | null;
}

// Added for AssessmentDetailsPage
export interface LocalizedString {
    en: string;
    ar: string;
    [key: string]: string; // Allow other languages
}

// SubSpecification interface
export interface SubSpecification {
  id?: string;
  name?: {
    en: string;
    ar: string;
  };
  description?: {
    en: string;
    ar: string;
  };
  versionHistory?: Array<{
    version: string;
    date: string;
  }>;
  updatedAt?: string;
  [key: string]: unknown;
}

export interface Domain {
    id: string;
    name: string | LocalizedString | {
        en: string;
        ar: string;
    };
    description?: string | LocalizedString;
    score: number;
    maxScore: number;
    subcategories?: Array<{
        id: string;
        name: LocalizedString;
        description?: LocalizedString;
        score: number;
    }>;
    category?: string;
    priority?: string;
    specifications?: Specification[];
    swotAnalysisCompleted?: boolean;
    [key: string]: unknown; // Allow additional properties
}

export interface SWOTItem {
  en: string;
  ar: string;
  id?: string; // Optional ID for tracking items
}

export interface SWOTAnalysis {
    strengths: SWOTItem[];
    weaknesses: SWOTItem[];
    opportunities: SWOTItem[];
    threats: SWOTItem[];
}

export interface Assessment {
    id: string;
    title: LocalizedString;
    project: LocalizedString; // Assuming project name is also localized
    status: LocalizedString; // Assuming status can be localized
    description: LocalizedString;
    createdAt: string; // Assuming ISO string
    updatedAt: string; // Assuming ISO string
    overallScore?: number;
    domains: Domain[];
    swot: SWOTAnalysis;
}

// Props for components in AssessmentDetailsPage
export interface KpiCardProps {
    title: string; // This will be a translated key
    value: string | number;
    icon?: React.ReactNode;
    locale: string;
    children?: React.ReactNode;
}

export interface DomainDetailCardProps {
    domain: Domain;
    locale: string;
    t: (key: string) => string;
}

export interface SwotSectionProps {
    swot: SWOTAnalysis;
    locale: string;
    t: (key: string) => string;
}

export interface SwotCategoryProps {
    title: string; // This will be a translated key
    items: SWOTItem[];
    locale: string;
    itemClassName: string;
    titleClassName: string;
    t: (key: string) => string;
}

export interface ChartPlaceholderProps {
    title: string; // This will be a translated key
    locale: string;
    t: (key: string) => string;
}

// Export AssessmentCriteriaType
export type AssessmentCriteriaType = 'maturity' | 'percentage' | 'compliance';

// Added missing interfaces needed for TypeScript errors
export interface AssessmentCriteriaLevel {
    label: {
        en: string;
        ar: string;
        [key: string]: string | undefined;
    };
    description?: {
        en: string;
        ar: string;
        [key: string]: string | undefined;
    };
    value: number;
}

export interface DomainWeight {
  domainId: string;
  weight: number;
}

export interface LevelDefinition {
  label: string | LocalizedString | {
    en: string;
    ar: string;
    [key: string]: string | undefined;
  };
  value: number;
  description?: string | LocalizedString | {
    en: string;
    ar: string;
    [key: string]: string | undefined;
  };
  color?: string;
}

export interface AssessmentCriteria {
    id: string;
    type: AssessmentCriteriaType;
    name: string | LocalizedString;
    description?: string | LocalizedString;
    levels?: Record<string, LevelDefinition>;
    frameworkId?: string;
    createdAt?: Date;
    values?: Record<string, unknown>;
    descriptions?: Record<string, unknown>;
    domainWeights?: DomainWeight[];
}

export interface Specification {
    id: string;
    name: string | LocalizedString;
    description?: string | LocalizedString;
    domainId: string;
    maturityLevel?: number;
    percentageValue?: number;
    complianceStatus?: string;
    currentRating?: number; // Percentage value (0-100)
    subSpecifications?: SubSpecification[];
    [key: string]: unknown; // Allow additional properties
}

export interface SpecificationRating {
  ratingType: AssessmentCriteriaType;
  specificationId: string;
  currentRating?: number;
  targetRating?: number;
  complianceStatus?: string;
  targetStatus?: string;
  percentageValue?: number;
  targetPercentage?: number;
  comments?: string;
  dataRating?: string;
  createdAt?: Date;
  updatedAt?: Date | string | null;
  createdBy?: string;
  updatedBy?: string;
  subSpecificationStates?: { id: string, state: string }[];
  domainName?: {
    en: string;
    ar: string;
  };
  controlName?: {
    en: string;
    ar: string;
  };
} 

// Data Assets interfaces
export interface AssetType {
  id: string;
  title: {
    en: string;
    ar: string;
  };
  description: {
    en: string;
    ar: string;
  };
  projectId: string;
  createdAt?: Date | { toDate: () => Date } | null;
  updatedAt?: Date | { toDate: () => Date } | null;
  createdBy?: string;
  updatedBy?: string;
}
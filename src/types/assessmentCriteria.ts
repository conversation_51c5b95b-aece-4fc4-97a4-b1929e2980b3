export type AssessmentCriteriaType = 'maturity' | 'percentage' | 'compliance';

export interface AssessmentCriteriaLevel {
  label: {
    en: string;
    ar: string;
    [key: string]: string | undefined;
  };
  description?: {
    en: string;
    ar: string;
    [key: string]: string | undefined;
  };
  value: number;
}

export interface DomainWeight {
  domainId: string;
  weight: number;
}

export interface LevelDefinition {
  label: string | { en: string; ar: string; [key: string]: string | undefined };
  value: number;
  description?: string | { en: string; ar: string; [key: string]: string | undefined };
  color?: string;
}

export interface AssessmentCriteria {
  id: string;
  type: AssessmentCriteriaType;
  name: string | { en: string; ar: string; [key: string]: string };
  description?: string | { en: string; ar: string; [key: string]: string };
  frameworkId?: string;
  createdAt?: Date;
  levels?: Record<string, AssessmentCriteriaLevel | LevelDefinition>;
  values?: Record<string, unknown>;
  descriptions?: Record<string, unknown>;
  domainWeights?: DomainWeight[];
}

export interface MaturityRating {
  ratingType: 'maturity';
  specificationId: string;
  currentRating: number;
  targetRating: number;
  comments?: string;
  updatedAt?: Date | string | null;
  updatedBy?: string;
  subSpecificationStates?: Array<{ id: string; state: string }>;
  dataRating?: string | null;
}

export interface PercentageRating {
  ratingType: 'percentage';
  specificationId: string;
  percentageValue: number;
  targetPercentage: number;
  comments?: string;
  updatedAt?: Date | string | null;
  updatedBy?: string;
  subSpecificationStates?: Array<{ id: string; state: string }>;
  dataRating?: string | null;
}

export interface ComplianceRating {
  ratingType: 'compliance';
  specificationId: string;
  complianceStatus: string;
  targetStatus: string;
  comments?: string;
  updatedAt?: Date | string | null;
  updatedBy?: string;
  subSpecificationStates?: Array<{ id: string; state: string }>;
  dataRating?: string | null;
}

export interface SpecificationRating {
  ratingType: AssessmentCriteriaType;
  specificationId: string;
  currentRating?: number;
  targetRating?: number;
  complianceStatus?: string;
  targetStatus?: string;
  percentageValue?: number;
  targetPercentage?: number;
  comments?: string;
  dataRating?: string;
  createdAt?: Date;
  updatedAt?: Date | string | null;
  createdBy?: string;
  updatedBy?: string;
  subSpecificationStates?: { id: string, state: string }[];
  domainName?: {
    en: string;
    ar: string;
  };
  controlName?: {
    en: string;
    ar: string;
  };
}

export type AssessmentValue = number | string | boolean | null; 
'use client';

import { useEffect } from 'react';
import { useAuthSession } from '@/hooks/useAuthSession';
import { useRouter } from 'next/navigation';
import { useLocale } from 'next-intl';
import { Loader } from './Loader';

interface ProtectedRouteProps {
    children: React.ReactNode;
    allowedRoles?: Array<'Client' | 'Consultant'>;
}

export default function ProtectedRoute({
    children,
    allowedRoles = ['Client', 'Consultant']
}: ProtectedRouteProps) {
    const { session, isLoading, isInitialized, logout } = useAuthSession();
    const router = useRouter();
    const locale = useLocale();

    useEffect(() => {
        // Only perform checks after auth is initialized
        if (!isInitialized) return;

        // If user is not authenticated, redirect to login
        if (!session && !isLoading) {
            router.push(`/${locale}/login`);
            return;
        }

        // If user is authenticated but doesn't have allowed role or isn't active
        if (session && !isLoading) {
            // Check if user has required role
            const hasAllowedRole = allowedRoles.includes(session.role);

            // Check if user is active
            const isActive = session.status === 'Active';

            if (!hasAllowedRole || !isActive) {
                // Sign out and redirect to unauthorized
                logout().then(() => {
                    router.push(`/${locale}/unauthorized`);
                });
            }
        }
    }, [session, isInitialized, isLoading, router, locale, allowedRoles, logout]);

    // Show loading component while checking auth
    if (isLoading || !isInitialized || !session) {
        return (
            <div className="h-screen flex items-center justify-center from-slate-800 via-slate-900 to-slate-950">
            <Loader size="lg" variant="primary" />
          </div>
        );
    }

    // User is authenticated and has permission
    return <>{children}</>;
} 
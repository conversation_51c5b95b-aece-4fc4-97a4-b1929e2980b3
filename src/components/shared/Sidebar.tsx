import { motion } from 'framer-motion';
import {
    HomeIcon,
    ChartBarIcon,
    MapIcon,
    CircleStackIcon,
    ServerIcon,
    DocumentTextIcon,
    ChatBubbleLeftRightIcon,
    ArrowLeftOnRectangleIcon,
    ChevronLeftIcon,
    ChevronRightIcon,
    LanguageIcon
} from '@heroicons/react/24/outline';
import { cn } from '@/lib/utils';
import { useState, useEffect, startTransition, memo } from 'react';
import { FRAMER_TRANSITIONS } from '@/lib/design/transitions';
import { SLIDE_RIGHT } from '@/lib/design/framer-animations';
import { usePathname, useRouter, Link } from "@/i18n/navigation";
import { useAuthSession } from '@/hooks/useAuthSession';

interface SidebarProps {
    className?: string;
    user?: {
        email: string;
        role: string;
    };
}

// Use memo to prevent unnecessary re-renders
const Sidebar = memo(function Sidebar({ className, user = { email: '<EMAIL>', role: 'User Role' } }: SidebarProps) {
    const [isCollapsed, setIsCollapsed] = useState(false);
    const router = useRouter();
    const pathname = usePathname();
    const [currentLanguage, setCurrentLanguage] = useState("en");
    const { logout } = useAuthSession();

    // Auto-collapse sidebar on small screens
    useEffect(() => {
        const handleResize = () => {
            if (window.innerWidth < 768) {
                setIsCollapsed(true);
            }
        };

        // Set initial state
        handleResize();

        // Add event listener
        window.addEventListener('resize', handleResize);

        // Cleanup
        return () => window.removeEventListener('resize', handleResize);
    }, []);

    // Get current language from URL or cookie on component mount
    useEffect(() => {
        // Check URL first for language
        const urlLanguage = pathname.split("/")[1];
        if (["en", "ar"].includes(urlLanguage)) {
            setCurrentLanguage(urlLanguage);
            return;
        }

        // Fall back to cookie if URL doesn't have language
        const savedLanguage =
            document.cookie
                .split("; ")
                .find((row) => row.startsWith("NEXT_LOCALE="))
                ?.split("=")[1] || "en";
        setCurrentLanguage(savedLanguage);
    }, [pathname]);

    // Set document direction based on currentLanguage
    useEffect(() => {
        if (currentLanguage === 'ar') {
            document.documentElement.dir = 'rtl';
        } else {
            document.documentElement.dir = 'ltr';
        }
    }, [currentLanguage]);

    // Load translations manually
    const translations: Record<string, Record<string, string>> = {
        en: {
            homePage: "Home Page",
            maturityAssessment: "Compliance Assessment",
            transformationRoadmap: "Transformation Roadmap",
            dataAssets: "Data Assets",
            logicalArchitecture: "Logical Architecture",
            reporting: "Reporting",
            feedback: "Feedback",
            logout: "Logout",
            projectSelection: "Project Selection",
            switchProjects: "Switch between your assigned projects"
        },
        ar: {
            homePage: "الصفحة الرئيسية",
            maturityAssessment: "تقييم الامتثال",
            transformationRoadmap: "خارطة طريق التحول",
            dataAssets: "أصول البيانات",
            logicalArchitecture: "البنية المنطقية",
            reporting: "التقارير",
            feedback: "الملاحظات",
            logout: "تسجيل الخروج",
            projectSelection: "اختيار المشروع",
            switchProjects: "التبديل بين المشاريع المخصصة لك"
        }
    };

    // Simple translation function
    const t = (key: string) => {
        return translations[currentLanguage]?.[key] || key;
    };

    const changeLanguage = (newLanguage: string) => {
        // First update the document direction and cookie
        document.documentElement.dir = newLanguage === 'ar' ? 'rtl' : 'ltr';
        document.cookie = `NEXT_LOCALE=${newLanguage}; path=/;`;

        // Then update the state
        setCurrentLanguage(newLanguage);

        // Prepare the new path
        const segments = pathname.split("/");
        if (["en", "ar"].includes(segments[1])) {
            segments[1] = newLanguage;
        } else {
            segments.splice(1, 0, newLanguage);
        }

        // Use startTransition to improve visual transition
        startTransition(() => {
            // Use router.replace instead of push to avoid unmounting
            router.replace(segments.join("/"), { locale: newLanguage });
            // Remove router.refresh() to prevent layout teardown
        });
    };

    const languageLabels = {
        en: "English",
        ar: "العربية",
    };

    const sidebarVariants = {
        expanded: { width: '280px' },
        collapsed: { width: '80px' },
    };

    const navItems = [
        { icon: HomeIcon, label: t('homePage'), path: `/dashboard` },
        { icon: ChartBarIcon, label: t('maturityAssessment'), path: `/maturity-assessment` },
        { icon: MapIcon, label: t('transformationRoadmap'), path: `/roadmap` },
        { icon: CircleStackIcon, label: t('dataAssets'), path: `/data-assets` },    
        { icon: ServerIcon, label: t('logicalArchitecture'), path: `/architecture` },
        { icon: DocumentTextIcon, label: t('reporting'), path: `/reporting` },
        { icon: ChatBubbleLeftRightIcon, label: t('feedback'), path: `/feedback` },
    ];

    return (
        <motion.div
            initial={isCollapsed ? 'collapsed' : 'expanded'}
            animate={isCollapsed ? 'collapsed' : 'expanded'}
            variants={sidebarVariants}
            transition={FRAMER_TRANSITIONS.spring}
            className={cn(
                "h-screen bg-gradient-to-b from-[#003874] to-[#48D3A5] text-white flex flex-col justify-between",
                "overflow-y-auto overflow-x-hidden scrollbar-thin scrollbar-thumb-white/20 scrollbar-track-transparent",
                "max-h-screen sticky top-0 left-0 z-40",
                className,
                currentLanguage === "ar" ? "rtl" : "ltr"
            )}
        >
            {/* Top Section */}
            <div>
                <div className="p-4 flex items-center justify-between border-b border-white/10">
                    <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 rounded-full bg-white/20 flex items-center justify-center">
                            <span className="text-xl">👤</span>
                        </div>
                        {!isCollapsed && (
                            <motion.div
                                variants={SLIDE_RIGHT}
                                initial="hidden"
                                animate="visible"
                                exit="exit"
                                className="max-w-[180px]"
                            >
                                <h2 className="font-semibold truncate">{user.email}</h2>
                                <p className="text-sm text-white/70 truncate">{user.role}</p>
                            </motion.div>
                        )}
                    </div>
                </div>

                {/* Navigation Items */}
                <nav className="p-3 space-y-1">
                    {navItems.map((item, index) => (
                        <Link
                            key={index}
                            href={item.path}
                            className="w-full flex items-center px-4 py-3 rounded-lg transition-colors hover:bg-white/10 relative block"
                        >
                            <motion.div
                                className="flex items-center w-full"
                                whileHover={{ scale: 1.02 }}
                                whileTap={{ scale: 0.98 }}
                                transition={FRAMER_TRANSITIONS.spring}
                            >
                                <item.icon className="w-6 h-6 min-w-[24px]" />
                                {!isCollapsed && (
                                    <motion.span
                                        className={`whitespace-nowrap ${currentLanguage === "ar" ? "mr-3 ml-0" : "ml-3"}`}
                                        variants={SLIDE_RIGHT}
                                        initial="hidden"
                                        animate="visible"
                                        exit="exit"
                                    >
                                        {item.label}
                                    </motion.span>
                                )}
                            </motion.div>
                        </Link>
                    ))}
                </nav>
            </div>

            {/* Bottom Section */}
            <div className="p-3 border-t border-white/10 space-y-2">
                {/* Project Selection Button - Special Styling */}
                {!isCollapsed && (
                    <Link href={`/${currentLanguage}/project-selection`} className="block">
                        <motion.div
                            className="w-full p-3 rounded-xl relative cursor-pointer overflow-hidden bg-gradient-to-r from-[#003874] to-[#48D3A5] hover:from-[#00305e] hover:to-[#41c093]"
                            whileHover={{ scale: 1.03, y: -2 }}
                            whileTap={{ scale: 0.98 }}
                        >
                            {/* Decorative elements */}
                            <div className="absolute top-0 right-0 w-16 h-16 rounded-full opacity-20 bg-white transform translate-x-1/3 -translate-y-1/3" />
                            <div className="absolute bottom-0 left-0 w-12 h-12 rounded-full opacity-10 bg-white transform -translate-x-1/3 translate-y-1/3" />

                            {/* Main content */}
                            <div className="flex flex-col gap-1 text-white">
                                <div>
                                    <h2 className=" font-bold">{t('projectSelection')}</h2>
                                    <p className="text-sm opacity-80 mt-1">{t('switchProjects')}</p>
                                </div>
                            </div>
                        </motion.div>
                    </Link>
                )}

                {/* Simplified Project Selection button for collapsed mode */}
                {isCollapsed && (
                    <Link href={`/${currentLanguage}/project-selection`} className="block">
                        <motion.button
                            className="w-full flex items-center justify-center p-4 rounded-xl bg-gradient-to-r from-[#003874] to-[#48D3A5] hover:from-[#00305e] hover:to-[#41c093]"
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                        >
                            <ChevronRightIcon className="w-6 h-6 min-w-[24px] text-white" />
                        </motion.button>
                    </Link>
                )}

                <motion.button
                    className="w-full flex items-center px-4 py-3 rounded-lg transition-colors hover:bg-white/10"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    transition={FRAMER_TRANSITIONS.spring}
                    onClick={() => changeLanguage(currentLanguage === "en" ? "ar" : "en")}
                >
                    <LanguageIcon className=" w-6 h-6 min-w-[24px]" />
                    {!isCollapsed && (
                        <motion.span
                            className={`whitespace-nowrap ${currentLanguage === "ar" ? "mr-3 ml-0" : "ml-3"}`}
                            variants={SLIDE_RIGHT}
                            initial="hidden"
                            animate="visible"
                            exit="exit"
                        >
                            {languageLabels[currentLanguage as keyof typeof languageLabels]}
                        </motion.span>
                    )}
                </motion.button>

                <motion.button
                    className="w-full flex items-center px-4 py-3 rounded-lg transition-colors hover:bg-white/10"
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    transition={FRAMER_TRANSITIONS.spring}
                    onClick={logout}
                >
                    <ArrowLeftOnRectangleIcon className="w-6 h-6 min-w-[24px]" />
                    {!isCollapsed && (
                        <motion.span
                            className={`whitespace-nowrap ${currentLanguage === "ar" ? "mr-3 ml-0" : "ml-3"}`}
                            variants={SLIDE_RIGHT}
                            initial="hidden"
                            animate="visible"
                            exit="exit"
                        >
                            {t('logout')}
                        </motion.span>
                    )}
                </motion.button>

                <motion.button
                    className="w-full flex items-center justify-center p-2 rounded-lg hover:bg-white/10"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => setIsCollapsed(!isCollapsed)}
                    transition={FRAMER_TRANSITIONS.spring}
                >
                    {isCollapsed ? <ChevronRightIcon className="w-6 h-6 min-w-[24px]" /> : <ChevronLeftIcon className="w-6 h-6" />}
                </motion.button>
            </div>
        </motion.div>
    );
});

export default Sidebar; 
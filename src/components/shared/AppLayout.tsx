"use client";
import { ReactNode, memo, useState, useEffect, useCallback, startTransition } from 'react';
import Sidebar from './Sidebar';
import { useLocale } from 'next-intl';
import { usePathname, useRouter } from "@/i18n/navigation";
import { useAuthSession } from '@/hooks/useAuthSession';

interface AppLayoutProps {
    children: ReactNode;
    user?: {
        email: string;
        role: string;
    };
}

// Use memo to prevent unnecessary re-renders
const AppLayout = memo(function AppLayout({ children, user }: AppLayoutProps) {
    const locale = useLocale();
    const pathname = usePathname();
    const router = useRouter();
    const { logout } = useAuthSession();
    const [currentLanguage, setCurrentLanguage] = useState(locale);

    // Update language when locale changes
    useEffect(() => {
        setCurrentLanguage(locale);
    }, [locale]);

    // Get current language from URL or cookie on component mount
    useEffect(() => {
        // Check URL first for language
        const urlLanguage = pathname.split("/")[1];
        if (["en", "ar"].includes(urlLanguage)) {
            setCurrentLanguage(urlLanguage);
            return;
        }

        // Fall back to cookie if URL doesn't have language
        const savedLanguage =
            document.cookie
                .split("; ")
                .find((row) => row.startsWith("NEXT_LOCALE="))
                ?.split("=")[1] || "en";
        setCurrentLanguage(savedLanguage);
    }, [pathname]);

    // Handle language change - memoized to prevent Sidebar re-renders
    const handleLanguageChange = useCallback((newLanguage: string) => {
        // First update the document direction and cookie
        document.documentElement.dir = newLanguage === 'ar' ? 'rtl' : 'ltr';
        document.cookie = `NEXT_LOCALE=${newLanguage}; path=/;`;

        // Then update the state
        setCurrentLanguage(newLanguage);

        // Prepare the new path
        const segments = pathname.split("/");
        if (["en", "ar"].includes(segments[1])) {
            segments[1] = newLanguage;
        } else {
            segments.splice(1, 0, newLanguage);
        }

        // Use startTransition to improve visual transition
        startTransition(() => {
            // Use router.replace instead of push to avoid unmounting
            router.replace(segments.join("/"), { locale: newLanguage });
        });
    }, [pathname, router]);

    // Handle logout
    const handleLogout = useCallback(() => {
        logout();
    }, [logout]);

    return (
        <div className="flex h-screen overflow-hidden">
            <Sidebar
                user={user}
                currentLanguage={currentLanguage}
                onLanguageChange={handleLanguageChange}
                onLogout={handleLogout}
            />
            <main className="flex-1 overflow-auto">
                <div className="flex flex-col min-h-screen">
                    {children}
                </div>
            </main>
        </div>
    );
}, (prevProps, nextProps) => {
    // Custom comparison function to prevent unnecessary re-renders
    return (
        prevProps.user?.email === nextProps.user?.email &&
        prevProps.user?.role === nextProps.user?.role &&
        prevProps.children === nextProps.children
    );
});

export default AppLayout;
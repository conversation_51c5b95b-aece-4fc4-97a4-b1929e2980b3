"use client";
import { ReactNode, memo } from 'react';
import Sidebar from './Sidebar';

interface AppLayoutProps {
    children: ReactNode;
    user?: {
        email: string;
        role: string;
    };
}

// Use memo to prevent unnecessary re-renders
const AppLayout = memo(function AppLayout({ children, user }: AppLayoutProps) {
    return (
        <div className="flex h-screen overflow-hidden">
            <Sidebar user={user} />
            <main className="flex-1 overflow-auto">
                <div className="flex flex-col min-h-screen">
                    {children}
                </div>
            </main>
        </div>
    );
});

export default AppLayout;
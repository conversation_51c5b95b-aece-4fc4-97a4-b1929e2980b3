"use client";
import { ReactNode, useMemo, memo } from 'react';
import { usePathname } from 'next/navigation';
import AppLayout from './AppLayout';
import ProtectedRoute from './ProtectedRoute';
import { useAuthSession } from '@/hooks/useAuthSession';

interface LayoutWrapperProps {
    children: ReactNode;
    locale: string;
}

const LayoutWrapper = memo(function LayoutWrapper({ children, locale: _locale }: LayoutWrapperProps) {
    const pathname = usePathname();
    const { session } = useAuthSession();

    // Memoize page type checks to reduce re-calculations
    const pageTypes = useMemo(() => {
        const isLoginPage = pathname?.includes('/login') || false;
        const isUnauthorizedPage = pathname?.includes('/unauthorized') || false;
        const isProjectSelectionPage = pathname?.includes('/project-selection') || false;

        return {
            isLoginPage,
            isUnauthorizedPage,
            isProjectSelectionPage,
            shouldShowSidebar: !isLoginPage && !isUnauthorizedPage && !isProjectSelectionPage
        };
    }, [pathname]);

    // Memoize the user object to prevent unnecessary re-renders
    const user = useMemo(() => ({
        email: session?.email || '',
        role: session?.role || ''
    }), [session?.email, session?.role]);

    // For login, unauthorized, and project-selection pages, render without sidebar
    if (!pageTypes.shouldShowSidebar) {
        return <>{children}</>;
    }

    // For protected pages, wrap with ProtectedRoute and AppLayout
    return (
        <ProtectedRoute>
            <AppLayout user={user}>
                {children}
            </AppLayout>
        </ProtectedRoute>
    );
});

export default LayoutWrapper;
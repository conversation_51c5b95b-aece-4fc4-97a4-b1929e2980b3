"use client";
import { ReactNode, useEffect, useState, useMemo } from 'react';
import { usePathname } from 'next/navigation';
import AppLayout from './AppLayout';
import ProtectedRoute from './ProtectedRoute';
import { useAuthSession } from '@/hooks/useAuthSession';

interface LayoutWrapperProps {
    children: ReactNode;
    locale: string;
}

export default function LayoutWrapper({ children, locale: _locale }: LayoutWrapperProps) {
    const pathname = usePathname();
    const { session } = useAuthSession();
    const [isLoginPage, setIsLoginPage] = useState(false);
    const [isUnauthorizedPage, setIsUnauthorizedPage] = useState(false);
    const [isProjectSelectionPage, setIsProjectSelectionPage] = useState(false);

    // Memoize the user object to prevent unnecessary re-renders
    const user = useMemo(() => ({
        email: session?.email || '',
        role: session?.role || ''
    }), [session?.email, session?.role]);

    useEffect(() => {
        // Check if the current path is login, unauthorized, or project-selection
        setIsLoginPage(pathname?.includes('/login') || false);
        setIsUnauthorizedPage(pathname?.includes('/unauthorized') || false);
        setIsProjectSelectionPage(pathname?.includes('/project-selection') || false);
    }, [pathname]);

    // For login, unauthorized, and project-selection pages, render without sidebar
    if (isLoginPage || isUnauthorizedPage || isProjectSelectionPage) {
        return <>{children}</>;
    }

    // For protected pages, wrap with ProtectedRoute and AppLayout
    return (
        <ProtectedRoute>
            <AppLayout user={user}>
                {children}
            </AppLayout>
        </ProtectedRoute>
    );
} 
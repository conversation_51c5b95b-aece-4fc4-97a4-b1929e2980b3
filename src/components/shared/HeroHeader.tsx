"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { cn } from "@/lib/utils";

interface HeroHeaderProps {
    title?: string;
    description?: string;
    children?: React.ReactNode;
    className?: string;
    isRTL?: boolean;
    height?: "auto" | "full";
    backgroundClassName?: string;
}

export function HeroHeader({
    title,
    description,
    children,
    className,
    isRTL = false,
    height = "auto",
    backgroundClassName
}: HeroHeaderProps) {
    return (
        <div
            className={cn(
                "relative overflow-hidden",
                height === "full" ? "min-h-screen" : "mb-8",
                backgroundClassName || "bg-gradient-to-r from-[#003874] via-[#2D8DC6] to-[#48D3A5]",
                className
            )}
        >
            {/* Decorative Elements */}
            <motion.div
                className={cn(
                    "absolute top-0 w-64 h-64 rounded-full opacity-30 bg-white",
                    isRTL ? "left-0" : "right-0"
                )}
                initial={{ x: isRTL ? -100 : 100, y: -100 }}
                animate={{
                    x: 0,
                    y: 0,
                    scale: [1, 1.2, 1],
                    rotate: [0, 45, 0],
                }}
                transition={{ duration: 20, repeat: Infinity, repeatType: "reverse" }}
            />
            <motion.div
                className={cn(
                    "absolute bottom-0 w-32 h-32 rounded-full opacity-30 bg-white",
                    isRTL ? "right-1/4" : "left-1/4"
                )}
                initial={{ y: 50 }}
                animate={{
                    y: [0, 20, 0],
                    scale: [1, 1.1, 1],
                }}
                transition={{ duration: 8, repeat: Infinity, repeatType: "reverse" }}
            />
            <motion.div
                className="absolute top-1/3 right-1/4 w-48 h-48 rounded-full opacity-20 bg-white"
                initial={{ y: -20 }}
                animate={{
                    y: [0, -30, 0],
                    scale: [1, 1.2, 1],
                }}
                transition={{ duration: 12, repeat: Infinity, repeatType: "reverse" }}
            />

            {/* Hero Content */}
            <div className={cn(
                "relative z-10 container mx-auto px-10 py-8 flex flex-col",
                height === "full" ? "justify-center min-h-screen" : ""
            )}>
                {title && (
                    <motion.h1
                        className="text-4xl md:text-5xl font-bold text-white mb-4"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.5 }}
                    >
                        {title}
                    </motion.h1>
                )}

                {description && (
                    <motion.p
                        className="text-xl text-white/80 mb-8 max-w-2xl"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.5, delay: 0.2 }}
                    >
                        {description}
                    </motion.p>
                )}

                {children}
            </div>
        </div>
    );
} 
import { motion } from 'framer-motion';
import { Edit } from 'lucide-react';

export interface StatCardProps {
    gradientFrom: string;
    gradientTo: string;
    title: string;
    value: string;
    description: string | React.ReactNode;
    icon: React.ComponentType<{ className?: string }>;
    onClick?: () => void;
    showEditOption?: boolean;
    onEdit?: (e: React.MouseEvent) => void;
}

export const StatCard = ({
    gradientFrom,
    gradientTo,
    title,
    value,
    description,
    icon: _Icon,
    onClick,
    showEditOption,
    onEdit
}: StatCardProps) => (
    <motion.div
        className={`relative rounded-xl overflow-hidden shadow-md bg-gradient-to-br ${gradientFrom} ${gradientTo} ${onClick ? 'cursor-pointer hover:shadow-lg transition-shadow' : ''}`}
        variants={{
            hidden: { opacity: 0, y: 20 },
            visible: { opacity: 1, y: 0, transition: { duration: 0.5 } }
        }}
        onClick={onClick}
        whileHover={onClick ? { scale: 1.03 } : undefined}
    >
        {/* Decorative elements */}
        <div className="absolute top-0 right-0 w-24 h-24 rounded-full opacity-20 bg-white translate-x-8 -translate-y-8" />
        <div className="absolute bottom-0 left-0 w-12 h-12 rounded-full opacity-20 bg-white -translate-x-4 translate-y-4" />

        {/* Card content */}
        <div className="p-6 relative z-10">
            <div className="flex justify-between items-start">
                <div className="flex-1">
                    <h3 className="text-white font-medium mb-1 truncate">{title}</h3>
                    <p className="text-2xl font-semibold text-white truncate">{value}</p>
                </div>
                {showEditOption && onEdit && (
                    <button
                        onClick={onEdit}
                        className="p-2 rounded-full bg-white/10 hover:bg-white/30 transition-colors"
                        aria-label="Edit"
                    >
                        <Edit size={16} className="text-white" />
                    </button>
                )}
            </div>
            <div className="text-white/70 text-sm mt-2 overflow-hidden">
                {description}
            </div>
        </div>
    </motion.div>
);

export default StatCard; 
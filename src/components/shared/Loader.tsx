/** @jsxImportSource react */
import { cn } from "../../lib/utils";
import { motion } from "framer-motion";

interface LoaderProps {
    size?: 'sm' | 'md' | 'lg';
    variant?: 'primary' | 'secondary';
    className?: string;
}

export function Loader({
    size = 'md',
    variant = 'primary',
    className
}: LoaderProps) {
    // Size configurations
    const sizeConfig = {
        sm: { container: 'h-8 w-8', arc: 'stroke-[2]' },
        md: { container: 'h-16 w-16', arc: 'stroke-[2.5]' },
        lg: { container: 'h-24 w-24', arc: 'stroke-[3]' }
    };

    // Color configurations
    const variantConfig = {
        primary: { start: '#00A7B5', mid: '#0078BE', end: '#5B4C9D' },
        secondary: { start: '#2D8DC6', mid: '#48D3A5', end: '#80D447' }
    };

    // Arc paths
    const arcs = [
        { radius: 40, startAngle: -60, endAngle: 240, delay: 0 },
        { radius: 32, startAngle: -30, endAngle: 210, delay: 0.2 },
        { radius: 24, startAngle: 0, endAngle: 180, delay: 0.4 },
        { radius: 16, startAngle: 30, endAngle: 150, delay: 0.6 }
    ];

    return (
        <div className={cn("flex flex-col items-center justify-center", className)}>
            <div className={cn("relative", sizeConfig[size].container)}>
                <svg className="absolute inset-0 h-full w-full" viewBox="0 0 100 100">
                    <defs>
                        <linearGradient id="arcGradient" gradientTransform="rotate(90)">
                            <stop offset="0%" stopColor={variantConfig[variant].start} />
                            <stop offset="50%" stopColor={variantConfig[variant].mid} />
                            <stop offset="100%" stopColor={variantConfig[variant].end} />
                        </linearGradient>
                    </defs>

                    {arcs.map((arc, index) => (
                        <motion.path
                            key={index}
                            d={`
                M ${50 + arc.radius * Math.cos((arc.startAngle * Math.PI) / 180)} 
                ${50 + arc.radius * Math.sin((arc.startAngle * Math.PI) / 180)}
                A ${arc.radius} ${arc.radius} 0 1 1 
                ${50 + arc.radius * Math.cos((arc.endAngle * Math.PI) / 180)} 
                ${50 + arc.radius * Math.sin((arc.endAngle * Math.PI) / 180)}
              `}
                            fill="none"
                            stroke="url(#arcGradient)"
                            className={sizeConfig[size].arc}
                            strokeLinecap="round"
                            initial={{ pathLength: 0, opacity: 0 }}
                            animate={{
                                pathLength: [0, 1],
                                opacity: [0.3, 1, 0.3],
                                rotate: 360
                            }}
                            transition={{
                                pathLength: { duration: 2, repeat: Infinity, delay: arc.delay },
                                opacity: { duration: 2, repeat: Infinity, delay: arc.delay },
                                rotate: { duration: 8, repeat: Infinity, ease: "linear" }
                            }}
                        />
                    ))}

                    <motion.circle
                        cx="50"
                        cy="50"
                        r="8"
                        fill="url(#arcGradient)"
                        animate={{ scale: [1, 1.1, 1], opacity: [0.7, 1, 0.7] }}
                        transition={{ duration: 2, repeat: Infinity }}
                    />
                </svg>
            </div>
        </div>
    );
}

interface SpinnerProps {
    size?: 'sm' | 'md' | 'lg';
    color?: 'primary' | 'secondary' | 'white' | 'gray';
    className?: string;
}

export function Spinner({ size = 'md', color = 'primary', className }: SpinnerProps) {
    const sizeClasses = {
        sm: 'w-4 h-4 border-2',
        md: 'w-8 h-8 border-3',
        lg: 'w-12 h-12 border-4'
    };

    const colorClasses = {
        primary: 'border-t-primary border-[#00A7B5]',
        secondary: 'border-t-[#48D3A5] border-[#2D8DC6]',
        white: 'border-t-white border-white/20',
        gray: 'border-t-gray-400 border-gray-200'
    };

    return (
        <div
            className={cn(
                'rounded-full animate-spin',
                sizeClasses[size],
                colorClasses[color],
                className
            )}
            style={{
                borderTopColor: 'transparent',
                borderRightColor: 'transparent',
                borderBottomColor: 'transparent'
            }}
        />
    );
} 
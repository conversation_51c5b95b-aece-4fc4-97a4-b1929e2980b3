"use client";

import { ReactNode, useEffect, useState, useTransition, useMemo } from 'react';
import { NextIntlClientProvider } from 'next-intl';
import LayoutWrapper from './LayoutWrapper';
import { AuthProvider } from '@/context/AuthContext';
import { ToastProvider } from '../ui/toast';
import { Toaster } from '../ui/toaster';
import { Toaster as SonnerToaster } from 'sonner';

interface ClientLayoutProps {
    children: ReactNode;
    locale: string;
}

export default function ClientLayout({ children, locale }: ClientLayoutProps) {
    const [messages, setMessages] = useState(null);
    const [_isPending, startTransition] = useTransition();

    // Memoize the locale to prevent unnecessary re-renders
    const memoizedLocale = useMemo(() => locale, [locale]);

    useEffect(() => {
        // Dynamically import the messages based on locale
        const loadMessages = async () => {
            startTransition(async () => {
                const messages = (await import(`../../../dictionary/${locale}.json`)).default;
                setMessages(messages);
            });
        };

        loadMessages();
    }, [locale]);

    // Don't render until messages are loaded
    if (!messages) {
        return null;
    }

    return (
        <NextIntlClientProvider locale={memoizedLocale} messages={messages}>
            <AuthProvider>
                <ToastProvider>
                    <LayoutWrapper locale={memoizedLocale}>
                        {children}
                    </LayoutWrapper>
                    <Toaster />
                    <SonnerToaster position="top-right" richColors />
                </ToastProvider>
            </AuthProvider>
        </NextIntlClientProvider>
    );
} 
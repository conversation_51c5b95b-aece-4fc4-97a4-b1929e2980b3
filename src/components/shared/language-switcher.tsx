'use client';

import { useLocale } from "next-intl";
import { useRouter, usePathname } from "@/i18n/navigation";
import { Button } from "@/components/ui/button";
import { Languages } from "lucide-react";
import { useState } from "react";
import { Spinner } from "./Loader";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface LanguageSwitcherProps {
    className?: string;
}

export function LanguageSwitcher({ className = "" }: LanguageSwitcherProps) {
    const locale = useLocale();
    const router = useRouter();
    const pathname = usePathname();
    const [isChanging, setIsChanging] = useState(false);

    const handleLanguageChange = async (newLocale: string) => {
        if (newLocale === locale || isChanging) return;

        setIsChanging(true);
        try {
            // Save preference in cookie
            document.cookie = `NEXT_LOCALE=${newLocale}; path=/; max-age=31536000`;

            // Update the locale
            await router.replace(pathname, { locale: newLocale });
        } catch (error) {
            console.error('Failed to change language:', error);
        } finally {
            setIsChanging(false);
        }
    };

    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <Button
                    variant="ghost"
                    size="icon"
                    disabled={isChanging}
                    className={`relative ${className}`}
                >
                    {isChanging ? (
                        <Spinner size="sm" color="primary" className="absolute inset-0 m-auto" />
                    ) : (
                        <Languages className="h-[1.2rem] w-[1.2rem]" />
                    )}
                    <span className="sr-only">Toggle language</span>
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="bg-white">
                <DropdownMenuItem
                    onClick={() => handleLanguageChange("en")}
                    className={locale === "en" ? "bg-accent" : ""}
                    disabled={isChanging}
                >
                    English
                </DropdownMenuItem>
                <DropdownMenuItem
                    onClick={() => handleLanguageChange("ar")}
                    className={locale === "ar" ? "bg-accent" : ""}
                    disabled={isChanging}
                >
                    العربية
                </DropdownMenuItem>
            </DropdownMenuContent>
        </DropdownMenu>
    );
} 
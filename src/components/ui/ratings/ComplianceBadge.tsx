"use client";

import { useTranslations } from "next-intl";
import { getComplianceStatusColor, getDefaultComplianceLabels } from "@/lib/ratings/assessmentCriteriaUtils";
import { AssessmentCriteria } from "@/types/assessmentCriteria";
import { CheckCircle, AlertCircle, XCircle, AlertTriangle } from "lucide-react";

interface ComplianceBadgeProps {
    status: string | undefined;
    size?: "sm" | "md" | "lg";
    locale?: string;
    assessmentCriteria?: AssessmentCriteria | null;
}

export function ComplianceBadge({ status, size = "md", locale = "en", assessmentCriteria, }: ComplianceBadgeProps) {
    const _t = useTranslations("Ratings");

    // Badge sizes
    const badgeSizes = {
        sm: "px-2 py-0.5 text-xs",
        md: "px-3 py-1 text-sm",
        lg: "px-4 py-1.5 text-base",
    };

    // Icon sizes
    const iconSizes = {
        sm: "h-3 w-3 mr-1",
        md: "h-4 w-4 mr-1.5",
        lg: "h-5 w-5 mr-2",
    };

    // Default to a fallback status if undefined
    const complianceStatus = status || 'Non-Compliant';

    // Get localized label for status
    const getStatusLabel = (status: string) => {
        // If we have assessment criteria with levels, use those labels
        if (assessmentCriteria?.type === 'compliance' && assessmentCriteria?.levels) {
            // Find a matching level based on the label or normalized status string
            const matchingLevel = Object.values(assessmentCriteria.levels).find(level => {
                if (!level.label) return false;

                // Handle both string and localized object for level.label
                if (typeof level.label === 'string') {
                    return level.label.toLowerCase().replace(/\s+/g, '') === status.toLowerCase().replace(/\s+/g, '');
                }

                if (typeof level.label === 'object') {
                    return (
                        (level.label.en && level.label.en.toLowerCase().replace(/\s+/g, '') === status.toLowerCase().replace(/\s+/g, '')) ||
                        (level.label.ar && level.label.ar.toLowerCase().replace(/\s+/g, '') === status.toLowerCase().replace(/\s+/g, ''))
                    );
                }

                return false;
            });

            if (!matchingLevel || !matchingLevel.label) return status;

            // Get localized label
            if (typeof matchingLevel.label === 'object') {
                return (locale in matchingLevel.label && typeof matchingLevel.label[locale as keyof typeof matchingLevel.label] === 'string')
                    ? matchingLevel.label[locale as keyof typeof matchingLevel.label]
                    : (matchingLevel.label.en || status);
            }

            return matchingLevel.label;
        }

        // Fallback to default labels
        return getDefaultComplianceLabels(status, locale);
    };

    // Get icon for status
    const getStatusIcon = (status: string) => {
        const normalizedStatus = status.toLowerCase();

        if (normalizedStatus.includes('compliant') || normalizedStatus.includes('fully')) {
            return <CheckCircle className={iconSizes[size]} />;
        } else if (normalizedStatus.includes('partial')) {
            return <AlertTriangle className={iconSizes[size]} />;
        } else if (normalizedStatus.includes('non') || normalizedStatus.includes('not') || normalizedStatus === 'initial') {
            return <XCircle className={iconSizes[size]} />;
        } else {
            return <AlertCircle className={iconSizes[size]} />;
        }
    };

    // Get color for status
    const statusColor = getComplianceStatusColor(complianceStatus);

    return (
        <div className={`inline-flex items-center rounded-full font-medium ${badgeSizes[size]} ${statusColor}`}>
            {getStatusIcon(complianceStatus)}
            <span>{getStatusLabel(complianceStatus)}</span>
        </div>
    );
}

export default ComplianceBadge; 
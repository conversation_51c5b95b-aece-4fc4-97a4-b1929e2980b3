"use client";

import React from 'react';
import { cn } from '@/lib/utils';

interface DynamicRatingIndicatorProps {
  value: number;
  maxValue?: number;
  size?: 'sm' | 'md' | 'lg';
  showValue?: boolean;
  className?: string;
  color?: string;
}

export function DynamicRatingIndicator({
  value,
  maxValue = 100,
  size = 'md',
  showValue = true,
  className,
  color = 'blue'
}: DynamicRatingIndicatorProps) {
  const percentage = Math.min((value / maxValue) * 100, 100);
  
  const sizeClasses = {
    sm: 'h-2 text-xs',
    md: 'h-3 text-sm',
    lg: 'h-4 text-base'
  };

  const colorClasses = {
    blue: 'bg-blue-500',
    green: 'bg-green-500',
    red: 'bg-red-500',
    yellow: 'bg-yellow-500',
    purple: 'bg-purple-500'
  };

  return (
    <div className={cn('w-full', className)}>
      <div className={cn('w-full bg-gray-200 rounded-full overflow-hidden', sizeClasses[size])}>
        <div
          className={cn(
            'h-full transition-all duration-300 rounded-full',
            colorClasses[color as keyof typeof colorClasses] || colorClasses.blue
          )}
          style={{ width: `${percentage}%` }}
        />
      </div>
      {showValue && (
        <div className={cn('mt-1 text-center font-medium', sizeClasses[size])}>
          {value.toFixed(1)}%
        </div>
      )}
    </div>
  );
}

export default DynamicRatingIndicator; 
"use client";

import { motion, AnimatePresence } from "framer-motion";
import { <PERSON>, Calculator, Target, Info, TrendingUp } from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { AssessmentCriteria } from "@/types";

interface DomainAverage {
  average: number;
  name: { en: string; ar: string };
  totalSpecifications: number;
  ratedSpecifications: number;
  domainId: string;
  weight?: number; // Domain weight from assessment criteria
}

interface WeightedComplianceModalProps {
  isOpen: boolean;
  onClose: () => void;
  domainAverages: Record<string, DomainAverage>;
  assessmentCriteria: AssessmentCriteria | null;
  locale: string;
}

export function WeightedComplianceModal({
  isOpen,
  onClose,
  domainAverages,
  assessmentCriteria,
  locale
}: WeightedComplianceModalProps) {
  const isRtl = locale === 'ar';

  // Calculate weighted compliance details
  const calculateWeightedDetails = () => {
    const domains = Object.entries(domainAverages);
    const calculations: Array<{
      domainId: string;
      name: string;
      compliance: number;
      weight: number;
      weightedValue: number;
      hasWeight: boolean;
    }> = [];

    let totalWeightedValue = 0;
    let totalWeight = 0;
    let domainsWithWeights = 0;

    domains.forEach(([domainId, domain]) => {
      // Use weight from domain averages if available, otherwise look up in assessment criteria
      const weight = domain.weight || assessmentCriteria?.domainWeights?.find(dw => dw.domainId === domain.domainId)?.weight || 0;
      const hasWeight = weight > 0;
      const weightedValue = hasWeight ? (domain.average * weight / 100) : 0;

      calculations.push({
        domainId,
        name: domain.name[locale as keyof typeof domain.name] || domain.name.en,
        compliance: domain.average,
        weight,
        weightedValue,
        hasWeight
      });

      if (hasWeight) {
        totalWeightedValue += weightedValue;
        totalWeight += weight;
        domainsWithWeights++;
      }
    });

    // If no weights are set, use equal weighting
    const finalWeightedCompliance = totalWeight > 0 ? 
      totalWeightedValue : 
      domains.reduce((sum, [_, domain]) => sum + domain.average, 0) / domains.length;

    return {
      calculations,
      totalWeightedValue,
      totalWeight,
      domainsWithWeights,
      finalWeightedCompliance,
      hasWeights: totalWeight > 0
    };
  };

  const details = calculateWeightedDetails();

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[9999]"
            onClick={handleBackdropClick}
          />

          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            transition={{ 
              duration: 0.3,
              type: "spring",
              stiffness: 300,
              damping: 30
            }}
            className="fixed inset-0 z-[10000] flex items-center justify-center p-4"
            onClick={handleBackdropClick}
          >
            <div 
              className="w-full max-w-4xl max-h-[90vh] overflow-y-auto bg-white rounded-2xl shadow-2xl"
              onClick={(e) => e.stopPropagation()}
            >
              {/* Header */}
              <div className="relative bg-gradient-to-r from-[#003874] via-[#2D8DC6] to-[#48D3A5] px-8 py-6 rounded-t-2xl">
                {/* Close button */}
                <button
                  onClick={onClose}
                  className={`absolute top-4 ${isRtl ? 'left-4' : 'right-4'} p-2 text-white/80 hover:text-white hover:bg-white/20 rounded-full transition-all duration-200 z-10`}
                >
                  <X className="h-5 w-5" />
                </button>

                <div className="relative z-10 space-y-3">
                  <div className={`flex items-center gap-4 ${isRtl ? 'flex-row-reverse' : ''}`}>
                    <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                      <Calculator className="h-8 w-8 text-white" />
                    </div>
                    <div className={isRtl ? 'text-right' : ''}>
                      <h2 className={`text-2xl font-bold text-white mb-2 ${isRtl ? 'font-arabic' : ''}`}>
                        {isRtl ? 'تفاصيل حساب الامتثال المرجح' : 'Weighted Compliance Calculation'}
                      </h2>
                      <p className={`text-white/90 text-base ${isRtl ? 'font-arabic' : ''}`}>
                        {isRtl ? 'كيف يتم حساب النسبة المرجحة للامتثال' : 'How the weighted compliance percentage is calculated'}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Content */}
              <div className="p-8 space-y-8">
                {/* Summary Card */}
                <Card className="border-2 border-emerald-200 bg-gradient-to-br from-emerald-50 to-green-50">
                  <CardHeader className="pb-4">
                    <CardTitle className={`text-xl font-bold text-emerald-900 flex items-center gap-3 ${isRtl ? 'flex-row-reverse font-arabic' : ''}`}>
                      <Target className="h-6 w-6" />
                      {isRtl ? 'النتيجة النهائية' : 'Final Result'}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className={`grid grid-cols-1 md:grid-cols-3 gap-6 ${isRtl ? 'text-right' : ''}`}>
                      <div className="text-center">
                        <p className={`text-emerald-600 text-sm font-medium uppercase tracking-wide mb-2 ${isRtl ? 'font-arabic' : ''}`}>
                          {isRtl ? 'الامتثال المرجح' : 'Weighted Compliance'}
                        </p>
                        <p className="text-4xl font-bold text-emerald-900">
                          {details.finalWeightedCompliance.toFixed(1)}%
                        </p>
                      </div>
                      <div className="text-center">
                        <p className={`text-emerald-600 text-sm font-medium uppercase tracking-wide mb-2 ${isRtl ? 'font-arabic' : ''}`}>
                          {isRtl ? 'إجمالي الأوزان' : 'Total Weights'}
                        </p>
                        <p className="text-4xl font-bold text-emerald-900">
                          {details.totalWeight}%
                        </p>
                      </div>
                      <div className="text-center">
                        <p className={`text-emerald-600 text-sm font-medium uppercase tracking-wide mb-2 ${isRtl ? 'font-arabic' : ''}`}>
                          {isRtl ? 'المجالات المرجحة' : 'Weighted Domains'}
                        </p>
                        <p className="text-4xl font-bold text-emerald-900">
                          {details.domainsWithWeights}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Calculation Method */}
                <Card className="border border-blue-200 bg-blue-50/50">
                  <CardHeader className="pb-4">
                    <CardTitle className={`text-lg font-bold text-blue-900 flex items-center gap-3 ${isRtl ? 'flex-row-reverse font-arabic' : ''}`}>
                      <Info className="h-5 w-5" />
                      {isRtl ? 'طريقة الحساب' : 'Calculation Method'}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className={`space-y-4 ${isRtl ? 'text-right font-arabic' : ''}`}>
                      {details.hasWeights ? (
                        <>
                          <p className="text-blue-800 font-medium">
                            {isRtl ? 
                              'يتم حساب الامتثال المرجح باستخدام الصيغة التالية:' : 
                              'Weighted compliance is calculated using the following formula:'
                            }
                          </p>
                          <div className="bg-white p-4 rounded-lg border border-blue-200 font-mono text-center">
                            <p className="text-blue-900 font-bold">
                              {isRtl ? 
                                'الامتثال المرجح = Σ (امتثال المجال × وزن المجال ÷ 100)' :
                                'Weighted Compliance = Σ (Domain Compliance × Domain Weight ÷ 100)'
                              }
                            </p>
                          </div>
                          <p className="text-blue-700 text-sm">
                            {isRtl ? 
                              'حيث يتم ضرب نسبة امتثال كل مجال في وزنه المحدد، ثم جمع النتائج للحصول على النسبة المرجحة النهائية.' :
                              'Where each domain\'s compliance percentage is multiplied by its assigned weight, then summed to get the final weighted percentage.'
                            }
                          </p>
                        </>
                      ) : (
                        <>
                          <p className="text-blue-800 font-medium">
                            {isRtl ? 
                              'لا توجد أوزان محددة للمجالات، لذا يتم استخدام المتوسط البسيط:' : 
                              'No domain weights are set, so simple average is used:'
                            }
                          </p>
                          <div className="bg-white p-4 rounded-lg border border-blue-200 font-mono text-center">
                            <p className="text-blue-900 font-bold">
                              {isRtl ? 
                                'الامتثال = مجموع امتثال المجالات ÷ عدد المجالات' :
                                'Compliance = Sum of Domain Compliance ÷ Number of Domains'
                              }
                            </p>
                          </div>
                        </>
                      )}
                    </div>
                  </CardContent>
                </Card>

                {/* Domain Breakdown */}
                <Card className="border border-gray-200">
                  <CardHeader className="pb-4">
                    <CardTitle className={`text-lg font-bold text-gray-900 flex items-center gap-3 ${isRtl ? 'flex-row-reverse font-arabic' : ''}`}>
                      <TrendingUp className="h-5 w-5" />
                      {isRtl ? 'تفصيل المجالات' : 'Domain Breakdown'}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {details.calculations.map((calc, index) => (
                        <div key={calc.domainId} 
                             className={`p-4 rounded-lg border transition-all duration-200 ${
                               calc.hasWeight ? 
                                 'border-emerald-200 bg-emerald-50/50' : 
                                 'border-gray-200 bg-gray-50/50'
                             }`}>
                          <div className={`flex items-center justify-between ${isRtl ? 'flex-row-reverse' : ''}`}>
                            <div className={`flex-1 ${isRtl ? 'text-right' : ''}`}>
                              <h4 className={`font-bold text-gray-900 mb-1 ${isRtl ? 'font-arabic' : ''}`}>
                                {calc.name}
                              </h4>
                              <div className={`flex items-center gap-4 text-sm ${isRtl ? 'flex-row-reverse' : ''}`}>
                                <span className="text-gray-600">
                                  {isRtl ? 'الامتثال:' : 'Compliance:'} <span className="font-semibold">{calc.compliance.toFixed(1)}%</span>
                                </span>
                                <span className="text-gray-600">
                                  {isRtl ? 'الوزن:' : 'Weight:'} <span className="font-semibold">{calc.weight}%</span>
                                </span>
                                {calc.hasWeight && (
                                  <span className="text-emerald-600">
                                    {isRtl ? 'القيمة المرجحة:' : 'Weighted Value:'} <span className="font-bold">{calc.weightedValue.toFixed(2)}%</span>
                                  </span>
                                )}
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              <Badge className={`${calc.hasWeight ? 
                                'bg-emerald-100 text-emerald-800 border-emerald-200' : 
                                'bg-gray-100 text-gray-800 border-gray-200'} text-xs font-bold`}>
                                {calc.hasWeight ? 
                                  (isRtl ? 'مرجح' : 'Weighted') : 
                                  (isRtl ? 'غير مرجح' : 'Unweighted')
                                }
                              </Badge>
                            </div>
                          </div>
                          
                          {/* Calculation visualization */}
                          {calc.hasWeight && (
                            <div className={`mt-3 p-3 bg-white rounded border border-emerald-200 ${isRtl ? 'text-right' : ''}`}>
                              <p className="text-xs text-emerald-700 font-mono">
                                {calc.compliance.toFixed(1)}% × {calc.weight}% ÷ 100 = {calc.weightedValue.toFixed(2)}%
                              </p>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Footer */}
              <div className="bg-gray-50 px-8 py-6 border-t border-gray-200 rounded-b-2xl">
                <div className={`flex justify-end ${isRtl ? 'flex-row-reverse' : ''}`}>
                  <Button
                    onClick={onClose}
                    className="bg-gradient-to-r from-[#003874] via-[#2D8DC6] to-[#48D3A5] hover:opacity-90 transition-all duration-200 text-white px-8"
                  >
                    {isRtl ? 'إغلاق' : 'Close'}
                  </Button>
                </div>
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
} 
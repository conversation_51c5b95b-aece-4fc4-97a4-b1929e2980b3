"use client";

import { motion } from "framer-motion";
import { useTranslations } from "next-intl";
import { FRAMER_TRANSITIONS } from "@/lib/design/transitions";
import { ComplianceBadge } from "@/components/ui/ratings/ComplianceBadge";
import { AssessmentCriteria } from "@/types";
import { calculateDomainRating } from "@/lib/utils/assessment-rating";
import { mapPercentageToComplianceStatus } from "@/lib/ratings/assessmentRatingUtils";

interface Specification {
    id: string;
    name?: { en: string; ar: string };
    description?: { en: string; ar: string };
    complianceStatus?: string;
    specifications?: Array<Specification>;
    [key: string]: unknown;
}

interface DomainLikeSpecification {
    id: string;
    name: { en: string; ar: string } | string;
    specifications?: Array<{
        id: string;
        currentRating?: number;
        maturityLevel?: number;
        percentageValue?: number;
        complianceStatus?: string;
        [key: string]: unknown;
    }>;
    [key: string]: unknown;
}

interface ComplianceAssessmentCardProps {
    specification: Specification;
    locale: string;
    assessmentCriteria?: AssessmentCriteria | null;
    isDomain?: boolean;
}

// Get color based on compliance value
const getComplianceColor = (value: number): string => {
    if (value === 0) return "bg-red-500"; // Red for 0
    if (value >= 50 && value < 70) return "bg-yellow-500"; // Yellow for 50-70
    if (value >= 70 && value < 90) return "bg-green-400"; // Light green for 70-90
    if (value >= 90) return "bg-green-600"; // Green for 90-100
    return "bg-gray-500"; // Default gray
};

// Get compliance percentage based on status and assessment criteria
const getCompliancePercentage = (status: string, assessmentCriteria?: AssessmentCriteria | null): number => {
    if (!assessmentCriteria?.levels) return 0;
    
    // Find the matching level
    const matchingLevel = Object.values(assessmentCriteria.levels).find(level => {
        if (typeof level.label === 'string') {
            return level.label.toLowerCase() === status.toLowerCase();
        } else if (typeof level.label === 'object') {
            return level.label.en?.toLowerCase() === status.toLowerCase() || 
                   level.label.ar?.toLowerCase() === status.toLowerCase();
        }
        return false;
    });
    
    return matchingLevel ? matchingLevel.value : 0;
};

export function ComplianceAssessmentCard({
    specification,
    locale,
    assessmentCriteria,
    isDomain = false
}: ComplianceAssessmentCardProps) {
    const t = useTranslations("SpecificationDetails");

    let complianceStatus = specification.complianceStatus;
    let compliancePercentage = 0;
    let totalSpecs = 0;
    let hasRatings = false;

    // For domains with specifications, use new calculation approach
    if (isDomain && specification.specifications && specification.specifications.length > 0 && assessmentCriteria) {
        // Count valid specs with compliance status
        const validSpecs = specification.specifications.filter(spec => spec.complianceStatus);
        totalSpecs = validSpecs.length;
        hasRatings = totalSpecs > 0;

        if (hasRatings) {
            // Step 1: Calculate the numeric rating using the improved domain rating logic
            const specWithName = {
                ...specification,
                name: specification.name || { en: '', ar: '' }
            };
            const avgRating = calculateDomainRating(specWithName as DomainLikeSpecification, assessmentCriteria);

            // Step 2: Map the numeric rating back to a compliance status
            const mappedStatus = mapPercentageToComplianceStatus(avgRating, assessmentCriteria);

            if (mappedStatus) {
                complianceStatus = typeof mappedStatus.label === 'object'
                    ? (locale === 'ar' && mappedStatus.label.ar ? mappedStatus.label.ar : mappedStatus.label.en)
                    : mappedStatus.label;

                // Store the numeric percentage for display
                compliancePercentage = avgRating;
            }
        }
    } else {
        // For individual specifications
        hasRatings = !!complianceStatus;
        if (hasRatings && complianceStatus) {
            compliancePercentage = getCompliancePercentage(complianceStatus, assessmentCriteria);
        }
    }

    // Fallback if no status is determined but we have assessment criteria
    if (!hasRatings && assessmentCriteria?.type === 'compliance' && assessmentCriteria.levels) {
        // Use the lowest compliance level as default
        const sortedLevels = Object.values(assessmentCriteria.levels).sort((a, b) => a.value - b.value);
        if (sortedLevels.length > 0) {
            const lowestLevel = sortedLevels[0];
            complianceStatus = typeof lowestLevel.label === 'object'
                ? (locale === 'ar' && lowestLevel.label.ar ? lowestLevel.label.ar : lowestLevel.label.en)
                : lowestLevel.label;
            compliancePercentage = lowestLevel.value;
        } else {
            complianceStatus = 'Non-Compliant';
            compliancePercentage = 0;
        }
    }

    // Get the color for the current compliance level
    const complianceColor = getComplianceColor(compliancePercentage);

    return (
        <motion.div
            initial="hidden"
            animate="visible"
            variants={{
                hidden: { opacity: 0, scale: 0.9 },
                visible: { opacity: 1, scale: 1 }
            }}
            transition={FRAMER_TRANSITIONS.springSoft}
            className="lg:w-1/3 w-full bg-white/10 backdrop-blur-md rounded-xl overflow-hidden text-white shadow-xl border border-white/20"
        >
            <div className="p-6 text-center">
                <h3 className="text-lg font-medium mb-6">
                    {isDomain ? t("domainComplianceAssessment") : t("complianceAssessment")}
                </h3>

                {!hasRatings && isDomain ? (
                    <div className="bg-white/20 backdrop-blur-sm rounded-lg p-6 text-center">
                        <p className="text-white/90">{locale === 'ar' ? 'لم يبدأ التقييم' : 'Assessment Not Started'}</p>
                    </div>
                ) : (
                    <div className="flex flex-col items-center justify-center gap-4">
                        {/* Compliance Status Badge */}
                        <div className="flex items-center justify-center">
                            <ComplianceBadge
                                status={complianceStatus}
                                size="lg"
                                locale={locale}
                                assessmentCriteria={assessmentCriteria}
                            />
                        </div>

                        {/* Percentage Display */}
                        <div className="text-center">
                            <div className="text-3xl font-bold text-white mb-2">
                                {Math.round(compliancePercentage)}%
                            </div>
                            <div className="text-sm text-white/80">
                                {locale === 'ar' ? 'نسبة الامتثال' : 'Compliance Percentage'}
                            </div>
                        </div>

                        {/* Progress Bar */}
                        <div className="w-full max-w-[200px]">
                            <div className="w-full h-3 bg-white/20 rounded-full overflow-hidden">
                                <div
                                    className={`h-full ${complianceColor} rounded-full transition-all duration-500`}
                                    style={{ width: `${compliancePercentage}%` }}
                                ></div>
                            </div>
                            <div className="flex justify-between text-xs text-white/60 mt-1">
                                <span>0%</span>
                                <span>100%</span>
                            </div>
                        </div>

                        {/* Description */}
                        <div className="text-sm text-white/80 max-w-[280px] mx-auto mt-2">
                            {assessmentCriteria?.type === 'compliance' && assessmentCriteria.levels ? (
                                (() => {
                                    const matchingLevel = Object.values(assessmentCriteria.levels).find(
                                        level => {
                                            if (typeof level.label === 'object') {
                                                return level.label.en === complianceStatus || level.label.ar === complianceStatus;
                                            }
                                            return level.label === complianceStatus;
                                        }
                                    );
                                    return matchingLevel?.description && typeof matchingLevel.description === 'object' 
                                        ? (matchingLevel.description[locale as keyof typeof matchingLevel.description] || 
                                           (matchingLevel.description && 'en' in matchingLevel.description ? matchingLevel.description.en : '')) 
                                        : (matchingLevel?.description as string) || t(`complianceDescriptions.${complianceStatus}`);
                                })()
                            ) : (
                                t(`complianceDescriptions.${complianceStatus}`)
                            )}
                        </div>

                        {/* Available Compliance Levels */}
                        {assessmentCriteria?.levels && (
                            <div className="mt-4 w-full">
                                <div className="text-xs text-white/70 mb-2">
                                    {locale === 'ar' ? 'مستويات الامتثال المتاحة' : 'Available Compliance Levels'}
                                </div>
                                <div className="space-y-1">
                                    {Object.values(assessmentCriteria.levels)
                                        .sort((a, b) => b.value - a.value)
                                        .map((level, index) => {
                                            const levelLabel = typeof level.label === 'object'
                                                ? (level.label[locale as keyof typeof level.label] || level.label.en)
                                                : level.label;
                                            const isCurrentLevel = levelLabel === complianceStatus;
                                            const levelColor = getComplianceColor(level.value);
                                            
                                            return (
                                                <div 
                                                    key={index} 
                                                    className={`flex items-center justify-between text-xs p-2 rounded ${
                                                        isCurrentLevel ? 'bg-white/20' : 'bg-white/10'
                                                    }`}
                                                >
                                                    <div className="flex items-center gap-2">
                                                        <div className={`w-3 h-3 rounded-full ${levelColor}`}></div>
                                                        <span className={isCurrentLevel ? 'font-semibold' : ''}>{levelLabel}</span>
                                                    </div>
                                                    <span className={`${isCurrentLevel ? 'font-semibold' : 'text-white/70'}`}>
                                                        {level.value}%
                                                    </span>
                                                </div>
                                            );
                                        })}
                                </div>
                            </div>
                        )}

                        {isDomain && specification.specifications && totalSpecs > 0 && (
                            <div className="text-xs text-white/70 mt-2">
                                {t("basedOn")} {totalSpecs} {t("specifications")}
                            </div>
                        )}
                    </div>
                )}
            </div>
        </motion.div>
    );
}

export default ComplianceAssessmentCard; 
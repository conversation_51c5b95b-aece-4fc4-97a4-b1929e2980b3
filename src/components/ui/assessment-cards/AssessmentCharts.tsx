"use client";

import { motion } from "framer-motion";
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  RadarChart,
  Radar,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  LineChart,
  Line,
  Area,
  AreaChart
} from "recharts";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { AssessmentCriteria } from "@/types";

interface DomainAverage {
  average: number;
  name: { en: string; ar: string };
  totalSpecifications: number;
  ratedSpecifications: number;
  domainId: string;
  weight?: number; // Domain weight from assessment criteria
}

interface AssessmentChartsProps {
  domainAverages: Record<string, DomainAverage>;
  assessmentCriteria: AssessmentCriteria | null;
  locale: string;
  selectedDomains?: string[]; // Optional filter for specific domains
}

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.5 }
  }
};

// Color palette for charts
const COLORS = ['#003874', '#2D8DC6', '#48D3A5', '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'];

export function AssessmentCharts({ 
  domainAverages, 
  assessmentCriteria, 
  locale,
  selectedDomains = [] 
}: AssessmentChartsProps) {
  const isRtl = locale === 'ar';

  // Prepare data for charts with weighted compliance
  const prepareChartData = () => {
    const domains = Object.entries(domainAverages);
    
    // Filter domains if selectedDomains is provided and not empty
    const filteredDomains = selectedDomains.length > 0 
      ? domains.filter(([domainId]) => selectedDomains.includes(domainId))
      : domains;
    
    return filteredDomains.map(([domainId, domain], index) => {
      // Use weight from domain averages if available, otherwise look up in assessment criteria
      const weight = domain.weight || assessmentCriteria?.domainWeights?.find(dw => dw.domainId === domain.domainId)?.weight || 0;
      const weightedCompliance = weight > 0 ? domain.average * (weight / 100) : domain.average;
      
      return {
        id: domainId,
        name: domain.name[locale as keyof typeof domain.name] || domain.name.en,
        shortName: domainId, // Use domain ID for display in charts
        compliance: Math.round(domain.average * 10) / 10,
        weightedCompliance: Math.round(weightedCompliance * 10) / 10,
        weight: weight,
        totalSpecs: domain.totalSpecifications,
        ratedSpecs: domain.ratedSpecifications,
        completionRate: domain.totalSpecifications > 0 ? 
          Math.round((domain.ratedSpecifications / domain.totalSpecifications) * 100) : 0,
        color: COLORS[index % COLORS.length]
      };
    });
  };

  const chartData = prepareChartData();

  // Custom tooltip for charts
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      // Find the domain data to show full name
      const domainData = chartData.find(d => d.shortName === label || d.name === label);
      const displayName = domainData?.name || label;
      
      return (
        <div className="bg-white p-4 border border-gray-200 rounded-lg shadow-lg max-w-xs">
          <p className={`font-semibold text-gray-900 mb-2 ${isRtl ? 'text-right font-arabic' : ''}`}>
            {displayName}
          </p>
          <p className={`text-xs text-gray-600 mb-2 ${isRtl ? 'text-right font-arabic' : ''}`}>
            {isRtl ? `معرف المجال: ${domainData?.id || label}` : `Domain ID: ${domainData?.id || label}`}
          </p>
          {payload.map((entry: any, index: number) => (
            <p key={index} className={`text-sm ${isRtl ? 'text-right font-arabic' : ''}`} 
               style={{ color: entry.color }}>
              {`${entry.name}: ${entry.value}${entry.name.includes('Rate') || entry.name.includes('Compliance') || entry.name.includes('Weight') ? '%' : ''}`}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  // Compliance distribution using real assessment criteria levels
  const complianceDistribution = () => {
    if (!assessmentCriteria?.levels) {
      // Fallback to default ranges if no assessment criteria
      const ranges = [
        { name: isRtl ? 'ممتاز (80-100%)' : 'Excellent (80-100%)', min: 80, max: 100, color: '#10B981' },
        { name: isRtl ? 'جيد (60-79%)' : 'Good (60-79%)', min: 60, max: 79, color: '#3B82F6' },
        { name: isRtl ? 'متوسط (40-59%)' : 'Average (40-59%)', min: 40, max: 59, color: '#F59E0B' },
        { name: isRtl ? 'ضعيف (0-39%)' : 'Poor (0-39%)', min: 0, max: 39, color: '#EF4444' }
      ];

      return ranges.map(range => ({
        name: range.name,
        value: chartData.filter(domain => 
          domain.weightedCompliance >= range.min && domain.weightedCompliance <= range.max
        ).length,
        color: range.color
      })).filter(item => item.value > 0);
    }

    // Use real assessment criteria levels
    const levels = Object.values(assessmentCriteria.levels).sort((a, b) => b.value - a.value);
    const colorMap = ['#10B981', '#3B82F6', '#F59E0B', '#EF4444', '#8B5CF6', '#EC4899', '#14B8A6', '#F97316'];
    
    return levels.map((level, index) => {
      const label = typeof level.label === 'object' 
        ? (level.label[locale] || level.label.en)
        : level.label;
      
      // Find the next lower level to determine range
      const nextLevel = levels[index + 1];
      const minValue = nextLevel ? nextLevel.value + 0.1 : 0;
      const maxValue = level.value;
      
      const domainsInLevel = chartData.filter(domain => 
        domain.weightedCompliance >= minValue && domain.weightedCompliance <= maxValue
      ).length;

      return {
        name: `${label} (${minValue.toFixed(0)}-${maxValue}%)`,
        value: domainsInLevel,
        color: colorMap[index % colorMap.length],
        level: level.value
      };
    }).filter(item => item.value > 0);
  };

  const distributionData = complianceDistribution();

  return (
    <div className="space-y-8">
      {/* Domain Compliance Bar Chart */}
      <motion.div variants={itemVariants}>
        <Card className="border-0 shadow-xl bg-white/95 backdrop-blur-sm">
          <CardHeader className="pb-4">
            <CardTitle className={`text-xl font-bold text-gray-900 ${isRtl ? 'text-right font-arabic' : ''}`}>
              {isRtl ? 'الامتثال المرجح للمجالات' : 'Domain Weighted Compliance'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-96">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 80 }}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                  <XAxis 
                    dataKey="shortName" 
                    tick={{ fontSize: 12, fill: '#6B7280', fontWeight: 'bold' }}
                    angle={0}
                    textAnchor="middle"
                    height={60}
                    interval={0}
                  />
                  <YAxis 
                    tick={{ fontSize: 12, fill: '#6B7280' }}
                    domain={[0, 100]}
                    label={{ 
                      value: isRtl ? 'نسبة الامتثال المرجح (%)' : 'Weighted Compliance (%)', 
                      angle: -90, 
                      position: 'insideLeft',
                      style: { textAnchor: 'middle' }
                    }}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Bar 
                    dataKey="compliance" 
                    fill="#2D8DC6"
                    radius={[4, 4, 0, 0]}
                    name={isRtl ? 'الامتثال الأساسي' : 'Base Compliance'}
                    fillOpacity={0.7}
                  />
                  <Bar 
                    dataKey="weightedCompliance" 
                    fill="#003874"
                    radius={[4, 4, 0, 0]}
                    name={isRtl ? 'الامتثال المرجح' : 'Weighted Compliance'}
                  />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Compliance Distribution Pie Chart */}
        <motion.div variants={itemVariants}>
          <Card className="border-0 shadow-xl bg-white/95 backdrop-blur-sm">
            <CardHeader className="pb-4">
              <CardTitle className={`text-xl font-bold text-gray-900 ${isRtl ? 'text-right font-arabic' : ''}`}>
                {isRtl ? 'توزيع مستويات الامتثال' : 'Compliance Distribution'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={distributionData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, value, percent }) => 
                        `${name}: ${value} (${(percent * 100).toFixed(0)}%)`
                      }
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {distributionData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip content={<CustomTooltip />} />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Domain Impact Analysis */}
        <motion.div variants={itemVariants}>
          <Card className="border-0 shadow-xl bg-white/95 backdrop-blur-sm">
            <CardHeader className="pb-4">
              <CardTitle className={`text-xl font-bold text-gray-900 ${isRtl ? 'text-right font-arabic' : ''}`}>
                {isRtl ? 'تحليل تأثير المجالات' : 'Domain Impact Analysis'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 60 }}>
                    <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                    <XAxis 
                      dataKey="shortName" 
                      tick={{ fontSize: 11, fill: '#6B7280', fontWeight: 'bold' }}
                      angle={0}
                      textAnchor="middle"
                      height={50}
                      interval={0}
                    />
                    <YAxis 
                      tick={{ fontSize: 12, fill: '#6B7280' }}
                      domain={[0, 100]}
                      label={{ 
                        value: isRtl ? 'النسبة المئوية (%)' : 'Percentage (%)', 
                        angle: -90, 
                        position: 'insideLeft',
                        style: { textAnchor: 'middle' }
                      }}
                    />
                    <Tooltip content={<CustomTooltip />} />
                    <Area 
                      type="monotone" 
                      dataKey="weight" 
                      stroke="#48D3A5" 
                      fill="#48D3A5" 
                      fillOpacity={0.4}
                      name={isRtl ? 'وزن المجال' : 'Domain Weight'}
                    />
                    <Area 
                      type="monotone" 
                      dataKey="weightedCompliance" 
                      stroke="#003874" 
                      fill="#003874" 
                      fillOpacity={0.6}
                      name={isRtl ? 'الامتثال المرجح' : 'Weighted Compliance'}
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Performance Trends Analysis */}
      <motion.div variants={itemVariants}>
        <Card className="border-0 shadow-xl bg-white/95 backdrop-blur-sm">
          <CardHeader className="pb-4">
            <CardTitle className={`text-xl font-bold text-gray-900 ${isRtl ? 'text-right font-arabic' : ''}`}>
              {isRtl ? 'تحليل اتجاهات الأداء' : 'Performance Trends Analysis'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-96">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 80 }}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                  <XAxis 
                    dataKey="shortName" 
                    tick={{ fontSize: 11, fill: '#6B7280', fontWeight: 'bold' }}
                    angle={0}
                    textAnchor="middle"
                    height={60}
                    interval={0}
                  />
                  <YAxis 
                    tick={{ fontSize: 12, fill: '#6B7280' }}
                    domain={[0, 100]}
                    label={{ 
                      value: isRtl ? 'النسبة المئوية (%)' : 'Percentage (%)', 
                      angle: -90, 
                      position: 'insideLeft',
                      style: { textAnchor: 'middle' }
                    }}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Line 
                    type="monotone" 
                    dataKey="completionRate" 
                    stroke="#2D8DC6" 
                    strokeWidth={3}
                    dot={{ fill: '#2D8DC6', strokeWidth: 2, r: 6 }}
                    name={isRtl ? 'معدل الإنجاز' : 'Completion Rate'}
                    strokeDasharray="5 5"
                  />
                  <Line 
                    type="monotone" 
                    dataKey="compliance" 
                    stroke="#48D3A5" 
                    strokeWidth={2}
                    dot={{ fill: '#48D3A5', strokeWidth: 2, r: 4 }}
                    name={isRtl ? 'الامتثال الأساسي' : 'Base Compliance'}
                    strokeOpacity={0.7}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="weightedCompliance" 
                    stroke="#003874" 
                    strokeWidth={4}
                    dot={{ fill: '#003874', strokeWidth: 3, r: 8 }}
                    name={isRtl ? 'الامتثال المرجح' : 'Weighted Compliance'}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Enhanced Radar Chart for Domain Performance */}
      {chartData.length <= 8 && (
        <motion.div variants={itemVariants}>
          <Card className="border-0 shadow-xl bg-white/95 backdrop-blur-sm">
            <CardHeader className="pb-4">
              <CardTitle className={`text-xl font-bold text-gray-900 ${isRtl ? 'text-right font-arabic' : ''}`}>
                {isRtl ? 'رادار الأداء الشامل للمجالات' : 'Comprehensive Domain Performance Radar'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[500px]">
                <ResponsiveContainer width="100%" height="100%">
                  <RadarChart data={chartData} margin={{ top: 40, right: 100, bottom: 40, left: 100 }}>
                    <PolarGrid stroke="#e5e7eb" strokeWidth={1} />
                    <PolarAngleAxis 
                      dataKey="shortName" 
                      tick={{ fontSize: 12, fill: '#374151', fontWeight: 'bold' }}
                      className="font-bold"
                    />
                    <PolarRadiusAxis 
                      angle={90} 
                      domain={[0, 100]} 
                      tick={{ fontSize: 9, fill: '#6B7280' }}
                      tickCount={6}
                    />
                    <Radar
                      name={isRtl ? 'الامتثال الأساسي' : 'Base Compliance'}
                      dataKey="compliance"
                      stroke="#48D3A5"
                      fill="#48D3A5"
                      fillOpacity={0.2}
                      strokeWidth={2}
                      strokeDasharray="5 5"
                    />
                    <Radar
                      name={isRtl ? 'الامتثال المرجح' : 'Weighted Compliance'}
                      dataKey="weightedCompliance"
                      stroke="#003874"
                      fill="#003874"
                      fillOpacity={0.4}
                      strokeWidth={3}
                    />
                    <Radar
                      name={isRtl ? 'معدل الإنجاز' : 'Completion Rate'}
                      dataKey="completionRate"
                      stroke="#2D8DC6"
                      fill="#2D8DC6"
                      fillOpacity={0.1}
                      strokeWidth={2}
                      strokeDasharray="3 3"
                    />
                    <Tooltip content={<CustomTooltip />} />
                  </RadarChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}
    </div>
  );
} 
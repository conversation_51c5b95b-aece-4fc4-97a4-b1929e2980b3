"use client";

import { motion } from "framer-motion";
import { useState } from "react";
import { cn } from "@/lib/utils";

interface TabItem {
  id: string;
  label: string;
  content: React.ReactNode;
}

interface ProfessionalTabsProps {
  tabs: TabItem[];
  defaultTab?: string;
  onTabChange?: (tabId: string) => void;
  className?: string;
  isRtl?: boolean;
}

export function ProfessionalTabs({ 
  tabs, 
  defaultTab, 
  onTabChange, 
  className,
  isRtl = false 
}: ProfessionalTabsProps) {
  const [activeTab, setActiveTab] = useState(defaultTab || tabs[0]?.id);

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
    onTabChange?.(tabId);
  };

  const activeTabIndex = tabs.findIndex(tab => tab.id === activeTab);
  const activeTabItem = tabs.find(tab => tab.id === activeTab);

  return (
    <div className={cn("w-full", className)}>
      {/* Enterprise Tab Navigation */}
      <div className="relative mb-12">
        {/* Main Tab Container */}
        <div className="relative bg-white border border-gray-200/60 rounded-xl shadow-lg overflow-hidden backdrop-blur-sm">
          {/* Header Section */}
          <div className="px-6 py-4 bg-gradient-to-r from-gray-50 to-white border-b border-gray-200/50">
            <div className={`flex items-center justify-between ${isRtl ? 'flex-row-reverse' : ''}`}>
              <h2 className={`text-lg font-semibold text-gray-900 ${isRtl ? 'font-arabic' : ''}`}>
                {isRtl ? 'أقسام التقييم' : 'Assessment Sections'}
              </h2>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-emerald-500 rounded-full"></div>
                <span className="text-xs text-gray-500 font-medium">
                  {activeTabIndex + 1} / {tabs.length}
                </span>
              </div>
            </div>
          </div>

          {/* Tab Navigation */}
          <div className={`relative flex ${isRtl ? 'flex-row-reverse' : ''}`}>
            {/* Animated Background Indicator */}
            <motion.div
              className="absolute top-0 bottom-0 bg-gradient-to-r from-[#003874] to-[#2D8DC6] shadow-md"
              initial={false}
              animate={{
                [isRtl ? 'right' : 'left']: `${activeTabIndex * (100 / tabs.length)}%`,
                width: `${100 / tabs.length}%`,
              }}
              transition={{
                type: "spring",
                stiffness: 400,
                damping: 35,
              }}
            />
            
            {/* Tab Buttons */}
            {tabs.map((tab, index) => (
              <button
                key={tab.id}
                onClick={() => handleTabChange(tab.id)}
                className={cn(
                  "relative z-10 flex-1 px-8 py-6 text-base font-medium transition-all duration-300",
                  "focus:outline-none focus:ring-2 focus:ring-[#003874]/30 focus:ring-inset",
                  "group",
                  activeTab === tab.id
                    ? "text-white"
                    : "text-gray-600 hover:text-gray-900 hover:bg-gray-50/80",
                  isRtl ? 'font-arabic' : ''
                )}
              >
                <div className="flex flex-col items-center gap-2">
                  {/* Tab Number */}
                  <motion.div
                    className={cn(
                      "w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold transition-all duration-300",
                      activeTab === tab.id
                        ? "bg-white/20 text-white border-2 border-white/30"
                        : "bg-gray-200 text-gray-500 group-hover:bg-gray-300 group-hover:text-gray-700"
                    )}
                    initial={false}
                    animate={{
                      scale: activeTab === tab.id ? 1.1 : 1,
                    }}
                    transition={{ duration: 0.2 }}
                  >
                    {index + 1}
                  </motion.div>
                  
                  {/* Tab Label */}
                  <motion.span
                    initial={false}
                    animate={{
                      fontWeight: activeTab === tab.id ? 600 : 500,
                      letterSpacing: activeTab === tab.id ? '0.025em' : '0',
                    }}
                    transition={{ duration: 0.2 }}
                    className="text-center leading-tight"
                  >
                    {tab.label}
                  </motion.span>
                </div>

                {/* Active Tab Indicator */}
                {activeTab === tab.id && (
                  <motion.div
                    className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-12 h-1 bg-white rounded-full"
                    initial={{ opacity: 0, scale: 0.5 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3 }}
                  />
                )}
              </button>
            ))}
          </div>
        </div>
        
        {/* Progress Bar */}
        <div className="mt-4 bg-gray-200 rounded-full h-1 overflow-hidden">
          <motion.div
            className="h-full bg-gradient-to-r from-[#003874] to-[#2D8DC6]"
            initial={{ width: 0 }}
            animate={{ width: `${((activeTabIndex + 1) / tabs.length) * 100}%` }}
            transition={{ duration: 0.5, ease: "easeOut" }}
          />
        </div>
      </div>

      {/* Tab Content */}
      <motion.div
        key={activeTab}
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -10 }}
        transition={{
          duration: 0.3,
          ease: "easeOut"
        }}
        className="relative"
      >
        {activeTabItem?.content}
      </motion.div>
    </div>
  );
} 
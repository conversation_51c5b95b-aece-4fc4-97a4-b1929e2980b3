"use client";

import { motion } from "framer-motion";
import { 
  TrendingUp, 
  Target, 
  CheckCircle2, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Info
} from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { AssessmentCriteria } from "@/types";

interface DomainAverage {
  average: number;
  name: { en: string; ar: string };
  totalSpecifications: number;
  ratedSpecifications: number;
  domainId: string;
  weight?: number; // Domain weight from assessment criteria
}

interface AssessmentKPIsProps {
  domainAverages: Record<string, DomainAverage>;
  assessmentCriteria: AssessmentCriteria | null;
  locale: string;
  onWeightedComplianceClick: () => void;
}

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.5 }
  }
};

export function AssessmentKPIs({ 
  domainAverages, 
  assessmentCriteria, 
  locale,
  onWeightedComplianceClick 
}: AssessmentKPIsProps) {
  const isRtl = locale === 'ar';

  // Calculate weighted compliance KPIs from domain averages
  const calculateKPIs = () => {
    const domains = Object.entries(domainAverages);
    
    if (domains.length === 0) {
      return {
        totalDomains: 0,
        totalSpecifications: 0,
        ratedSpecifications: 0,
        weightedCompliance: 0,
        completionRate: 0,
        highPerformingDomains: 0,
        totalWeight: 0,
        domainsWithWeights: 0
      };
    }

    const totalSpecifications = domains.reduce((sum, [_, domain]) => sum + domain.totalSpecifications, 0);
    const ratedSpecifications = domains.reduce((sum, [_, domain]) => sum + domain.ratedSpecifications, 0);
    
    // Calculate weighted compliance using domain weights
    let weightedCompliance = 0;
    let totalWeight = 0;
    let domainsWithWeights = 0;
    
    domains.forEach(([domainId, domain]) => {
      // Use weight from domain averages if available, otherwise look up in assessment criteria
      const weight = domain.weight || assessmentCriteria?.domainWeights?.find(dw => dw.domainId === domain.domainId)?.weight || 0;
      if (weight > 0) {
        weightedCompliance += domain.average * (weight / 100);
        totalWeight += weight;
        domainsWithWeights++;
      }
    });
    
    // If not all domains have weights, fall back to equal weighting
    if (totalWeight === 0) {
      weightedCompliance = domains.reduce((sum, [_, domain]) => sum + domain.average, 0) / domains.length;
      totalWeight = 100; // Assume 100% for display purposes
      domainsWithWeights = domains.length;
    }

    const completionRate = totalSpecifications > 0 ? (ratedSpecifications / totalSpecifications) * 100 : 0;
    const highPerformingDomains = domains.filter(([_, domain]) => domain.average >= 80).length;

    return {
      totalDomains: domains.length,
      totalSpecifications,
      ratedSpecifications,
      weightedCompliance,
      completionRate,
      highPerformingDomains,
      totalWeight,
      domainsWithWeights
    };
  };

  const kpis = calculateKPIs();

  // Get compliance level label
  const getComplianceLevel = (percentage: number) => {
    if (!assessmentCriteria?.levels) {
      if (percentage >= 80) return isRtl ? 'ملتزم' : 'Compliant';
      if (percentage >= 60) return isRtl ? 'ملتزم جزئياً' : 'Partially Compliant';
      if (percentage >= 40) return isRtl ? 'ملتزم محدود' : 'Limited Compliance';
      return isRtl ? 'غير ملتزم' : 'Non-Compliant';
    }

    const levels = Object.values(assessmentCriteria.levels).sort((a, b) => b.value - a.value);
    const matchingLevel = levels.find(level => percentage >= level.value);
    
    if (matchingLevel) {
      return typeof matchingLevel.label === 'object' 
        ? (matchingLevel.label[locale] || matchingLevel.label.en)
        : matchingLevel.label;
    }
    
    return isRtl ? 'غير محدد' : 'Not Determined';
  };

  const getComplianceColor = (percentage: number) => {
    if (percentage >= 80) return 'text-emerald-700 bg-emerald-50 border-emerald-200';
    if (percentage >= 60) return 'text-blue-700 bg-blue-50 border-blue-200';
    if (percentage >= 40) return 'text-amber-700 bg-amber-50 border-amber-200';
    return 'text-red-700 bg-red-50 border-red-200';
  };

  return (
    <div className="space-y-8">
      {/* Main KPIs Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">

      {/* Weighted Compliance - Clickable */}
      <motion.div variants={itemVariants}>
        <Card className="border-0 shadow-lg bg-gradient-to-br from-emerald-50 to-green-50 hover:shadow-xl transition-all duration-300 cursor-pointer group"
              onClick={onWeightedComplianceClick}>
          <CardContent className="p-6">
            <div className={`flex items-center justify-between mb-4 ${isRtl ? 'flex-row-reverse' : ''}`}>
              <div className="p-3 bg-emerald-100 rounded-xl group-hover:bg-emerald-200 transition-colors">
                <Target className="h-6 w-6 text-emerald-600" />
              </div>
              <div className="flex items-center gap-2">
                <Badge className={`${getComplianceColor(kpis.weightedCompliance)} text-xs font-bold`}>
                  {getComplianceLevel(kpis.weightedCompliance)}
                </Badge>
                <Button variant="ghost" size="sm" className="p-1 h-auto">
                  <Info className="h-4 w-4 text-emerald-600" />
                </Button>
              </div>
            </div>
            <div className={isRtl ? 'text-right' : ''}>
              <p className="text-emerald-600 text-sm font-medium uppercase tracking-wide mb-1">
                {isRtl ? 'الامتثال المرجح' : 'Weighted Compliance'}
              </p>
              <p className="text-3xl font-bold text-emerald-900 mb-1">
                {kpis.weightedCompliance.toFixed(1)}%
              </p>
              <p className="text-emerald-700 text-xs">
                {isRtl ? `عبر ${kpis.domainsWithWeights} مجالات مرجحة` : `Across ${kpis.domainsWithWeights} weighted domains`}
              </p>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Domain Weights Summary */}
      <motion.div variants={itemVariants}>
        <Card className="border-0 shadow-lg bg-gradient-to-br from-blue-50 to-indigo-50 hover:shadow-xl transition-all duration-300">
          <CardContent className="p-6">
            <div className={`flex items-center justify-between mb-4 ${isRtl ? 'flex-row-reverse' : ''}`}>
              <div className="p-3 bg-blue-100 rounded-xl">
                <PieChart className="h-6 w-6 text-blue-600" />
              </div>
              <Badge className={`${kpis.totalWeight >= 100 ? 'bg-emerald-50 text-emerald-700 border-emerald-200' : 
                                 kpis.totalWeight >= 80 ? 'bg-blue-50 text-blue-700 border-blue-200' : 
                                 'bg-amber-50 text-amber-700 border-amber-200'} text-xs font-bold`}>
                {kpis.totalWeight >= 100 ? (isRtl ? 'مكتمل' : 'Complete') : 
                 kpis.totalWeight >= 80 ? (isRtl ? 'جيد' : 'Good') : 
                 (isRtl ? 'يحتاج تحسين' : 'Needs Review')}
              </Badge>
            </div>
            <div className={isRtl ? 'text-right' : ''}>
              <p className="text-blue-600 text-sm font-medium uppercase tracking-wide mb-1">
                {isRtl ? 'إجمالي الأوزان' : 'Total Weights'}
              </p>
              <p className="text-3xl font-bold text-blue-900 mb-1">
                {kpis.totalWeight.toFixed(0)}%
              </p>
              <p className="text-blue-700 text-xs">
                {isRtl ? `${kpis.domainsWithWeights} من ${kpis.totalDomains} مجالات` : 
                         `${kpis.domainsWithWeights} of ${kpis.totalDomains} domains`}
              </p>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Completion Rate */}
      <motion.div variants={itemVariants}>
        <Card className="border-0 shadow-lg bg-gradient-to-br from-purple-50 to-violet-50 hover:shadow-xl transition-all duration-300">
          <CardContent className="p-6">
            <div className={`flex items-center justify-between mb-4 ${isRtl ? 'flex-row-reverse' : ''}`}>
              <div className="p-3 bg-purple-100 rounded-xl">
                <CheckCircle2 className="h-6 w-6 text-purple-600" />
              </div>
              <Badge className={`${kpis.completionRate >= 80 ? 'bg-emerald-50 text-emerald-700 border-emerald-200' : 
                                 kpis.completionRate >= 60 ? 'bg-amber-50 text-amber-700 border-amber-200' : 
                                 'bg-red-50 text-red-700 border-red-200'} text-xs font-bold`}>
                {kpis.completionRate >= 80 ? (isRtl ? 'مكتمل' : 'Complete') : 
                 kpis.completionRate >= 60 ? (isRtl ? 'جاري' : 'In Progress') : 
                 (isRtl ? 'بداية' : 'Starting')}
              </Badge>
            </div>
            <div className={isRtl ? 'text-right' : ''}>
              <p className="text-purple-600 text-sm font-medium uppercase tracking-wide mb-1">
                {isRtl ? 'معدل الإنجاز' : 'Completion Rate'}
              </p>
              <p className="text-3xl font-bold text-purple-900 mb-1">
                {kpis.completionRate.toFixed(1)}%
              </p>
              <p className="text-purple-700 text-xs">
                {isRtl ? `${kpis.ratedSpecifications} من ${kpis.totalSpecifications} مواصفة` : 
                         `${kpis.ratedSpecifications} of ${kpis.totalSpecifications} specs`}
              </p>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* High Performing Domains */}
      <motion.div variants={itemVariants}>
        <Card className="border-0 shadow-lg bg-gradient-to-br from-amber-50 to-orange-50 hover:shadow-xl transition-all duration-300">
          <CardContent className="p-6">
            <div className={`flex items-center justify-between mb-4 ${isRtl ? 'flex-row-reverse' : ''}`}>
              <div className="p-3 bg-amber-100 rounded-xl">
                <BarChart3 className="h-6 w-6 text-amber-600" />
              </div>
              <Badge className={`${kpis.highPerformingDomains > 0 ? 'bg-emerald-50 text-emerald-700 border-emerald-200' : 
                                 'bg-gray-50 text-gray-700 border-gray-200'} text-xs font-bold`}>
                {kpis.highPerformingDomains > 0 ? (isRtl ? 'ممتاز' : 'Excellent') : (isRtl ? 'يحتاج تحسين' : 'Needs Improvement')}
              </Badge>
            </div>
            <div className={isRtl ? 'text-right' : ''}>
              <p className="text-amber-600 text-sm font-medium uppercase tracking-wide mb-1">
                {isRtl ? 'المجالات عالية الأداء' : 'High Performing Domains'}
              </p>
              <p className="text-3xl font-bold text-amber-900 mb-1">
                {kpis.highPerformingDomains}
              </p>
              <p className="text-amber-700 text-xs">
                {isRtl ? 'مجالات بنسبة 80%+' : 'Domains with 80%+ compliance'}
              </p>
            </div>
          </CardContent>
        </Card>
      </motion.div>
      </div>

      {/* Top and Least Performing Domains */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Performing Domains */}
        <motion.div variants={itemVariants}>
          <Card className="border-0 shadow-xl bg-gradient-to-br from-emerald-50 via-green-50 to-teal-50 hover:shadow-2xl transition-all duration-300">
            <CardContent className="p-6">
              <div className={`flex items-center gap-4 mb-6 ${isRtl ? 'flex-row-reverse' : ''}`}>
                <div className="p-4 bg-gradient-to-r from-emerald-500 to-green-500 rounded-2xl shadow-lg">
                  <TrendingUp className="h-8 w-8 text-white" />
                </div>
                <div className={isRtl ? 'text-right' : ''}>
                  <h3 className={`text-xl font-bold text-emerald-900 mb-1 ${isRtl ? 'font-arabic' : ''}`}>
                    {isRtl ? 'أفضل المجالات أداءً' : 'Top Performing Domains'}
                  </h3>
                  <p className={`text-emerald-700 text-sm ${isRtl ? 'font-arabic' : ''}`}>
                    {isRtl ? 'المجالات ذات أعلى نسب امتثال' : 'Domains with highest compliance rates'}
                  </p>
                </div>
              </div>
              
              <div className="space-y-3">
                {Object.entries(domainAverages)
                  .sort(([,a], [,b]) => b.average - a.average)
                  .slice(0, 3)
                  .map(([domainId, domain], index) => {
                    const weight = domain.weight || assessmentCriteria?.domainWeights?.find(dw => dw.domainId === domain.domainId)?.weight || 0;
                    const weightedCompliance = weight > 0 ? domain.average * (weight / 100) : domain.average;
                    
                    return (
                      <div key={domainId} className={`flex items-center justify-between p-4 bg-white/80 backdrop-blur-sm rounded-xl border border-emerald-200/50 hover:bg-white/90 transition-all duration-200 ${isRtl ? 'flex-row-reverse' : ''}`}>
                        <div className={`flex items-center gap-3 ${isRtl ? 'flex-row-reverse' : ''}`}>
                          <div className={`w-8 h-8 rounded-full bg-gradient-to-r from-emerald-500 to-green-500 flex items-center justify-center text-white font-bold text-sm`}>
                            {index + 1}
                          </div>
                          <div className={isRtl ? 'text-right' : ''}>
                            <p className={`font-semibold text-emerald-900 text-sm ${isRtl ? 'font-arabic' : ''}`}>
                              {domain.name[locale as keyof typeof domain.name] || domain.name.en}
                            </p>
                            <p className="text-emerald-700 text-xs">
                              {domain.ratedSpecifications}/{domain.totalSpecifications} {isRtl ? 'مواصفة' : 'specs'}
                            </p>
                          </div>
                        </div>
                        <div className={`text-right ${isRtl ? 'text-left' : ''}`}>
                          <p className="text-emerald-900 font-bold text-lg">
                            {weightedCompliance.toFixed(1)}%
                          </p>
                          {weight > 0 && (
                            <p className="text-emerald-600 text-xs">
                              {isRtl ? `وزن: ${weight}%` : `Weight: ${weight}%`}
                            </p>
                          )}
                        </div>
                      </div>
                    );
                  })}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Least Performing Domains */}
        <motion.div variants={itemVariants}>
          <Card className="border-0 shadow-xl bg-gradient-to-br from-red-50 via-orange-50 to-amber-50 hover:shadow-2xl transition-all duration-300">
            <CardContent className="p-6">
              <div className={`flex items-center gap-4 mb-6 ${isRtl ? 'flex-row-reverse' : ''}`}>
                <div className="p-4 bg-gradient-to-r from-red-500 to-orange-500 rounded-2xl shadow-lg">
                  <AlertTriangle className="h-8 w-8 text-white" />
                </div>
                <div className={isRtl ? 'text-right' : ''}>
                  <h3 className={`text-xl font-bold text-red-900 mb-1 ${isRtl ? 'font-arabic' : ''}`}>
                    {isRtl ? 'المجالات التي تحتاج تحسين' : 'Domains Needing Improvement'}
                  </h3>
                  <p className={`text-red-700 text-sm ${isRtl ? 'font-arabic' : ''}`}>
                    {isRtl ? 'المجالات ذات أقل نسب امتثال' : 'Domains with lowest compliance rates'}
                  </p>
                </div>
              </div>
              
              <div className="space-y-3">
                {Object.entries(domainAverages)
                  .sort(([,a], [,b]) => a.average - b.average)
                  .slice(0, 3)
                  .map(([domainId, domain], index) => {
                    const weight = domain.weight || assessmentCriteria?.domainWeights?.find(dw => dw.domainId === domain.domainId)?.weight || 0;
                    const weightedCompliance = weight > 0 ? domain.average * (weight / 100) : domain.average;
                    
                    return (
                      <div key={domainId} className={`flex items-center justify-between p-4 bg-white/80 backdrop-blur-sm rounded-xl border border-red-200/50 hover:bg-white/90 transition-all duration-200 ${isRtl ? 'flex-row-reverse' : ''}`}>
                        <div className={`flex items-center gap-3 ${isRtl ? 'flex-row-reverse' : ''}`}>
                          <div className={`w-8 h-8 rounded-full bg-gradient-to-r from-red-500 to-orange-500 flex items-center justify-center text-white font-bold text-sm`}>
                            {index + 1}
                          </div>
                          <div className={isRtl ? 'text-right' : ''}>
                            <p className={`font-semibold text-red-900 text-sm ${isRtl ? 'font-arabic' : ''}`}>
                              {domain.name[locale as keyof typeof domain.name] || domain.name.en}
                            </p>
                            <p className="text-red-700 text-xs">
                              {domain.ratedSpecifications}/{domain.totalSpecifications} {isRtl ? 'مواصفة' : 'specs'}
                            </p>
                          </div>
                        </div>
                        <div className={`text-right ${isRtl ? 'text-left' : ''}`}>
                          <p className="text-red-900 font-bold text-lg">
                            {weightedCompliance.toFixed(1)}%
                          </p>
                          {weight > 0 && (
                            <p className="text-red-600 text-xs">
                              {isRtl ? `وزن: ${weight}%` : `Weight: ${weight}%`}
                            </p>
                          )}
                        </div>
                      </div>
                    );
                  })}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
} 
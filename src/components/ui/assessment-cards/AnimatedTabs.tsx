"use client";

import { motion } from "framer-motion";
import { useState } from "react";
import { cn } from "@/lib/utils";

interface TabItem {
  id: string;
  label: string;
  content: React.ReactNode;
}

interface AnimatedTabsProps {
  tabs: TabItem[];
  defaultTab?: string;
  onTabChange?: (tabId: string) => void;
  className?: string;
  isRtl?: boolean;
}

export function AnimatedTabs({ 
  tabs, 
  defaultTab, 
  onTabChange, 
  className,
  isRtl = false 
}: AnimatedTabsProps) {
  const [activeTab, setActiveTab] = useState(defaultTab || tabs[0]?.id);

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
    onTabChange?.(tabId);
  };

  const activeTabIndex = tabs.findIndex(tab => tab.id === activeTab);
  const activeTabItem = tabs.find(tab => tab.id === activeTab);

  return (
    <div className={cn("w-full", className)}>
      {/* Super Enhanced Tab List */}
      <div className="relative mb-10">
        {/* Background Glow Effects */}
        <div className="absolute inset-0 bg-gradient-to-r from-[#003874]/30 via-[#2D8DC6]/30 to-[#48D3A5]/30 rounded-3xl blur-2xl scale-110 opacity-60" />
        <div className="absolute inset-0 bg-gradient-to-r from-[#003874]/20 via-[#2D8DC6]/20 to-[#48D3A5]/20 rounded-2xl blur-xl scale-105 opacity-80" />
        
        <div className={`relative flex bg-white/95 backdrop-blur-lg rounded-2xl p-3 shadow-2xl border border-white/20 ${isRtl ? 'flex-row-reverse' : ''}`}>
          {/* Enhanced Animated Background Indicator */}
          <motion.div
            className="absolute top-3 bottom-3 bg-gradient-to-r from-[#003874] via-[#2D8DC6] to-[#48D3A5] rounded-xl shadow-2xl"
            initial={false}
            animate={{
              [isRtl ? 'right' : 'left']: `${activeTabIndex * (100 / tabs.length)}%`,
              width: `${100 / tabs.length}%`,
            }}
            transition={{
              type: "spring",
              stiffness: 400,
              damping: 35,
            }}
          >
            {/* Inner Glow */}
            <div className="absolute inset-0 bg-white/20 rounded-xl" />
            {/* Shimmer Effect */}
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent rounded-xl"
              animate={{
                x: ['-100%', '200%'],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: "linear"
              }}
            />
          </motion.div>
          
          {/* Enhanced Tab Triggers */}
          {tabs.map((tab, _index) => (
            <motion.button
              key={tab.id}
              onClick={() => handleTabChange(tab.id)}
              className={cn(
                "relative z-10 flex-1 px-10 py-5 text-xl font-bold transition-all duration-400 rounded-xl",
                "focus:outline-none focus:ring-3 focus:ring-[#003874]/30 focus:ring-offset-3",
                activeTab === tab.id
                  ? "text-white shadow-2xl"
                  : "text-gray-700 hover:text-gray-900 hover:bg-white/70",
                isRtl ? 'font-arabic' : ''
              )}
              whileHover={{ 
                scale: activeTab === tab.id ? 1.02 : 1.05,
                y: activeTab === tab.id ? 0 : -2
              }}
              whileTap={{ scale: 0.96 }}
              transition={{ 
                type: "spring",
                stiffness: 300,
                damping: 20
              }}
            >
              <motion.span
                initial={false}
                animate={{
                  scale: activeTab === tab.id ? 1.1 : 1,
                  fontWeight: activeTab === tab.id ? 800 : 700,
                  letterSpacing: activeTab === tab.id ? '0.05em' : '0.025em'
                }}
                transition={{ 
                  duration: 0.3,
                  ease: "easeInOut"
                }}
              >
                {tab.label}
              </motion.span>
              
              {/* Enhanced Active Tab Effects */}
              {activeTab === tab.id && (
                <>
                  {/* Inner Glow */}
                  <motion.div
                    className="absolute inset-0 bg-white/25 rounded-xl"
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.4 }}
                  />
                  {/* Pulse Effect */}
                  <motion.div
                    className="absolute inset-0 bg-white/10 rounded-xl"
                    animate={{
                      scale: [1, 1.05, 1],
                      opacity: [0.3, 0.1, 0.3],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  />
                </>
              )}
              
              {/* Hover Sparkle Effect */}
              {activeTab !== tab.id && (
                <motion.div
                  className="absolute inset-0 bg-gradient-to-r from-[#003874]/5 via-[#2D8DC6]/5 to-[#48D3A5]/5 rounded-xl opacity-0"
                  whileHover={{ opacity: 1 }}
                  transition={{ duration: 0.3 }}
                />
              )}
            </motion.button>
          ))}
        </div>
        
        {/* Enhanced Floating Particles */}
        <motion.div
          className="absolute -top-2 -left-2 w-4 h-4 bg-[#48D3A5] rounded-full blur-sm"
          animate={{
            y: [-5, 5, -5],
            opacity: [0.4, 0.8, 0.4],
          }}
          transition={{
            duration: 3,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div
          className="absolute -bottom-2 -right-2 w-3 h-3 bg-[#2D8DC6] rounded-full blur-sm"
          animate={{
            y: [3, -3, 3],
            opacity: [0.6, 0.9, 0.6],
          }}
          transition={{
            duration: 2.5,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 0.5
          }}
        />
      </div>

      {/* Tab Content with Animations */}
      <motion.div
        key={activeTab}
        initial={{ opacity: 0, y: 20, scale: 0.98 }}
        animate={{ opacity: 1, y: 0, scale: 1 }}
        exit={{ opacity: 0, y: -20, scale: 0.98 }}
        transition={{
          duration: 0.4,
          ease: [0.4, 0, 0.2, 1]
        }}
        className="relative"
      >
        {/* Content Background with Subtle Animation */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.1, duration: 0.3 }}
          className="absolute inset-0 bg-gradient-to-br from-white/50 to-gray-50/30 rounded-2xl backdrop-blur-sm"
        />
        
        {/* Actual Content */}
        <div className="relative z-10">
          {activeTabItem?.content}
        </div>
      </motion.div>
    </div>
  );
} 
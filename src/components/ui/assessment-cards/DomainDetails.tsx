"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { 
  Building2, 
  Target, 
  Search,
  Shield,
  Loader2
} from "lucide-react";
import { Card, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Domain, Framework } from "@/types";

interface Assessment {
  id: string;
  name: {
    en: string;
    ar: string;
  };
  description: string;
  frameworkId: string;
  status: string;
  createdAt: Date | string | null;
  updatedAt: Date | string | null;
}

interface DomainDetailsProps {
  domains: Domain[];
  _framework: Framework | null;
  _assessment: Assessment;
  locale: string;
  domainsLoaded: boolean;
  loadDomains: () => Promise<void>;
  projectId: string;
  assessmentId: string;
}

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.1
    }
  }
};

const heroCardVariants = {
  hidden: { 
    opacity: 0, 
    y: 30,
    scale: 0.95
  },
  visible: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      type: "spring",
      stiffness: 200,
      damping: 25,
      duration: 0.6
    }
  },
  hover: {
    y: -8,
    scale: 1.02,
    transition: {
      type: "spring",
      stiffness: 400,
      damping: 25
    }
  }
};



export function DomainDetails({ 
  domains, 
  _framework, 
  _assessment, 
  locale,
  domainsLoaded,
  loadDomains,
  projectId,
  assessmentId
}: DomainDetailsProps) {
  const t = useTranslations('AssessmentDetails');
  const router = useRouter();
  const isRtl = locale === 'ar';

  // Helper function to get localized name
  const getLocalizedName = (name: string | { en: string; ar: string; [key: string]: string }) => {
    if (typeof name === 'string') return name;
    return name[locale] || name.en || '';
  };

  // Helper function to get localized description
  const getLocalizedDescription = (description: string | { en: string; ar: string; } | undefined) => {
    if (!description) return '';
    if (typeof description === 'string') return description;
    return description[locale as keyof typeof description] || description.en || '';
  };

  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('name');
  const [loading, setLoading] = useState(false);

  // Load domains when component mounts if not already loaded
  useEffect(() => {
    if (!domainsLoaded) {
      setLoading(true);
      loadDomains().finally(() => setLoading(false));
    }
  }, [domainsLoaded, loadDomains]);

  // Filter and sort domains
  const filteredDomains = domains
    .filter(domain => {
      const domainName = getLocalizedName(domain.name);
      return domainName.toLowerCase().includes(searchTerm.toLowerCase());
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'name':
          const nameA = getLocalizedName(a.name);
          const nameB = getLocalizedName(b.name);
          return nameA.localeCompare(nameB);
        case 'score':
          return (b.score / b.maxScore) - (a.score / a.maxScore);
        case 'specifications':
          return (b.specifications?.length || 0) - (a.specifications?.length || 0);
        default:
          return 0;
      }
    });

  // Handle domain navigation
  const handleDomainClick = (domain: Domain) => {
    const domainName = getLocalizedName(domain.name) || domain.id;
    const encodedDomainName = encodeURIComponent(domainName);
    router.push(`/${locale}/maturity-assessment/${projectId}/${assessmentId}/${encodedDomainName}`);
  };

  // Use consistent portal theme colors for all domain cards
  const getPortalThemeGradient = (index: number) => {
    const gradients = [
      'from-[#003874] via-[#2D8DC6] to-[#48D3A5]',
      'from-[#2D8DC6] via-[#48D3A5] to-[#003874]',
      'from-[#48D3A5] via-[#003874] to-[#2D8DC6]',
      'from-[#003874] via-[#48D3A5] to-[#2D8DC6]'
    ];
    return gradients[index % gradients.length];
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-16">
        <div className="text-center">
          <Loader2 className="h-12 w-12 animate-spin text-[#003874] mx-auto mb-4" />
          <p className="text-gray-600 text-lg">Loading domains...</p>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="space-y-12 min-h-screen"
    >
      {/* Enhanced Header and Controls */}
      <motion.div variants={heroCardVariants} className="mb-8">
        <div className="relative overflow-hidden bg-gradient-to-br from-[#003874] via-[#2D8DC6] to-[#48D3A5] rounded-3xl shadow-2xl p-8">
          {/* Decorative Elements */}
          <div className="absolute top-0 right-0 w-96 h-96 bg-white/10 rounded-full blur-3xl -translate-y-48 translate-x-48" />
          <div className="absolute bottom-0 left-0 w-64 h-64 bg-white/5 rounded-full blur-3xl translate-y-32 -translate-x-32" />
          <div className="absolute top-1/2 left-1/2 w-80 h-80 bg-white/5 rounded-full blur-3xl -translate-x-1/2 -translate-y-1/2" />
          
          <div className="relative z-10">
            {/* Header Section */}
            <div className={`flex items-center justify-center mb-8 ${isRtl ? 'flex-row-reverse' : ''}`}>
              <div className={`flex items-center gap-6 ${isRtl ? 'flex-row-reverse' : ''}`}>
                <div className="p-5 bg-white/20 rounded-3xl backdrop-blur-sm shadow-lg">
                  <Building2 className="h-10 w-10 text-white" />
                </div>
                <div className={isRtl ? 'text-right' : ''}>
                  <h1 className={`text-3xl font-bold text-white mb-2 ${isRtl ? 'font-arabic' : ''}`}>
                    {t('domainDetails')}
                  </h1>
                  <p className="text-white/90 text-lg leading-relaxed">
                    Compliance assessment domains and specifications
                  </p>
                </div>
              </div>
            </div>

            {/* Search and Filter Controls */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {/* Search */}
              <div className="relative">
                <Search className={`absolute top-4 h-5 w-5 text-white/70 ${isRtl ? 'right-4' : 'left-4'}`} />
                <Input
                  placeholder={t('searchDomains')}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className={`bg-white/20 border-white/30 text-white placeholder:text-white/70 py-4 rounded-xl backdrop-blur-sm ${isRtl ? 'pr-12' : 'pl-12'}`}
                />
              </div>

              {/* Sort */}
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="bg-white/20 border-white/30 text-white py-4 rounded-xl backdrop-blur-sm">
                  <SelectValue placeholder={t('sortBy')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="name">{t('sortByName')}</SelectItem>
                  <SelectItem value="score">{t('sortByScore')}</SelectItem>
                  <SelectItem value="specifications">{t('sortBySpecifications')}</SelectItem>
                </SelectContent>
              </Select>

              {/* Domains Count */}
              <div className={`flex items-center gap-4 p-4 bg-white/20 rounded-xl backdrop-blur-sm ${isRtl ? 'flex-row-reverse' : ''}`}>
                <div className="p-3 bg-white/30 rounded-xl">
                  <Building2 className="h-5 w-5 text-white" />
                </div>
                <div className={isRtl ? 'text-right' : ''}>
                  <p className="text-white/80 text-sm font-medium">Domains</p>
                  <p className="text-white font-bold text-xl">{filteredDomains.length}</p>
                </div>
              </div>

              {/* Assessment Type */}
              <div className={`flex items-center gap-4 p-4 bg-white/20 rounded-xl backdrop-blur-sm ${isRtl ? 'flex-row-reverse' : ''}`}>
                <div className="p-3 bg-white/30 rounded-xl">
                  <Shield className="h-5 w-5 text-white" />
                </div>
                <div className={isRtl ? 'text-right' : ''}>
                  <p className="text-white/80 text-sm font-medium">Type</p>
                  <p className="text-white font-bold text-xl">Compliance</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </motion.div>

                   {/* Super Hero Domain Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8">
        {filteredDomains.map((domain, index) => {
          const gradientClass = getPortalThemeGradient(index);

          return (
            <motion.div
              key={domain.id}
              variants={heroCardVariants}
              whileHover="hover"
              className="group cursor-pointer"
              onClick={() => handleDomainClick(domain)}
            >
              <Card className="relative overflow-hidden border-0 shadow-2xl hover:shadow-3xl transition-all duration-700 h-full bg-white">
                {/* Super Hero Background with Enhanced Portal Theme Gradient */}
                <div className={`absolute inset-0 bg-gradient-to-br ${gradientClass} opacity-[0.12] group-hover:opacity-[0.18] transition-opacity duration-700`} />
                
                {/* Enhanced Decorative Elements */}
                <div className="absolute top-0 right-0 w-64 h-64 bg-white/30 rounded-full blur-3xl opacity-40 group-hover:opacity-60 transition-opacity duration-700 -translate-y-32 translate-x-32" />
                <div className="absolute bottom-0 left-0 w-48 h-48 bg-white/20 rounded-full blur-2xl opacity-30 group-hover:opacity-50 transition-opacity duration-700 translate-y-24 -translate-x-24" />
                <div className="absolute top-1/2 left-1/2 w-32 h-32 bg-white/15 rounded-full blur-xl opacity-40 group-hover:opacity-60 transition-opacity duration-700 -translate-x-1/2 -translate-y-1/2" />
                
                <CardHeader className="relative z-10 p-8">
                  {/* Super Hero Header Section */}
                  <div className={`flex items-center gap-6 mb-6 ${isRtl ? 'flex-row-reverse' : ''}`}>
                    <div className={`p-6 bg-gradient-to-br ${gradientClass} rounded-3xl shadow-2xl group-hover:shadow-3xl transition-all duration-500 group-hover:scale-110 group-hover:rotate-3`}>
                      <Building2 className="h-12 w-12 text-white" />
                    </div>
                    
                    <div className={`flex-1 ${isRtl ? 'text-right' : ''}`}>
                      <h3 className={`text-2xl font-bold text-gray-900 group-hover:text-[#003874] transition-colors duration-500 leading-tight mb-3 ${isRtl ? 'font-arabic' : ''}`}>
                        {getLocalizedName(domain.name)}
                      </h3>
                      
                      {/* Enhanced Domain Type Badge */}
                      <Badge className="px-4 py-2 text-sm font-bold bg-white/90 backdrop-blur-sm text-[#003874] border-[#003874]/30 shadow-lg hover:shadow-xl transition-shadow duration-300">
                        <Shield className="h-4 w-4 mr-2" />
                        Domain
                      </Badge>
                    </div>
                  </div>

                  {/* Enhanced Description Section */}
                  {domain.description && (
                    <div className="mb-6 p-4 bg-white/50 backdrop-blur-sm rounded-xl border border-white/30">
                      <p className={`text-gray-700 leading-relaxed text-base ${isRtl ? 'text-right font-arabic' : ''}`}>
                        {getLocalizedDescription(domain.description) || 'No description available'}
                      </p>
                    </div>
                  )}

                  {/* Assessment Info Display */}
                  <div className={`flex items-center justify-center p-4 bg-white/70 backdrop-blur-sm rounded-xl border border-white/50 ${isRtl ? 'flex-row-reverse' : ''}`}>
                    <div className={`flex items-center gap-3 ${isRtl ? 'flex-row-reverse' : ''}`}>
                      <div className={`p-3 bg-gradient-to-r ${gradientClass} rounded-xl shadow-lg`}>
                        <Target className="h-6 w-6 text-white" />
                      </div>
                      <div className={isRtl ? 'text-right' : ''}>
                        <p className="text-sm text-gray-600 font-medium">Assessment Type</p>
                        <p className="text-lg font-bold text-gray-900">Compliance Assessment</p>
                      </div>
                    </div>
                  </div>
                </CardHeader>

                {/* Enhanced Hover Glow Effect */}
                <div className={`absolute inset-0 rounded-3xl bg-gradient-to-br ${gradientClass} opacity-0 group-hover:opacity-[0.08] transition-opacity duration-700 pointer-events-none`} />
                
                {/* Sparkle Effects */}
                <motion.div
                  className="absolute top-4 right-4 w-2 h-2 bg-white rounded-full opacity-0 group-hover:opacity-80"
                  animate={{
                    scale: [0, 1.5, 0],
                    opacity: [0, 0.8, 0],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    delay: 0.2,
                  }}
                />
                <motion.div
                  className="absolute bottom-8 left-8 w-1.5 h-1.5 bg-white rounded-full opacity-0 group-hover:opacity-60"
                  animate={{
                    scale: [0, 1.2, 0],
                    opacity: [0, 0.6, 0],
                  }}
                  transition={{
                    duration: 2.5,
                    repeat: Infinity,
                    delay: 0.8,
                  }}
                />
              </Card>
            </motion.div>
          );
        })}
      </div>

      {/* No Results */}
      {filteredDomains.length === 0 && (
        <motion.div variants={heroCardVariants}>
          <div className="relative overflow-hidden bg-gradient-to-br from-[#003874] via-[#2D8DC6] to-[#48D3A5] rounded-3xl shadow-2xl">
            {/* Decorative Elements */}
            <div className="absolute top-0 right-0 w-64 h-64 bg-white/10 rounded-full blur-3xl -translate-y-32 translate-x-32" />
            <div className="absolute bottom-0 left-0 w-48 h-48 bg-white/5 rounded-full blur-2xl translate-y-24 -translate-x-24" />
            
            <div className="relative z-10 text-center py-24 px-12">
              <div className="p-8 bg-white/20 rounded-3xl backdrop-blur-sm mx-auto w-fit mb-8">
                <Search className="h-24 w-24 text-white mx-auto" />
              </div>
              <h3 className={`text-4xl font-bold text-white mb-4 ${isRtl ? 'font-arabic' : ''}`}>
                {t('noDomainsFound')}
              </h3>
              <p className="text-white/90 text-xl leading-relaxed mb-8 max-w-2xl mx-auto">
                {searchTerm 
                  ? `${t('noDomainMatch')} "${searchTerm}"` 
                  : t('noDomainsAvailable')
                }
              </p>
              {searchTerm && (
                <Button
                  onClick={() => setSearchTerm('')}
                  className="bg-white/20 hover:bg-white/30 text-white border-white/30 backdrop-blur-sm px-8 py-4 text-lg rounded-xl shadow-lg"
                >
                  {t('clearSearch')}
                </Button>
              )}
            </div>
          </div>
        </motion.div>
      )}
    </motion.div>
  );
} 
"use client";

import { motion } from "framer-motion";
import { useTranslations } from "next-intl";
import Link from "next/link";
import { 
  Eye, 
  Calendar, 
  Shield, 
  ChevronRight,
  FileText,
  Target,
  Building2,
  Clock,
  CheckCircle2,
  TrendingUp,
  Users,
  Star,
  Sparkles,
  ArrowUpRight,
  Activity,
  Award,
  Zap
} from "lucide-react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

interface Assessment {
  id: string;
  name: {
    en: string;
    ar: string;
  };
  description: string;
  frameworkId: string;
  frameworkName?: {
    en: string;
    ar: string;
  };
  status: string;
  createdAt: Date | { toDate: () => Date } | string | null;
  updatedAt: Date | { toDate: () => Date } | string | null;
}

interface AssessmentCardProps {
  assessment: Assessment;
  locale: string;
  projectId: string;
}

// Enhanced animation variants
const heroCardVariants = {
  hidden: { 
    opacity: 0, 
    y: 60,
    scale: 0.9,
    rotateX: 15
  },
  visible: {
    opacity: 1,
    y: 0,
    scale: 1,
    rotateX: 0,
    transition: {
      type: "spring",
      stiffness: 300,
      damping: 30,
      duration: 0.8,
      staggerChildren: 0.1
    }
  },
  hover: {
    y: -20,
    scale: 1.05,
    rotateX: -2,
    transition: {
      type: "spring",
      stiffness: 400,
      damping: 25,
      duration: 0.4
    }
  }
};

const contentVariants = {
  hidden: { 
    opacity: 0,
    y: 30
  },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      delay: 0.3,
      duration: 0.6,
      staggerChildren: 0.15
    }
  }
};

const itemVariants = {
  hidden: { 
    opacity: 0,
    x: -30,
    scale: 0.95
  },
  visible: {
    opacity: 1,
    x: 0,
    scale: 1,
    transition: {
      type: "spring",
      stiffness: 200,
      damping: 20
    }
  }
};

const glowVariants = {
  hidden: { opacity: 0, scale: 0.8 },
  visible: { 
    opacity: [0, 0.3, 0],
    scale: [0.8, 1.2, 1],
    transition: {
      duration: 2,
      repeat: Infinity,
      ease: "easeInOut"
    }
  }
};

export function AssessmentCard({ assessment, locale, projectId }: AssessmentCardProps) {
  const t = useTranslations('ComplianceAssessment');
  const isRtl = locale === 'ar';

  // Format date with enhanced styling
  const formatDate = (timestamp: Date | { toDate: () => Date } | string | null) => {
    if (!timestamp) return '-';
    try {
      const date = typeof timestamp === 'object' && 'toDate' in timestamp 
        ? timestamp.toDate() 
        : new Date(timestamp);
      return date.toLocaleDateString(locale === 'ar' ? 'ar-SA' : 'en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      });
    } catch {
      return '-';
    }
  };

  // Enhanced status configuration with more visual appeal
  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'active':
        return {
          color: 'bg-gradient-to-r from-emerald-500 to-green-600 text-white',
          bgColor: 'from-emerald-50 to-green-50',
          borderColor: 'border-emerald-200',
          icon: CheckCircle2,
          label: 'Active',
          glow: 'shadow-emerald-500/25'
        };
      case 'completed':
        return {
          color: 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white',
          bgColor: 'from-blue-50 to-indigo-50',
          borderColor: 'border-blue-200',
          icon: Award,
          label: 'Completed',
          glow: 'shadow-blue-500/25'
        };
      case 'draft':
        return {
          color: 'bg-gradient-to-r from-amber-500 to-orange-600 text-white',
          bgColor: 'from-amber-50 to-orange-50',
          borderColor: 'border-amber-200',
          icon: Clock,
          label: 'Draft',
          glow: 'shadow-amber-500/25'
        };
      default:
        return {
          color: 'bg-gradient-to-r from-gray-500 to-slate-600 text-white',
          bgColor: 'from-gray-50 to-slate-50',
          borderColor: 'border-gray-200',
          icon: FileText,
          label: status,
          glow: 'shadow-gray-500/25'
        };
    }
  };

  const statusConfig = getStatusConfig(assessment.status);
  const StatusIcon = statusConfig.icon;

  return (
    <motion.div
      variants={heroCardVariants}
      initial="hidden"
      animate="visible"
      whileHover="hover"
      className="group w-full perspective-1000"
    >
      <Card className="relative overflow-hidden border border-white/20 bg-gradient-to-br from-white via-gray-50/50 to-white shadow-2xl hover:shadow-4xl transition-all duration-700 backdrop-blur-sm h-full transform-gpu animate-breathe">
        {/* Animated Background Gradient */}
        <div className="absolute inset-0 bg-gradient-to-br from-[#003874]/8 via-[#2D8DC6]/6 to-[#48D3A5]/8 opacity-60 group-hover:opacity-100 transition-opacity duration-700" />
        
        {/* Base gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50/80 via-cyan-50/60 to-emerald-50/80" />
        
        {/* Subtle pattern overlay */}
        <div className="absolute inset-0 opacity-[0.02] bg-[radial-gradient(circle_at_1px_1px,rgba(0,56,116,0.3)_1px,transparent_0)]" style={{backgroundSize: '20px 20px'}} />
        
        {/* Dynamic Glow Effects */}
        <motion.div 
          variants={glowVariants}
          className="absolute top-0 right-0 w-40 h-40 bg-gradient-to-br from-[#48D3A5]/20 to-transparent rounded-full blur-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-700 -translate-y-10 translate-x-10"
        />
        <motion.div 
          variants={glowVariants}
          className="absolute bottom-0 left-0 w-32 h-32 bg-gradient-to-tr from-[#003874]/20 to-transparent rounded-full blur-2xl opacity-30 group-hover:opacity-60 transition-opacity duration-700 translate-y-6 -translate-x-6"
        />
        
        {/* Floating Particles Effect */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          {[...Array(6)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 bg-gradient-to-r from-[#48D3A5] to-[#2D8DC6] rounded-full opacity-0 group-hover:opacity-30"
              style={{
                left: `${20 + i * 15}%`,
                top: `${30 + i * 10}%`,
              }}
              animate={{
                y: [-10, -30, -10],
                opacity: [0, 0.3, 0],
                scale: [0.5, 1, 0.5],
              }}
              transition={{
                duration: 3 + i * 0.5,
                repeat: Infinity,
                delay: i * 0.3,
                ease: "easeInOut"
              }}
            />
          ))}
        </div>

        {/* Hero Header with Enhanced Design */}
        <CardHeader className="relative z-10 pb-6 bg-gradient-to-br from-[#003874]/5 via-[#2D8DC6]/3 to-[#48D3A5]/5 backdrop-blur-sm border-b border-white/20">
          <motion.div 
            variants={contentVariants}
            className="space-y-6"
          >
            {/* Status Badge and Date - Enhanced */}
            <div className={`flex items-center justify-between ${isRtl ? 'flex-row-reverse' : ''}`}>
              <motion.div variants={itemVariants}>
                <Badge className={`${statusConfig.color} px-4 py-2 text-sm font-bold border-0 shadow-lg ${statusConfig.glow} flex items-center gap-2`}>
                  <StatusIcon className="h-4 w-4" />
                  {statusConfig.label}
                  <Sparkles className="h-3 w-3 opacity-70" />
                </Badge>
              </motion.div>
              
              <motion.div 
                variants={itemVariants}
                className={`flex items-center gap-2 text-gray-600 bg-white/90 backdrop-blur-sm px-4 py-2 rounded-full shadow-md border border-white/50 ${isRtl ? 'flex-row-reverse' : ''}`}
              >
                <Calendar className="h-4 w-4" />
                <span className="text-sm font-semibold">
                  {formatDate(assessment.createdAt)}
                </span>
              </motion.div>
            </div>

            {/* Hero Title Section - Completely Redesigned */}
            <motion.div 
              variants={itemVariants}
              className={`flex items-start gap-6 ${isRtl ? 'flex-row-reverse' : ''}`}
            >
              <div className="relative flex-shrink-0">
                <div className="p-5 bg-gradient-to-br from-[#003874] via-[#2D8DC6] to-[#48D3A5] rounded-3xl shadow-2xl group-hover:shadow-3xl transition-all duration-500 transform group-hover:scale-110 group-hover:rotate-3">
                  <Shield className="h-10 w-10 text-white" />
                </div>
                {/* Floating Ring Effect */}
                <div className="absolute inset-0 rounded-3xl border-2 border-[#48D3A5]/30 scale-125 opacity-0 group-hover:opacity-100 group-hover:scale-150 transition-all duration-700" />
              </div>
              
              <div className={`flex-1 ${isRtl ? 'text-right' : ''}`}>
                <motion.h2 
                  variants={itemVariants}
                  className={`text-3xl font-black text-gray-900 group-hover:animate-gradient-text transition-all duration-500 leading-tight mb-3 ${isRtl ? 'font-arabic' : ''}`}
                >
                  {assessment.name?.[locale as keyof typeof assessment.name] || assessment.name?.en}
                </motion.h2>
                
                <motion.p 
                  variants={itemVariants}
                  className={`text-gray-600 text-lg leading-relaxed line-clamp-2 font-medium ${isRtl ? 'font-arabic' : ''}`}
                >
                  {assessment.description || t('noDescriptionAvailable')}
                </motion.p>
              </div>
            </motion.div>
          </motion.div>
        </CardHeader>

        {/* Enhanced Content Section */}
        <CardContent className="relative z-10 px-6 pb-6 space-y-6">
          <motion.div 
            variants={contentVariants}
            className="space-y-6"
          >
            {/* Framework Information - Hero Style */}
            {assessment.frameworkName && (
              <motion.div 
                variants={itemVariants}
                className={`relative p-6 bg-gradient-to-br from-white/90 via-blue-50/80 to-indigo-50/90 rounded-2xl border border-blue-200/50 shadow-lg hover:shadow-xl transition-all duration-300 ${isRtl ? 'text-right' : ''}`}
              >
                <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-br from-white/50 to-transparent rounded-full blur-xl" />
                <div className={`flex items-center gap-4 ${isRtl ? 'flex-row-reverse' : ''}`}>
                  <div className="relative p-3 bg-white rounded-2xl shadow-lg">
                    <Target className="h-6 w-6 text-[#003874]" />
                    <div className="absolute inset-0 bg-gradient-to-br from-[#48D3A5]/20 to-transparent rounded-2xl" />
                  </div>
                  <div className="flex-1">
                    <p className="text-xs font-bold text-[#003874] uppercase tracking-wider mb-2 flex items-center gap-2">
                      <Zap className="h-3 w-3" />
                      Framework
                    </p>
                    <p className={`text-xl font-black text-[#003874] ${isRtl ? 'font-arabic' : ''}`}>
                      {assessment.frameworkName?.[locale as keyof typeof assessment.frameworkName] || assessment.frameworkName?.en}
                    </p>
                  </div>
                </div>
              </motion.div>
            )}

            {/* Assessment Metrics - Enhanced Grid */}
            <motion.div 
              variants={itemVariants}
              className="grid grid-cols-2 gap-4"
            >
              <div className={`relative p-5 bg-gradient-to-br from-emerald-50 via-green-50 to-emerald-100 rounded-2xl border border-emerald-200 shadow-lg hover:shadow-xl transition-all duration-300 group/metric ${isRtl ? 'text-right' : ''}`}>
                <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-emerald-300/30 to-transparent rounded-full blur-lg" />
                <div className={`flex items-center gap-3 mb-3 ${isRtl ? 'flex-row-reverse' : ''}`}>
                  <div className="p-2 bg-emerald-500 rounded-xl shadow-lg group-hover/metric:scale-110 transition-transform duration-300">
                    <TrendingUp className="h-5 w-5 text-white" />
                  </div>
                  <span className="text-xs font-black text-emerald-700 uppercase tracking-wider">
                    Progress
                  </span>
                </div>
                <p className="text-xl font-black text-emerald-800">
                  Ready to Start
                </p>
              </div>

              <div className={`relative p-5 bg-gradient-to-br from-purple-50 via-violet-50 to-purple-100 rounded-2xl border border-purple-200 shadow-lg hover:shadow-xl transition-all duration-300 group/metric ${isRtl ? 'text-right' : ''}`}>
                <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-purple-300/30 to-transparent rounded-full blur-lg" />
                <div className={`flex items-center gap-3 mb-3 ${isRtl ? 'flex-row-reverse' : ''}`}>
                  <div className="p-2 bg-purple-500 rounded-xl shadow-lg group-hover/metric:scale-110 transition-transform duration-300">
                    <Users className="h-5 w-5 text-white" />
                  </div>
                  <span className="text-xs font-black text-purple-700 uppercase tracking-wider">
                    Type
                  </span>
                </div>
                <p className="text-xl font-black text-purple-800">
                  Compliance
                </p>
              </div>
            </motion.div>

            {/* Enhanced Quick Stats */}
            <motion.div 
              variants={itemVariants}
              className={`relative p-4 bg-gradient-to-r from-white/90 via-gray-50/80 to-white/90 rounded-2xl border border-gray-200/50 shadow-lg ${isRtl ? 'flex-row-reverse' : ''}`}
            >
              <div className="absolute inset-0 bg-gradient-to-r from-[#003874]/5 to-[#48D3A5]/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
              <div className={`relative flex items-center justify-between ${isRtl ? 'flex-row-reverse' : ''}`}>
                <div className={`flex items-center gap-3 ${isRtl ? 'flex-row-reverse' : ''}`}>
                  <div className="p-2 bg-gradient-to-br from-[#003874] to-[#2D8DC6] rounded-lg shadow-md">
                    <Building2 className="h-4 w-4 text-white" />
                  </div>
                  <span className="text-sm text-gray-700 font-bold">
                    Business Assessment
                  </span>
                </div>
                
                <div className={`flex items-center gap-2 ${isRtl ? 'flex-row-reverse' : ''}`}>
                  <Activity className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600 font-medium">
                    Updated {formatDate(assessment.updatedAt)}
                  </span>
                </div>
              </div>
            </motion.div>
          </motion.div>
        </CardContent>

        {/* Hero Footer - Completely Redesigned */}
        <CardFooter className={`relative z-10 border-t border-white/30 bg-gradient-to-r from-[#003874]/5 via-[#2D8DC6]/3 to-[#48D3A5]/5 backdrop-blur-md p-6 ${isRtl ? 'flex-row-reverse' : ''}`}>
          <motion.div
            variants={itemVariants}
            className="w-full"
          >
            <Link href={`/${locale}/maturity-assessment/${projectId}/${assessment.id}`} className="block w-full">
              <Button
                size="lg"
                className={`relative w-full h-16 bg-gradient-to-r from-[#003874] via-[#2D8DC6] to-[#48D3A5] hover:from-[#001f4d] hover:via-[#1e6fa8] hover:to-[#2fb380] text-white font-black text-lg transition-all duration-500 shadow-xl hover:shadow-2xl group-hover:scale-105 overflow-hidden border border-white/20 ${isRtl ? 'flex-row-reverse' : ''}`}
              >
                {/* Button Glow Effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000" />
                
                <Eye className={`h-6 w-6 ${isRtl ? 'ml-4' : 'mr-4'} relative z-10`} />
                <span className="flex-1 relative z-10">{t('viewAssessment')}</span>
                <motion.div
                  initial={{ x: isRtl ? -8 : 8, opacity: 0 }}
                  animate={{ x: 0, opacity: 0 }}
                  whileHover={{ x: isRtl ? -8 : 8, opacity: 1 }}
                  transition={{ duration: 0.3 }}
                  className="relative z-10"
                >
                  <ArrowUpRight className={`h-6 w-6 ${isRtl ? 'rotate-180' : ''}`} />
                </motion.div>
              </Button>
            </Link>
          </motion.div>
        </CardFooter>

        {/* Enhanced Hover Glow Effect */}
        <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-[#003874]/10 via-[#2D8DC6]/10 to-[#48D3A5]/10 opacity-0 group-hover:opacity-100 transition-all duration-700 pointer-events-none" />
        
        {/* Border Glow */}
        <div className="absolute inset-0 rounded-2xl border-2 border-gradient-to-r from-[#003874]/20 via-[#2D8DC6]/20 to-[#48D3A5]/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none" />
        
        {/* Shimmer effect on hover */}
        <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-in-out pointer-events-none" />
      </Card>
    </motion.div>
  );
} 
"use client";

import { useState, Suspense } from "react";
import { motion } from "framer-motion";
import { useTranslations } from "next-intl";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { AssessmentKPIs } from "@/components/ui/assessment-cards/AssessmentKPIs";
import { AssessmentCharts } from "@/components/ui/assessment-cards/AssessmentCharts";
import { DomainFilter } from "@/components/ui/assessment-cards/DomainFilter";
import { WeightedComplianceModal } from "@/components/ui/assessment-cards/WeightedComplianceModal";
import { useAssessmentCriteria } from "@/hooks/useAssessmentCriteria";
import { Domain, Framework } from "@/types";

interface DomainAverage {
  average: number;
  name: { en: string; ar: string };
  totalSpecifications: number;
  ratedSpecifications: number;
  domainId: string;
  weight?: number; // Domain weight from assessment criteria
}

interface Assessment {
  id: string;
  name: {
    en: string;
    ar: string;
  };
  description: string;
  frameworkId: string;
  status: string;
  createdAt: Date | { toDate: () => Date } | string | null;
  updatedAt: Date | { toDate: () => Date } | string | null;
  domainAverages?: Record<string, DomainAverage>;
}

interface AssessmentOverviewProps {
  assessment: Assessment;
  framework: Framework | null;
  domains: Domain[];
  locale: string;
}

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.2
    }
  }
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5
    }
  }
};

// Loading skeleton component
const LoadingSkeleton = ({ isRtl }: { isRtl: boolean }) => (
  <div className="space-y-8">
    {/* KPIs Skeleton */}
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {[...Array(4)].map((_, i) => (
        <div key={i} className="h-32 bg-gray-200 rounded-lg animate-pulse" />
      ))}
    </div>
    
    {/* Charts Skeleton */}
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
      {[...Array(4)].map((_, i) => (
        <div key={i} className="h-80 bg-gray-200 rounded-lg animate-pulse" />
      ))}
    </div>
    
    {/* Assessment Info Skeleton */}
    <div className="h-64 bg-gray-200 rounded-lg animate-pulse" />
  </div>
);

export function AssessmentOverview({ 
  assessment, 
  framework, 
  domains, 
  locale 
}: AssessmentOverviewProps) {
  const isRtl = locale === 'ar';
  const [showWeightedModal, setShowWeightedModal] = useState(false);
  const [selectedDomains, setSelectedDomains] = useState<string[]>([]);
  
  // Fetch assessment criteria for the framework
  const { 
    assessmentCriteria, 
    loading: criteriaLoading 
  } = useAssessmentCriteria(framework?.id || '');

  // Helper function to get localized name
  const getLocalizedName = (name: string | { en: string; ar: string; [key: string]: string }) => {
    if (typeof name === 'string') return name;
    return name[locale] || name.en || '';
  };

  // Format date
  const formatDate = (timestamp: Date | { toDate: () => Date } | string | null) => {
    if (!timestamp) return '-';
    try {
      const date = typeof timestamp === 'object' && 'toDate' in timestamp 
        ? timestamp.toDate() 
        : new Date(timestamp);
      return date.toLocaleDateString(locale === 'ar' ? 'ar-SA' : 'en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return '-';
    }
  };

  // Get domain averages from assessment data or calculate from domains
  const getDomainAverages = (): Record<string, DomainAverage> => {
    // First try to get from assessment.domainAverages if available
    if (assessment.domainAverages) {
      return assessment.domainAverages;
    }

    // Fallback: calculate from domains data (for backward compatibility)
    const averages: Record<string, DomainAverage> = {};
    
    domains.forEach(domain => {
      const totalSpecs = domain.specifications?.length || 0;
      const ratedSpecs = domain.specifications?.filter(spec => 
        spec.currentRating !== undefined && spec.currentRating !== null
      ).length || 0;
      
      // Calculate average rating (treating unrated as 0%)
      const totalRating = domain.specifications?.reduce((sum, spec) => 
        sum + (spec.currentRating || 0), 0) || 0;
      const average = totalSpecs > 0 ? totalRating / totalSpecs : 0;

      averages[domain.id] = {
        average,
        name: typeof domain.name === 'object' ? domain.name : { en: domain.name, ar: domain.name },
        totalSpecifications: totalSpecs,
        ratedSpecifications: ratedSpecs,
        domainId: domain.id
      };
    });

    return averages;
  };

  const domainAverages = getDomainAverages();

  // Show loading state while criteria is loading
  if (criteriaLoading) {
    return <LoadingSkeleton isRtl={isRtl} />;
  }

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="space-y-8"
    >
      {/* KPIs Section */}
      <Suspense fallback={<div className="h-32 bg-gray-100 rounded-lg animate-pulse" />}>
        <AssessmentKPIs
          domainAverages={domainAverages}
          assessmentCriteria={assessmentCriteria}
          locale={locale}
          onWeightedComplianceClick={() => setShowWeightedModal(true)}
        />
      </Suspense>

      {/* Domain Filter */}
      <DomainFilter
        domainAverages={domainAverages}
        selectedDomains={selectedDomains}
        onDomainsChange={setSelectedDomains}
        locale={locale}
      />

      {/* Charts Section */}
      <Suspense fallback={<div className="h-80 bg-gray-100 rounded-lg animate-pulse" />}>
        <AssessmentCharts
          domainAverages={domainAverages}
          assessmentCriteria={assessmentCriteria}
          locale={locale}
          selectedDomains={selectedDomains}
        />
      </Suspense>

      {/* Assessment Information */}
      <motion.div variants={itemVariants}>
        <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300">
          <CardHeader>
            <CardTitle className={`text-2xl font-bold ${isRtl ? 'text-right font-arabic' : ''}`}>
              {isRtl ? 'معلومات التقييم' : 'Assessment Information'}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Assessment Name */}
              <div className={isRtl ? 'text-right' : ''}>
                <h3 className={`text-lg font-semibold text-gray-900 mb-2 ${isRtl ? 'font-arabic' : ''}`}>
                  {isRtl ? 'اسم التقييم' : 'Assessment Name'}
                </h3>
                <p className={`text-gray-700 text-lg ${isRtl ? 'font-arabic' : ''}`}>
                  {assessment.name?.[locale as keyof typeof assessment.name] || assessment.name?.en}
                </p>
              </div>

              {/* Framework */}
              {framework && (
                <div className={isRtl ? 'text-right' : ''}>
                  <h3 className={`text-lg font-semibold text-gray-900 mb-2 ${isRtl ? 'font-arabic' : ''}`}>
                    {isRtl ? 'الإطار' : 'Framework'}
                  </h3>
                  <p className={`text-gray-700 text-lg ${isRtl ? 'font-arabic' : ''}`}>
                    {getLocalizedName(framework.name)}
                  </p>
                </div>
              )}

              {/* Created Date */}
              <div className={isRtl ? 'text-right' : ''}>
                <h3 className={`text-lg font-semibold text-gray-900 mb-2 ${isRtl ? 'font-arabic' : ''}`}>
                  {isRtl ? 'تاريخ الإنشاء' : 'Created'}
                </h3>
                <p className="text-gray-700 text-lg">
                  {formatDate(assessment.createdAt)}
                </p>
              </div>

              {/* Last Updated */}
              <div className={isRtl ? 'text-right' : ''}>
                <h3 className={`text-lg font-semibold text-gray-900 mb-2 ${isRtl ? 'font-arabic' : ''}`}>
                  {isRtl ? 'آخر تحديث' : 'Last Updated'}
                </h3>
                <p className="text-gray-700 text-lg">
                  {formatDate(assessment.updatedAt)}
                </p>
              </div>

              {/* Total Domains */}
              <div className={isRtl ? 'text-right' : ''}>
                <h3 className={`text-lg font-semibold text-gray-900 mb-2 ${isRtl ? 'font-arabic' : ''}`}>
                  {isRtl ? 'إجمالي المجالات' : 'Total Domains'}
                </h3>
                <p className="text-gray-700 text-lg">
                  {Object.keys(domainAverages).length}
                </p>
              </div>

              {/* Total Specifications */}
              <div className={isRtl ? 'text-right' : ''}>
                <h3 className={`text-lg font-semibold text-gray-900 mb-2 ${isRtl ? 'font-arabic' : ''}`}>
                  {isRtl ? 'إجمالي المواصفات' : 'Total Specifications'}
                </h3>
                <p className="text-gray-700 text-lg">
                  {Object.values(domainAverages).reduce((sum, domain) => sum + domain.totalSpecifications, 0)}
                </p>
              </div>
            </div>

            {/* Description */}
            <div className={isRtl ? 'text-right' : ''}>
              <h3 className={`text-lg font-semibold text-gray-900 mb-2 ${isRtl ? 'font-arabic' : ''}`}>
                {isRtl ? 'الوصف' : 'Description'}
              </h3>
              <p className={`text-gray-700 leading-relaxed ${isRtl ? 'font-arabic' : ''}`}>
                {assessment.description || (isRtl ? 'لا يوجد وصف متاح.' : 'No description available.')}
              </p>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Weighted Compliance Modal */}
      <WeightedComplianceModal
        isOpen={showWeightedModal}
        onClose={() => setShowWeightedModal(false)}
        domainAverages={domainAverages}
        assessmentCriteria={assessmentCriteria}
        locale={locale}
      />
    </motion.div>
  );
} 
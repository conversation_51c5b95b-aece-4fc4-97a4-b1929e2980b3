"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { Filter, X, CheckCircle2 } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";

interface DomainAverage {
  average: number;
  name: { en: string; ar: string };
  totalSpecifications: number;
  ratedSpecifications: number;
  domainId: string;
  weight?: number;
}

interface DomainFilterProps {
  domainAverages: Record<string, DomainAverage>;
  selectedDomains: string[];
  onDomainsChange: (domains: string[]) => void;
  locale: string;
}

export function DomainFilter({ 
  domainAverages, 
  selectedDomains, 
  onDomainsChange, 
  locale 
}: DomainFilterProps) {
  const isRtl = locale === 'ar';
  const [isExpanded, setIsExpanded] = useState(false);

  const allDomainIds = Object.keys(domainAverages);
  const isAllSelected = selectedDomains.length === 0 || selectedDomains.length === allDomainIds.length;

  const handleDomainToggle = (domainId: string) => {
    if (selectedDomains.includes(domainId)) {
      onDomainsChange(selectedDomains.filter(id => id !== domainId));
    } else {
      onDomainsChange([...selectedDomains, domainId]);
    }
  };

  const handleSelectAll = () => {
    onDomainsChange([]);
  };

  const handleClearAll = () => {
    onDomainsChange([]);
  };

  const getSelectedDomainsText = () => {
    if (isAllSelected) {
      return isRtl ? 'جميع المجالات' : 'All Domains';
    }
    if (selectedDomains.length === 1) {
      const domain = domainAverages[selectedDomains[0]];
      return domain?.name[locale as keyof typeof domain.name] || domain?.name.en || selectedDomains[0];
    }
    return isRtl ? `${selectedDomains.length} مجالات محددة` : `${selectedDomains.length} domains selected`;
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="mb-8"
    >
      <Card className="border-0 shadow-lg bg-gradient-to-r from-blue-50 via-indigo-50 to-purple-50">
        <CardContent className="p-6">
          <div className={`flex items-center justify-between ${isRtl ? 'flex-row-reverse' : ''}`}>
            <div className={`flex items-center gap-4 ${isRtl ? 'flex-row-reverse' : ''}`}>
              <div className="p-3 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-xl shadow-lg">
                <Filter className="h-6 w-6 text-white" />
              </div>
              <div className={isRtl ? 'text-right' : ''}>
                <h3 className={`text-lg font-bold text-gray-900 mb-1 ${isRtl ? 'font-arabic' : ''}`}>
                  {isRtl ? 'تصفية المجالات' : 'Domain Filter'}
                </h3>
                <p className={`text-gray-600 text-sm ${isRtl ? 'font-arabic' : ''}`}>
                  {getSelectedDomainsText()}
                </p>
              </div>
            </div>

            <div className={`flex items-center gap-3 ${isRtl ? 'flex-row-reverse' : ''}`}>
              <Badge className="bg-blue-100 text-blue-800 border-blue-200 px-3 py-1">
                {isAllSelected ? allDomainIds.length : selectedDomains.length} / {allDomainIds.length}
              </Badge>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsExpanded(!isExpanded)}
                className={`border-blue-200 hover:bg-blue-50 ${isRtl ? 'flex-row-reverse' : ''}`}
              >
                <Filter className={`h-4 w-4 ${isRtl ? 'ml-2' : 'mr-2'}`} />
                {isExpanded ? (isRtl ? 'إخفاء' : 'Hide') : (isRtl ? 'إظهار' : 'Show')}
              </Button>
            </div>
          </div>

          {isExpanded && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
              className="mt-6 pt-6 border-t border-blue-200"
            >
              <div className={`flex items-center gap-3 mb-4 ${isRtl ? 'flex-row-reverse' : ''}`}>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSelectAll}
                  className="border-emerald-200 hover:bg-emerald-50 text-emerald-700"
                >
                  <CheckCircle2 className={`h-4 w-4 ${isRtl ? 'ml-2' : 'mr-2'}`} />
                  {isRtl ? 'تحديد الكل' : 'Select All'}
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleClearAll}
                  className="border-red-200 hover:bg-red-50 text-red-700"
                >
                  <X className={`h-4 w-4 ${isRtl ? 'ml-2' : 'mr-2'}`} />
                  {isRtl ? 'إلغاء التحديد' : 'Clear All'}
                </Button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3">
                {Object.entries(domainAverages).map(([domainId, domain]) => {
                  const isSelected = selectedDomains.length === 0 || selectedDomains.includes(domainId);
                  const weight = domain.weight || 0;
                  
                  return (
                    <motion.div
                      key={domainId}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <Button
                        variant={isSelected ? "default" : "outline"}
                        className={`w-full p-4 h-auto justify-start ${
                          isSelected 
                            ? 'bg-gradient-to-r from-blue-500 to-indigo-500 text-white border-0 shadow-lg' 
                            : 'border-gray-200 hover:border-blue-300 hover:bg-blue-50'
                        } ${isRtl ? 'flex-row-reverse text-right' : ''}`}
                        onClick={() => handleDomainToggle(domainId)}
                      >
                        <div className={`flex-1 ${isRtl ? 'text-right' : 'text-left'}`}>
                          <div className={`flex items-center gap-2 mb-1 ${isRtl ? 'flex-row-reverse' : ''}`}>
                            <span className={`font-semibold text-sm ${isRtl ? 'font-arabic' : ''}`}>
                              {domain.name[locale as keyof typeof domain.name] || domain.name.en}
                            </span>
                            {isSelected && (
                              <CheckCircle2 className="h-4 w-4 text-white" />
                            )}
                          </div>
                          <div className={`flex items-center gap-2 text-xs ${isRtl ? 'flex-row-reverse' : ''}`}>
                            <span className={isSelected ? 'text-white/80' : 'text-gray-500'}>
                              {domain.average.toFixed(1)}%
                            </span>
                            {weight > 0 && (
                              <span className={isSelected ? 'text-white/70' : 'text-gray-400'}>
                                • {isRtl ? `وزن: ${weight}%` : `Weight: ${weight}%`}
                              </span>
                            )}
                          </div>
                        </div>
                      </Button>
                    </motion.div>
                  );
                })}
              </div>
            </motion.div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
} 
"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useTranslations } from "next-intl";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { 
  collection, 
  addDoc, 
  serverTimestamp 
} from "firebase/firestore";
import { db } from "@/lib/firebaseClient";
import { toast } from "sonner";
import { Framework } from "@/types";

// UI Components
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Loader2, CheckCircle, AlertCircle, Shield, X, Check, Target, FileText } from "lucide-react";

interface AssessmentFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
  projectId: string | null;
  frameworks: Framework[];
  frameworksLoading: boolean;
  locale: string;
}

// Form validation schema
const createAssessmentSchema = z.object({
  nameEn: z.string().min(1, "Assessment name (English) is required"),
  nameAr: z.string().min(1, "Assessment name (Arabic) is required"),
  description: z.string().min(1, "Assessment description is required"),
  frameworkId: z.string().min(1, "Framework selection is required"),
});

type AssessmentFormData = z.infer<typeof createAssessmentSchema>;

export function AssessmentFormModal({
  isOpen,
  onClose,
  onSuccess,
  projectId,
  frameworks,
  frameworksLoading,
  locale
}: AssessmentFormModalProps) {
  const t = useTranslations('ComplianceAssessment');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedFramework, setSelectedFramework] = useState<string>("");
  const isRtl = locale === 'ar';

  const form = useForm<AssessmentFormData>({
    resolver: zodResolver(createAssessmentSchema),
    defaultValues: {
      nameEn: "",
      nameAr: "",
      description: "",
      frameworkId: "",
    },
  });

  const handleSubmit = async (data: AssessmentFormData) => {
    if (!projectId) {
      toast.error(t('projectRequired'));
      return;
    }

    setIsSubmitting(true);
    try {
      // Create assessment document in projects/{projectId}/ComplianceAssessment collection
      const assessmentData = {
        name: {
          en: data.nameEn,
          ar: data.nameAr
        },
        description: data.description,
        frameworkId: data.frameworkId,
        projectId: projectId,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
        status: "active"
      };

      const assessmentRef = collection(db, `projects/${projectId}/ComplianceAssessment`);
      await addDoc(assessmentRef, assessmentData);

      toast.success(t('successCreate'));
      form.reset();
      setSelectedFramework("");
      onSuccess?.();
      onClose();
    } catch (error) {
      console.error('Error creating assessment:', error);
      toast.error(t('errorCreate'));
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      form.reset();
      setSelectedFramework("");
      onClose();
    }
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      handleClose();
    }
  };

  const handleFrameworkSelect = (frameworkId: string) => {
    setSelectedFramework(frameworkId);
    form.setValue('frameworkId', frameworkId, { shouldValidate: true });
  };

  const getSelectedFramework = () => {
    return frameworks.find(f => f.id === selectedFramework);
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[9999]"
            onClick={handleBackdropClick}
          />

          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            transition={{ 
              duration: 0.3,
              type: "spring",
              stiffness: 300,
              damping: 30
            }}
            className="fixed inset-0 z-[10000] flex items-center justify-center p-4"
            onClick={handleBackdropClick}
          >
            <div 
              className="w-full max-w-[800px] max-h-[90vh] overflow-y-auto bg-white border-2 border-gray-200 shadow-2xl rounded-lg"
              onClick={(e) => e.stopPropagation()}
            >
              {/* Header with gradient background */}
              <div className="relative bg-gradient-to-r from-[#003874] via-[#2D8DC6] to-[#48D3A5] px-8 py-6 rounded-t-lg">
                {/* Decorative elements */}
                <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full blur-xl -translate-y-8 translate-x-8" />
                <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/5 rounded-full blur-lg translate-y-4 -translate-x-4" />
                
                {/* Close button */}
                <button
                  onClick={handleClose}
                  disabled={isSubmitting}
                  className={`absolute top-4 ${isRtl ? 'left-4' : 'right-4'} p-2 text-white/80 hover:text-white hover:bg-white/20 rounded-full transition-all duration-200 z-10`}
                >
                  <X className="h-5 w-5" />
                </button>

                <div className="relative z-10 space-y-3">
                  <div className={`flex items-center gap-4 ${isRtl ? 'flex-row-reverse' : ''}`}>
                    <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                      <Shield className="h-8 w-8 text-white" />
                    </div>
                    <div className={isRtl ? 'text-right' : ''}>
                      <h2 className="text-2xl font-bold text-white mb-2">
                        {t('addAssessment')}
                      </h2>
                      <p className="text-white/90 text-base">
                        {t('description')}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Form Content */}
              <div className="px-8 py-6 bg-white">
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
                    {/* Assessment Name (English) */}
                    <FormField
                      control={form.control}
                      name="nameEn"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-base font-semibold text-gray-800 flex items-center gap-2">
                            <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                            {t('assessmentNameEn')}
                          </FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="Enter assessment name in English"
                              className="h-12 text-base border-2 border-gray-200 focus:border-[#003874] focus:ring-0 rounded-lg bg-gray-50/50 transition-all duration-200"
                              disabled={isSubmitting}
                            />
                          </FormControl>
                          <FormMessage className="text-red-500" />
                        </FormItem>
                      )}
                    />

                    {/* Assessment Name (Arabic) */}
                    <FormField
                      control={form.control}
                      name="nameAr"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-base font-semibold text-gray-800 flex items-center gap-2">
                            <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                            {t('assessmentNameAr')}
                          </FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="أدخل اسم التقييم باللغة العربية"
                              className="h-12 text-base text-right border-2 border-gray-200 focus:border-[#003874] focus:ring-0 rounded-lg bg-gray-50/50 transition-all duration-200"
                              dir="rtl"
                              disabled={isSubmitting}
                            />
                          </FormControl>
                          <FormMessage className="text-red-500" />
                        </FormItem>
                      )}
                    />

                    {/* Description */}
                    <FormField
                      control={form.control}
                      name="description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-base font-semibold text-gray-800 flex items-center gap-2">
                            <span className="w-2 h-2 bg-purple-500 rounded-full"></span>
                            {t('assessmentDescription')}
                          </FormLabel>
                          <FormControl>
                            <Textarea
                              {...field}
                              placeholder="Enter a detailed description of the assessment"
                              className="min-h-[120px] text-base resize-none border-2 border-gray-200 focus:border-[#003874] focus:ring-0 rounded-lg bg-gray-50/50 transition-all duration-200"
                              disabled={isSubmitting}
                            />
                          </FormControl>
                          <FormMessage className="text-red-500" />
                        </FormItem>
                      )}
                    />

                    {/* Framework Selection - Button Based */}
                    <FormField
                      control={form.control}
                      name="frameworkId"
                      render={({ field: _field }) => (
                        <FormItem>
                          <FormLabel className="text-base font-semibold text-gray-800 flex items-center gap-2">
                            <span className="w-2 h-2 bg-orange-500 rounded-full"></span>
                            {t('framework')}
                          </FormLabel>
                          
                          <div className="space-y-4">
                            {frameworksLoading ? (
                              <div className="flex items-center justify-center py-8">
                                <div className="flex items-center gap-3 text-gray-600">
                                  <Loader2 className="h-6 w-6 animate-spin text-blue-500" />
                                  <span className="text-lg">{t('loadingFrameworks')}</span>
                                </div>
                              </div>
                            ) : frameworks.length === 0 ? (
                              <div className="flex items-center justify-center py-8">
                                <div className="flex items-center gap-3 text-gray-600">
                                  <AlertCircle className="h-6 w-6 text-yellow-500" />
                                  <span className="text-lg">{t('noFrameworks')}</span>
                                </div>
                              </div>
                            ) : (
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                {frameworks.map((framework) => {
                                  const isSelected = selectedFramework === framework.id;
                                  return (
                                    <motion.button
                                      key={framework.id}
                                      type="button"
                                      onClick={() => handleFrameworkSelect(framework.id)}
                                      disabled={isSubmitting}
                                      whileHover={{ scale: 1.02, y: -2 }}
                                      whileTap={{ scale: 0.98 }}
                                      className={`relative p-6 rounded-xl border-2 transition-all duration-200 text-left ${
                                        isSelected
                                          ? 'border-[#003874] bg-gradient-to-br from-[#003874]/5 to-[#48D3A5]/5 shadow-lg'
                                          : 'border-gray-200 bg-gray-50/50 hover:border-gray-300 hover:bg-gray-100/50'
                                      }`}
                                    >
                                      {/* Selection indicator */}
                                      <div className={`absolute top-4 ${isRtl ? 'left-4' : 'right-4'} w-6 h-6 rounded-full border-2 flex items-center justify-center transition-all duration-200 ${
                                        isSelected
                                          ? 'border-[#003874] bg-[#003874]'
                                          : 'border-gray-300 bg-white'
                                      }`}>
                                        {isSelected && (
                                          <Check className="h-4 w-4 text-white" />
                                        )}
                                      </div>

                                      {/* Framework icon */}
                                      <div className={`mb-4 p-3 rounded-lg inline-flex ${
                                        isSelected
                                          ? 'bg-[#003874]/10 text-[#003874]'
                                          : 'bg-gray-200 text-gray-600'
                                      }`}>
                                        <Target className="h-6 w-6" />
                                      </div>

                                      {/* Framework name */}
                                      <h3 className={`text-lg font-bold mb-2 transition-colors ${
                                        isSelected ? 'text-[#003874]' : 'text-gray-900'
                                      } ${isRtl ? 'font-arabic text-right' : ''}`}>
                                        {framework.name?.[locale as keyof typeof framework.name] || framework.name?.en || framework.id}
                                      </h3>

                                      {/* Framework description */}
                                      {framework.description && (
                                        <p className={`text-sm text-gray-600 line-clamp-2 ${isRtl ? 'text-right font-arabic' : ''}`}>
                                          {framework.description?.[locale as keyof typeof framework.description] || framework.description?.en}
                                        </p>
                                      )}

                                      {/* Selected framework overlay */}
                                      {isSelected && (
                                        <motion.div
                                          initial={{ opacity: 0 }}
                                          animate={{ opacity: 1 }}
                                          className="absolute inset-0 bg-gradient-to-br from-[#003874]/5 to-[#48D3A5]/5 rounded-xl pointer-events-none"
                                        />
                                      )}
                                    </motion.button>
                                  );
                                })}
                              </div>
                            )}

                            {/* Selected framework summary */}
                            {selectedFramework && getSelectedFramework() && (
                              <motion.div
                                initial={{ opacity: 0, y: 10 }}
                                animate={{ opacity: 1, y: 0 }}
                                className="p-4 bg-gradient-to-r from-[#003874]/10 to-[#48D3A5]/10 rounded-lg border border-[#003874]/20"
                              >
                                <div className={`flex items-center gap-3 ${isRtl ? 'flex-row-reverse' : ''}`}>
                                  <FileText className="h-5 w-5 text-[#003874]" />
                                  <div className={isRtl ? 'text-right' : ''}>
                                    <p className="text-sm font-medium text-[#003874]">Selected Framework:</p>
                                                                         <p className={`text-base font-bold text-gray-900 ${isRtl ? 'font-arabic' : ''}`}>
                                       {(() => {
                                         const framework = getSelectedFramework();
                                         return framework?.name?.[locale as keyof typeof framework.name] || framework?.name?.en;
                                       })()}
                                     </p>
                                  </div>
                                </div>
                              </motion.div>
                            )}
                          </div>
                          
                          <FormMessage className="text-red-500" />
                        </FormItem>
                      )}
                    />
                  </form>
                </Form>
              </div>

              {/* Footer */}
              <div className="bg-gray-50 px-8 py-6 border-t border-gray-200 rounded-b-lg">
                <div className={`flex gap-4 w-full ${isRtl ? 'flex-row-reverse' : ''}`}>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleClose}
                    disabled={isSubmitting}
                    className="flex-1 h-12 text-base font-medium border-2 border-gray-300 hover:border-gray-400 hover:bg-gray-100 transition-all duration-200"
                  >
                    {t('cancel')}
                  </Button>
                  <Button
                    onClick={form.handleSubmit(handleSubmit)}
                    disabled={isSubmitting || !projectId || frameworksLoading || !selectedFramework}
                    className="flex-1 h-12 text-base font-medium bg-gradient-to-r from-[#003874] via-[#2D8DC6] to-[#48D3A5] hover:opacity-90 transition-all duration-200 shadow-lg hover:shadow-xl text-white border-0"
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                        {t('saving')}
                      </>
                    ) : (
                      <>
                        <CheckCircle className="mr-2 h-5 w-5" />
                        {t('createAssessment')}
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
} 
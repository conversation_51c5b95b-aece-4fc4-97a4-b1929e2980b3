"use client"

import {
  Toast,
  ToastClose,
  ToastDescription,
  ToastProvider,
  ToastTitle,
  ToastViewport,
} from "@/components/ui/toast"
import { useToast } from "@/components/ui/use-toast"

export type ToasterProps = {
  position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
}

export function Toaster({ position = 'top-right' }: ToasterProps) {
  const { toasts } = useToast()

  return (
    <ToastProvider>
      {toasts.map(function ({ id, title, description, action, ...props }) {
        return (
          <Toast key={id} {...props}>
            <div className="grid gap-1">
              {title && <ToastTitle>{title}</ToastTitle>}
              {description && (
                <ToastDescription>
                  {typeof description === 'string' ? description : null}
                </ToastDescription>
              )}
            </div>
            {action && (
              <div className="mt-2">{action}</div>
            )}
            <ToastClose />
          </Toast>
        )
      })}
      <ToastViewport className={position} />
    </ToastProvider>
  )
} 

"use client";

import { motion } from "framer-motion";
import { ChevronRight, Briefcase, Calendar, AlertCircle } from "lucide-react";
import { Project } from "@/types";
import { useTranslations } from "next-intl";

// Animation variants
const projectCardVariant = {
    hidden: { opacity: 0, y: 50 },
    visible: {
        opacity: 1,
        y: 0,
        transition: {
            type: "spring",
            stiffness: 100,
            damping: 15
        }
    },
    hover: {
        y: -15,
        boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
        transition: {
            type: "spring",
            stiffness: 400,
            damping: 10
        }
    },
    tap: {
        scale: 0.98,
        transition: {
            type: "spring",
            stiffness: 400,
            damping: 17
        }
    }
};

interface ProjectCardProps {
    project: Project;
    onClick: () => void;
    lang: string;
}

export default function ProjectCard({ project, onClick, lang }: ProjectCardProps) {
    const t = useTranslations('ProjectSelection');

    // Get gradient colors based on status
    const getStatusGradient = (status: string) => {
        switch (status) {
            case 'completed':
                return "from-[#48D3A5] via-[#48D3A5] to-[#302C64]";
            case 'in-progress':
                return "from-[#003874] via-[#003874] to-[#2D8DC6]";
            default: // on-holding or other
                return "from-[#31326A] via-[#31326A] to-[#003874]";
        }
    };

    // Get status icon based on status
    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'completed':
                return <Briefcase className="h-10 w-10 text-white" />;
            case 'in-progress':
                return <Calendar className="h-10 w-10 text-white" />;
            default: // on-holding or other
                return <AlertCircle className="h-10 w-10 text-white" />;
        }
    };

    // Format project status for display
    const getStatusTranslation = (status: string) => {
        switch (status) {
            case 'on-holding':
                return t('onHolding');
            case 'in-progress':
                return t('inProgress');
            case 'completed':
                return t('completed');
            default:
                return status;
        }
    };

    return (
        <motion.div
            variants={projectCardVariant}
            initial="hidden"
            animate="visible"
            whileHover="hover"
            whileTap="tap"
            className="relative overflow-hidden rounded-2xl cursor-pointer w-full"
            onClick={onClick}
        >
            <div className={`bg-gradient-to-br ${getStatusGradient(project.status)} p-8 h-full min-h-[320px] relative overflow-hidden`}>
                {/* Decorative elements */}
                <div className="absolute top-0 right-0 w-64 h-64 rounded-full opacity-20 bg-white transform translate-x-1/3 -translate-y-1/3"></div>
                <div className="absolute bottom-0 left-0 w-32 h-32 rounded-full opacity-20 bg-white transform -translate-x-1/3 translate-y-1/3"></div>

                <div className="relative z-10 flex flex-col h-full">
                    <div className="mb-8">
                        <div className="inline-flex items-center justify-center p-3 rounded-xl bg-white/20 backdrop-blur-sm mb-6">
                            {getStatusIcon(project.status)}
                        </div>
                        <h2 className={`text-3xl font-bold text-white mb-4 ${lang === 'ar' ? 'font-arabic text-right' : ''}`}>
                            {lang === 'ar' ? project.name.ar : project.name.en}
                        </h2>
                    </div>

                    {/* Project details */}
                    <div className={`space-y-3 mb-auto ${lang === 'ar' ? 'text-right' : ''}`}>
                        <div className="flex items-start text-white/90">
                            <div className="flex-shrink-0 mt-1">
                                <div className="h-5 w-5 bg-white/20 rounded-full"></div>
                            </div>
                            <p className={`ml-3 ${lang === 'ar' ? 'mr-3 ml-0 font-arabic' : ''}`}>
                                {t('status')}: {getStatusTranslation(project.status)}
                            </p>
                        </div>
                        <div className="flex items-start text-white/90">
                            <div className="flex-shrink-0 mt-1">
                                <div className="h-5 w-5 bg-white/20 rounded-full"></div>
                            </div>
                            <p className={`ml-3 ${lang === 'ar' ? 'mr-3 ml-0 font-arabic' : ''}`}>
                                {t('startDate')}: {project.startDate}
                            </p>
                        </div>
                        <div className="flex items-start text-white/90">
                            <div className="flex-shrink-0 mt-1">
                                <div className="h-5 w-5 bg-white/20 rounded-full"></div>
                            </div>
                            <p className={`ml-3 ${lang === 'ar' ? 'mr-3 ml-0 font-arabic' : ''}`}>
                                {t('deadline')}: {project.projectDeadline}
                            </p>
                        </div>
                    </div>

                    {/* Action button */}
                    <button className={`mt-8 w-full rounded-lg bg-white/90 backdrop-blur-sm text-blue-700 hover:bg-white py-3 px-6 transition-all shadow-md flex items-center justify-center gap-2 font-medium ${lang === 'ar' ? 'font-arabic flex-row-reverse' : ''}`}>
                        <span>{t('viewProject')}</span>
                        <ChevronRight size={18} className={`transition-transform duration-300 ${lang === 'ar' ? 'transform rotate-180' : ''}`} />
                    </button>
                </div>
            </div>
        </motion.div>
    );
} 
"use client";

import { motion } from "framer-motion";
import { useTranslations } from "next-intl";
import { DatabaseAsset } from "@/lib/services/dataAssetsService";
import { DatabaseKPIs } from "./DatabaseKPIs";
import { DatabaseCharts } from "./DatabaseCharts";
import { BarChart3, Loader2 } from "lucide-react";

interface DatabaseAnalyticsProps {
    databases: DatabaseAsset[];
    isRTL?: boolean;
    loading?: boolean;
}

const sectionVariants = {
    hidden: { 
        opacity: 0, 
        y: 20 
    },
    visible: {
        opacity: 1,
        y: 0,
        transition: {
            duration: 0.6,
            staggerChildren: 0.2
        }
    }
};

const titleVariants = {
    hidden: { 
        opacity: 0, 
        x: -30 
    },
    visible: {
        opacity: 1,
        x: 0,
        transition: {
            type: "spring",
            stiffness: 300,
            damping: 30
        }
    }
};

export function DatabaseAnalytics({ databases, isRTL = false, loading = false }: DatabaseAnalyticsProps) {
    const databasesT = useTranslations('Databases.analytics');

    if (loading) {
        return (
            <div className="flex items-center justify-center py-20">
                <div className="text-center">
                    <Loader2 className="h-12 w-12 animate-spin text-[#003874] mx-auto mb-4" />
                    <p className="text-gray-600 text-lg">{databasesT('loadingCharts')}</p>
                </div>
            </div>
        );
    }

    return (
        <motion.div
            variants={sectionVariants}
            initial="hidden"
            animate="visible"
            className="space-y-12"
        >
            {/* Analytics Header */}
            <motion.div 
                variants={titleVariants}
                className={`${isRTL ? 'text-right' : 'text-left'}`}
            >
                <div className={`flex items-center gap-4 mb-6 ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <div className="p-3 bg-gradient-to-r from-[#003874] to-[#2D8DC6] rounded-2xl shadow-lg">
                        <BarChart3 className="h-8 w-8 text-white" />
                    </div>
                    <div>
                        <h2 className={`text-3xl font-bold text-gray-900 mb-2 ${isRTL ? 'font-arabic' : ''}`}>
                            {databasesT('title')}
                        </h2>
                        <p className={`text-gray-600 text-lg ${isRTL ? 'font-arabic' : ''}`}>
                            Real-time insights and metrics from your database inventory
                        </p>
                    </div>
                </div>
            </motion.div>

            {/* KPIs Section */}
            <motion.section variants={sectionVariants}>
                <motion.div 
                    variants={titleVariants}
                    className={`mb-8 ${isRTL ? 'text-right' : 'text-left'}`}
                >
                    <h3 className={`text-2xl font-bold text-gray-800 mb-2 ${isRTL ? 'font-arabic' : ''}`}>
                        Key Performance Indicators
                    </h3>
                    <p className={`text-gray-600 ${isRTL ? 'font-arabic' : ''}`}>
                        Overview of your database health and distribution metrics
                    </p>
                </motion.div>
                
                <DatabaseKPIs 
                    databases={databases} 
                    isRTL={isRTL} 
                    loading={loading} 
                />
            </motion.section>

            {/* Charts Section */}
            <motion.section variants={sectionVariants}>
                <motion.div 
                    variants={titleVariants}
                    className={`mb-8 ${isRTL ? 'text-right' : 'text-left'}`}
                >
                    <h3 className={`text-2xl font-bold text-gray-800 mb-2 ${isRTL ? 'font-arabic' : ''}`}>
                        Distribution Analytics
                    </h3>
                    <p className={`text-gray-600 ${isRTL ? 'font-arabic' : ''}`}>
                        Visual breakdown of databases by status, DBMS, and technology
                    </p>
                </motion.div>
                
                <DatabaseCharts 
                    databases={databases} 
                    isRTL={isRTL} 
                    loading={loading} 
                />
            </motion.section>

            {/* Additional Insights Section (if we have data) */}
            {databases.length > 0 && (
                <motion.section variants={sectionVariants}>
                    <div className="bg-gradient-to-r from-slate-50 via-blue-50 to-indigo-50 rounded-2xl p-8 border border-blue-100">
                        <motion.div 
                            variants={titleVariants}
                            className={`${isRTL ? 'text-right' : 'text-left'}`}
                        >
                            <h3 className={`text-xl font-bold text-gray-800 mb-4 ${isRTL ? 'font-arabic' : ''}`}>
                                Database Insights
                            </h3>
                            
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                {/* Health Status Insight */}
                                <div className="bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-white/50">
                                    <div className={`${isRTL ? 'text-right' : 'text-left'}`}>
                                        <h4 className={`font-semibold text-gray-800 mb-2 ${isRTL ? 'font-arabic' : ''}`}>
                                            Database Health
                                        </h4>
                                        <p className={`text-sm text-gray-600 leading-relaxed ${isRTL ? 'font-arabic' : ''}`}>
                                            {databases.filter(d => d.status === 'Online').length} out of {databases.length} databases 
                                            are currently online and operational, representing{' '}
                                            {Math.round((databases.filter(d => d.status === 'Online').length / databases.length) * 100)}% 
                                            database availability.
                                        </p>
                                    </div>
                                </div>

                                {/* DBMS Coverage Insight */}
                                <div className="bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-white/50">
                                    <div className={`${isRTL ? 'text-right' : 'text-left'}`}>
                                        <h4 className={`font-semibold text-gray-800 mb-2 ${isRTL ? 'font-arabic' : ''}`}>
                                            DBMS Diversity
                                        </h4>
                                        <p className={`text-sm text-gray-600 leading-relaxed ${isRTL ? 'font-arabic' : ''}`}>
                                            Your organization uses {new Set(databases.map(d => d.dbms)).size} different DBMS technologies, 
                                            providing flexibility and specialized capabilities for various data requirements.
                                        </p>
                                    </div>
                                </div>

                                {/* Storage Distribution Insight */}
                                <div className="bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-white/50">
                                    <div className={`${isRTL ? 'text-right' : 'text-left'}`}>
                                        <h4 className={`font-semibold text-gray-800 mb-2 ${isRTL ? 'font-arabic' : ''}`}>
                                            Storage Technology
                                        </h4>
                                        <p className={`text-sm text-gray-600 leading-relaxed ${isRTL ? 'font-arabic' : ''}`}>
                                            Utilizing {new Set(databases.map(d => d.technology)).size} different storage technologies 
                                            to optimize performance, cost, and reliability across your database infrastructure.
                                        </p>
                                    </div>
                                </div>

                                {/* Risk Assessment Insight */}
                                <div className="bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-white/50">
                                    <div className={`${isRTL ? 'text-right' : 'text-left'}`}>
                                        <h4 className={`font-semibold text-gray-800 mb-2 ${isRTL ? 'font-arabic' : ''}`}>
                                            Risk Assessment
                                        </h4>
                                        <p className={`text-sm text-gray-600 leading-relaxed ${isRTL ? 'font-arabic' : ''}`}>
                                            {databases.filter(d => d.status === 'Offline' || d.status === 'Maintenance').length} databases 
                                            require attention, representing{' '}
                                            {Math.round((databases.filter(d => d.status === 'Offline' || d.status === 'Maintenance').length / databases.length) * 100)}% 
                                            of your total database inventory.
                                        </p>
                                    </div>
                                </div>

                                {/* Location Distribution Insight */}
                                <div className="bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-white/50">
                                    <div className={`${isRTL ? 'text-right' : 'text-left'}`}>
                                        <h4 className={`font-semibold text-gray-800 mb-2 ${isRTL ? 'font-arabic' : ''}`}>
                                            Location Coverage
                                        </h4>
                                        <p className={`text-sm text-gray-600 leading-relaxed ${isRTL ? 'font-arabic' : ''}`}>
                                            Databases are distributed across {new Set(databases.map(d => d.databaseLocation)).size} different locations,
                                            ensuring geographic redundancy and compliance with data locality requirements.
                                        </p>
                                    </div>
                                </div>

                                {/* Performance Optimization Insight */}
                                <div className="bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-white/50">
                                    <div className={`${isRTL ? 'text-right' : 'text-left'}`}>
                                        <h4 className={`font-semibold text-gray-800 mb-2 ${isRTL ? 'font-arabic' : ''}`}>
                                            Performance Status
                                        </h4>
                                        <p className={`text-sm text-gray-600 leading-relaxed ${isRTL ? 'font-arabic' : ''}`}>
                                            Based on current status distribution, your database infrastructure shows{' '}
                                            {databases.filter(d => d.status === 'Online').length >= databases.length * 0.8 ? 'excellent' : 
                                             databases.filter(d => d.status === 'Online').length >= databases.length * 0.6 ? 'good' : 'needs attention'} 
                                            {' '}health with opportunities for optimization and monitoring.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </motion.div>
                    </div>
                </motion.section>
            )}
        </motion.div>
    );
} 
"use client";

import { motion } from "framer-motion";
import { useTranslations } from "next-intl";
import { 
    <PERSON><PERSON><PERSON>, 
    <PERSON>, 
    Cell, 
    <PERSON><PERSON>hart, 
    Bar, 
    XAxis, 
    <PERSON>A<PERSON>s, 
    CartesianGrid, 
    Responsive<PERSON><PERSON>r,
    <PERSON>,
    Tooltip
} from "recharts";
import { DatabaseAsset } from "@/lib/services/dataAssetsService";

interface DatabaseChartsProps {
    databases: DatabaseAsset[];
    isRTL?: boolean;
    loading?: boolean;
}

// Color schemes for different chart types
const COLORS = {
    status: {
        'Online': '#10B981',      // Green - Good
        'Offline': '#EF4444',     // Red - Bad
        'Maintenance': '#F59E0B', // Yellow - Warning
        'Archived': '#6B7280'     // Gray - Neutral
    },
    dbms: ['#003874', '#2D8DC6', '#48D3A5', '#8B5CF6', '#F59E0B', '#EF4444', '#06B6D4', '#84CC16'],
    technologies: ['#6366F1', '#8B5CF6', '#EC4899', '#F59E0B', '#10B981', '#06B6D4', '#84CC16', '#F97316']
};

// Animation variants
const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
        opacity: 1,
        transition: {
            staggerChildren: 0.2,
            delayChildren: 0.3
        }
    }
};

const cardVariants = {
    hidden: { 
        opacity: 0, 
        y: 30,
        scale: 0.95
    },
    visible: {
        opacity: 1,
        y: 0,
        scale: 1,
        transition: {
            type: "spring",
            stiffness: 300,
            damping: 30,
            duration: 0.6
        }
    }
};

export function DatabaseCharts({ databases, isRTL = false, loading = false }: DatabaseChartsProps) {
    const databasesT = useTranslations('Databases.analytics');

    // Process data for charts
    const processChartData = () => {
        // Status distribution
        const statusCounts = databases.reduce((acc, database) => {
            acc[database.status] = (acc[database.status] || 0) + 1;
            return acc;
        }, {} as Record<string, number>);

        const statusData = Object.entries(statusCounts).map(([status, count]) => ({
            name: databasesT(`statusLabels.${status}`),
            value: count,
            color: COLORS.status[status as keyof typeof COLORS.status] || '#6B7280'
        }));

        // DBMS distribution
        const dbmsCounts = databases.reduce((acc, database) => {
            const dbms = database.dbms || 'Unknown';
            acc[dbms] = (acc[dbms] || 0) + 1;
            return acc;
        }, {} as Record<string, number>);

        const dbmsData = Object.entries(dbmsCounts)
            .map(([dbms, count], index) => ({
                name: dbms,
                value: count,
                color: COLORS.dbms[index % COLORS.dbms.length]
            }))
            .sort((a, b) => b.value - a.value);

        // Technology distribution 
        const technologyCounts = databases.reduce((acc, database) => {
            const tech = database.technology || 'Unknown';
            acc[tech] = (acc[tech] || 0) + 1;
            return acc;
        }, {} as Record<string, number>);

        const technologyData = Object.entries(technologyCounts)
            .map(([technology, count], index) => ({
                name: technology,
                value: count,
                color: COLORS.technologies[index % COLORS.technologies.length]
            }))
            .sort((a, b) => b.value - a.value);

        return {
            statusData,
            dbmsData,
            technologyData
        };
    };

    const { statusData, dbmsData, technologyData } = processChartData();

    // Custom tooltip component
    const CustomTooltip = ({ active, payload, label }: any) => {
        if (active && payload && payload.length) {
            return (
                <div className="bg-white p-3 rounded-lg shadow-lg border border-gray-200">
                    <p className="font-semibold text-gray-800">{`${payload[0].payload.name}: ${payload[0].value}`}</p>
                    <p className="text-sm text-gray-600">
                        {((payload[0].value / databases.length) * 100).toFixed(1)}% of total
                    </p>
                </div>
            );
        }
        return null;
    };

    // Custom label for pie charts
    const renderCustomLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }: any) => {
        if (percent < 0.05) return null; // Don't show labels for slices < 5%
        
        const RADIAN = Math.PI / 180;
        const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
        const x = cx + radius * Math.cos(-midAngle * RADIAN);
        const y = cy + radius * Math.sin(-midAngle * RADIAN);

        return (
            <text 
                x={x} 
                y={y} 
                fill="white" 
                textAnchor={x > cx ? 'start' : 'end'} 
                dominantBaseline="central"
                fontSize={12}
                fontWeight="bold"
            >
                {`${(percent * 100).toFixed(0)}%`}
            </text>
        );
    };

    if (loading) {
        return (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {[...Array(3)].map((_, index) => (
                    <div
                        key={index}
                        className="h-80 bg-gradient-to-br from-slate-200 to-slate-300 rounded-2xl animate-pulse"
                    />
                ))}
            </div>
        );
    }

    if (databases.length === 0) {
        return (
            <div className="text-center py-12">
                <div className="text-gray-500 text-lg">{databasesT('noChartData')}</div>
                <p className="text-gray-400 text-sm mt-2">Add some databases to see charts and analytics</p>
            </div>
        );
    }

    return (
        <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="space-y-8"
        >
            {/* Status Distribution Chart */}
            <motion.div variants={cardVariants}>
                <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
                    <div className="bg-gradient-to-r from-[#003874] to-[#2D8DC6] p-6">
                        <h3 className={`text-xl font-bold text-white ${isRTL ? 'text-right font-arabic' : 'text-left'}`}>
                            {databasesT('charts.statusDistribution')}
                        </h3>
                        <p className={`text-white/80 text-sm mt-1 ${isRTL ? 'text-right font-arabic' : 'text-left'}`}>
                            Distribution of databases by operational status
                        </p>
                    </div>
                    <div className="p-6">
                        <ResponsiveContainer width="100%" height={300}>
                            <PieChart>
                                <Pie
                                    data={statusData}
                                    cx="50%"
                                    cy="50%"
                                    labelLine={false}
                                    label={renderCustomLabel}
                                    outerRadius={100}
                                    fill="#8884d8"
                                    dataKey="value"
                                    animationBegin={0}
                                    animationDuration={1000}
                                >
                                    {statusData.map((entry, index) => (
                                        <Cell key={`cell-${index}`} fill={entry.color} />
                                    ))}
                                </Pie>
                                <Tooltip content={<CustomTooltip />} />
                                <Legend 
                                    wrapperStyle={{ 
                                        paddingTop: '20px',
                                        fontSize: '14px',
                                        fontWeight: '500'
                                    }}
                                />
                            </PieChart>
                        </ResponsiveContainer>
                    </div>
                </div>
            </motion.div>

            {/* DBMS Distribution Chart */}
            <motion.div variants={cardVariants}>
                <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
                    <div className="bg-gradient-to-r from-[#8B5CF6] to-[#A78BFA] p-6">
                        <h3 className={`text-xl font-bold text-white ${isRTL ? 'text-right font-arabic' : 'text-left'}`}>
                            {databasesT('charts.dbmsDistribution')}
                        </h3>
                        <p className={`text-white/80 text-sm mt-1 ${isRTL ? 'text-right font-arabic' : 'text-left'}`}>
                            Distribution of databases by DBMS type
                        </p>
                    </div>
                    <div className="p-6">
                        <ResponsiveContainer width="100%" height={300}>
                            <BarChart data={dbmsData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                                <XAxis 
                                    dataKey="name" 
                                    tick={{ fontSize: 12, fill: '#6B7280' }}
                                    tickLine={{ stroke: '#D1D5DB' }}
                                />
                                <YAxis 
                                    tick={{ fontSize: 12, fill: '#6B7280' }}
                                    tickLine={{ stroke: '#D1D5DB' }}
                                />
                                <Tooltip content={<CustomTooltip />} />
                                <Bar 
                                    dataKey="value" 
                                    radius={[4, 4, 0, 0]}
                                    animationDuration={1000}
                                >
                                    {dbmsData.map((entry, index) => (
                                        <Cell key={`cell-${index}`} fill={entry.color} />
                                    ))}
                                </Bar>
                            </BarChart>
                        </ResponsiveContainer>
                    </div>
                </div>
            </motion.div>

            {/* Technology Distribution Chart */}
            <motion.div variants={cardVariants}>
                <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
                    <div className="bg-gradient-to-r from-[#06B6D4] to-[#67E8F9] p-6">
                        <h3 className={`text-xl font-bold text-white ${isRTL ? 'text-right font-arabic' : 'text-left'}`}>
                            {databasesT('charts.technologyDistribution')}
                        </h3>
                        <p className={`text-white/80 text-sm mt-1 ${isRTL ? 'text-right font-arabic' : 'text-left'}`}>
                            Distribution of databases by technology type
                        </p>
                    </div>
                    <div className="p-6">
                        <ResponsiveContainer width="100%" height={300}>
                            <PieChart>
                                <Pie
                                    data={technologyData}
                                    cx="50%"
                                    cy="50%"
                                    labelLine={false}
                                    label={renderCustomLabel}
                                    outerRadius={100}
                                    fill="#8884d8"
                                    dataKey="value"
                                    animationBegin={0}
                                    animationDuration={1200}
                                >
                                    {technologyData.map((entry, index) => (
                                        <Cell key={`cell-${index}`} fill={entry.color} />
                                    ))}
                                </Pie>
                                <Tooltip content={<CustomTooltip />} />
                                <Legend 
                                    wrapperStyle={{ 
                                        paddingTop: '20px',
                                        fontSize: '14px',
                                        fontWeight: '500'
                                    }}
                                />
                            </PieChart>
                        </ResponsiveContainer>
                    </div>
                </div>
            </motion.div>

            {/* Performance Metrics Summary */}
            <motion.div variants={cardVariants}>
                <div className="bg-gradient-to-r from-slate-50 via-blue-50 to-indigo-50 rounded-2xl p-8 border border-blue-100">
                    <h3 className={`text-xl font-bold text-gray-800 mb-6 ${isRTL ? 'text-right font-arabic' : 'text-left'}`}>
                        Database Distribution Summary
                    </h3>
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                        {/* Status Health */}
                        <div className="bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-white/50">
                            <div className={`${isRTL ? 'text-right' : 'text-left'}`}>
                                <h4 className={`font-semibold text-gray-800 mb-2 ${isRTL ? 'font-arabic' : ''}`}>
                                    Status Health
                                </h4>
                                <p className={`text-sm text-gray-600 leading-relaxed ${isRTL ? 'font-arabic' : ''}`}>
                                    {databases.filter(d => d.status === 'Online').length} out of {databases.length} databases 
                                    are currently online, representing{' '}
                                    {Math.round((databases.filter(d => d.status === 'Online').length / databases.length) * 100)}% 
                                    availability.
                                </p>
                            </div>
                        </div>

                        {/* DBMS Diversity */}
                        <div className="bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-white/50">
                            <div className={`${isRTL ? 'text-right' : 'text-left'}`}>
                                <h4 className={`font-semibold text-gray-800 mb-2 ${isRTL ? 'font-arabic' : ''}`}>
                                    DBMS Diversity
                                </h4>
                                <p className={`text-sm text-gray-600 leading-relaxed ${isRTL ? 'font-arabic' : ''}`}>
                                    Using {new Set(databases.map(d => d.dbms)).size} different DBMS types 
                                    across your database infrastructure, providing technology diversity.
                                </p>
                            </div>
                        </div>

                        {/* Technology Mix */}
                        <div className="bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-white/50">
                            <div className={`${isRTL ? 'text-right' : 'text-left'}`}>
                                <h4 className={`font-semibold text-gray-800 mb-2 ${isRTL ? 'font-arabic' : ''}`}>
                                    Technology Mix
                                </h4>
                                <p className={`text-sm text-gray-600 leading-relaxed ${isRTL ? 'font-arabic' : ''}`}>
                                    Leveraging {new Set(databases.map(d => d.technology)).size} different technologies 
                                    to optimize performance and meet diverse business requirements.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </motion.div>
        </motion.div>
    );
} 
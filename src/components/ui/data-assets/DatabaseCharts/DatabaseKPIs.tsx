"use client";

import { motion } from "framer-motion";
import { useTranslations } from "next-intl";
import { 
    Database, 
    Activity, 
    Shield, 
    AlertTriangle,
    TrendingUp,
    HardDrive,
    Server
} from "lucide-react";
import { DatabaseAsset } from "@/lib/services/dataAssetsService";

interface DatabaseKPIsProps {
    databases: DatabaseAsset[];
    isRTL?: boolean;
    loading?: boolean;
}

interface KPIData {
    key: string;
    value: number | string;
    label: string;
    description: string;
    icon: React.ElementType;
    gradientFrom: string;
    gradientTo: string;
    textColor: string;
    percentage?: number;
    trend?: 'up' | 'down' | 'stable';
}

const cardVariants = {
    hidden: { 
        opacity: 0, 
        y: 30,
        scale: 0.95
    },
    visible: {
        opacity: 1,
        y: 0,
        scale: 1,
        transition: {
            type: "spring",
            stiffness: 300,
            damping: 30,
            duration: 0.6
        }
    }
};

const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
        opacity: 1,
        transition: {
            staggerChildren: 0.1,
            delayChildren: 0.2
        }
    }
};

export function DatabaseKPIs({ databases, isRTL = false, loading = false }: DatabaseKPIsProps) {
    const databasesT = useTranslations('Databases.analytics');

    // Calculate KPI data from real databases
    const calculateKPIs = (): KPIData[] => {
        const totalDatabases = databases.length;
        const onlineDatabases = databases.filter(d => d.status === 'Online').length;
        const offlineDatabases = databases.filter(d => d.status === 'Offline').length;
        const maintenanceDatabases = databases.filter(d => d.status === 'Maintenance').length;
        const archivedDatabases = databases.filter(d => d.status === 'Archived').length;
        
        // Calculate health percentage (Online databases are healthy)
        const healthPercentage = totalDatabases > 0 ? Math.round((onlineDatabases / totalDatabases) * 100) : 0;
        
        // Risk databases = Offline + Maintenance
        const riskDatabases = offlineDatabases + maintenanceDatabases;
        
        // Unique DBMS count
        const uniqueDbms = new Set(databases.map(d => d.dbms)).size;
        
        // Unique technologies count
        const uniqueTechnologies = new Set(databases.map(d => d.technology)).size;

        return [
            {
                key: 'totalDatabases',
                value: totalDatabases,
                label: databasesT('kpis.totalDatabases'),
                description: databasesT('insights.totalDatabasesDesc'),
                icon: Database,
                gradientFrom: '#003874',
                gradientTo: '#2D8DC6',
                textColor: 'text-white',
                trend: 'stable'
            },
            {
                key: 'onlineDatabases',
                value: onlineDatabases,
                label: databasesT('kpis.onlineDatabases'),
                description: databasesT('insights.onlineDatabasesDesc'),
                icon: Activity,
                gradientFrom: '#10B981',
                gradientTo: '#34D399',
                textColor: 'text-white',
                percentage: totalDatabases > 0 ? Math.round((onlineDatabases / totalDatabases) * 100) : 0,
                trend: 'up'
            },
            {
                key: 'databaseHealth',
                value: `${healthPercentage}%`,
                label: databasesT('kpis.databaseHealth'),
                description: databasesT('insights.databaseHealthDesc'),
                icon: Shield,
                gradientFrom: healthPercentage >= 80 ? '#10B981' : healthPercentage >= 60 ? '#F59E0B' : '#EF4444',
                gradientTo: healthPercentage >= 80 ? '#34D399' : healthPercentage >= 60 ? '#FCD34D' : '#F87171',
                textColor: 'text-white',
                percentage: healthPercentage,
                trend: healthPercentage >= 80 ? 'up' : healthPercentage >= 60 ? 'stable' : 'down'
            },
            {
                key: 'riskDatabases',
                value: riskDatabases,
                label: databasesT('kpis.riskDatabases'),
                description: databasesT('insights.riskDatabasesDesc'),
                icon: AlertTriangle,
                gradientFrom: riskDatabases === 0 ? '#10B981' : riskDatabases <= totalDatabases * 0.2 ? '#F59E0B' : '#EF4444',
                gradientTo: riskDatabases === 0 ? '#34D399' : riskDatabases <= totalDatabases * 0.2 ? '#FCD34D' : '#F87171',
                textColor: 'text-white',
                percentage: totalDatabases > 0 ? Math.round((riskDatabases / totalDatabases) * 100) : 0,
                trend: riskDatabases === 0 ? 'up' : 'down'
            },
            {
                key: 'databasesByDbms',
                value: uniqueDbms,
                label: databasesT('kpis.databasesByDbms'),
                description: 'Number of unique DBMS types in use',
                icon: HardDrive,
                gradientFrom: '#8B5CF6',
                gradientTo: '#A78BFA',
                textColor: 'text-white',
                trend: 'stable'
            },
            {
                key: 'databasesByTechnology',
                value: uniqueTechnologies,
                label: databasesT('kpis.databasesByTechnology'),
                description: 'Number of different technologies used',
                icon: Server,
                gradientFrom: '#06B6D4',
                gradientTo: '#67E8F9',
                textColor: 'text-white',
                trend: 'stable'
            }
        ];
    };

    const kpiData = calculateKPIs();

    if (loading) {
        return (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {[...Array(6)].map((_, index) => (
                    <div
                        key={index}
                        className="h-40 bg-gradient-to-br from-slate-200 to-slate-300 rounded-2xl animate-pulse"
                    />
                ))}
            </div>
        );
    }

    return (
        <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
        >
            {kpiData.map((kpi, index) => {
                const Icon = kpi.icon;
                
                return (
                    <motion.div
                        key={kpi.key}
                        variants={cardVariants}
                        className="group relative overflow-hidden"
                    >
                        {/* Card with hero-style gradient background */}
                        <div 
                            className="relative rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:scale-105 h-40"
                            style={{
                                background: `linear-gradient(135deg, ${kpi.gradientFrom} 0%, ${kpi.gradientTo} 100%)`
                            }}
                        >
                            {/* Hero-style decorative elements */}
                            <motion.div
                                className={`absolute top-0 w-32 h-32 rounded-full opacity-20 bg-white ${isRTL ? 'left-0' : 'right-0'}`}
                                initial={{ x: isRTL ? -50 : 50, y: -50 }}
                                animate={{
                                    x: 0,
                                    y: 0,
                                    scale: [1, 1.2, 1],
                                    rotate: [0, 45, 0],
                                }}
                                transition={{ duration: 20, repeat: Infinity, repeatType: "reverse" }}
                            />
                            <motion.div
                                className={`absolute bottom-0 w-16 h-16 rounded-full opacity-20 bg-white ${isRTL ? 'right-1/4' : 'left-1/4'}`}
                                initial={{ y: 25 }}
                                animate={{
                                    y: [0, 10, 0],
                                    scale: [1, 1.1, 1],
                                }}
                                transition={{ duration: 8, repeat: Infinity, repeatType: "reverse" }}
                            />
                            <motion.div
                                className="absolute top-1/3 right-1/4 w-24 h-24 rounded-full opacity-10 bg-white"
                                initial={{ y: -10 }}
                                animate={{
                                    y: [0, -15, 0],
                                    scale: [1, 1.2, 1],
                                }}
                                transition={{ duration: 12, repeat: Infinity, repeatType: "reverse" }}
                            />

                            {/* Content */}
                            <div className="relative z-10 p-6 h-full flex flex-col justify-between">
                                {/* Header with icon and trend */}
                                <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                                    <div className="p-3 bg-white/20 backdrop-blur-md rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
                                        <Icon className="h-6 w-6 text-white" />
                                    </div>
                                    
                                    {kpi.trend && (
                                        <div className={`flex items-center gap-1 bg-white/20 backdrop-blur-md px-2 py-1 rounded-lg ${isRTL ? 'flex-row-reverse' : ''}`}>
                                            <TrendingUp 
                                                className={`h-4 w-4 text-white ${
                                                    kpi.trend === 'down' ? 'rotate-180' : 
                                                    kpi.trend === 'stable' ? 'rotate-90' : ''
                                                }`} 
                                            />
                                        </div>
                                    )}
                                </div>

                                {/* Value and label */}
                                <div className={`${isRTL ? 'text-right' : 'text-left'}`}>
                                    <div className="flex items-baseline gap-2 mb-1">
                                        <motion.span 
                                            className={`text-3xl font-black ${kpi.textColor} ${isRTL ? 'font-arabic' : ''}`}
                                            initial={{ scale: 0.8, opacity: 0 }}
                                            animate={{ scale: 1, opacity: 1 }}
                                            transition={{ delay: 0.3 + index * 0.1 }}
                                        >
                                            {kpi.value}
                                        </motion.span>
                                        {kpi.percentage !== undefined && kpi.key !== 'databaseHealth' && (
                                            <span className="text-sm font-medium text-white/80">
                                                ({kpi.percentage}%)
                                            </span>
                                        )}
                                    </div>
                                    <motion.h3 
                                        className={`text-sm font-bold text-white mb-1 ${isRTL ? 'font-arabic' : ''}`}
                                        initial={{ y: 10, opacity: 0 }}
                                        animate={{ y: 0, opacity: 1 }}
                                        transition={{ delay: 0.4 + index * 0.1 }}
                                    >
                                        {kpi.label}
                                    </motion.h3>
                                    <motion.p 
                                        className={`text-xs text-white/70 leading-relaxed ${isRTL ? 'font-arabic' : ''}`}
                                        initial={{ y: 10, opacity: 0 }}
                                        animate={{ y: 0, opacity: 1 }}
                                        transition={{ delay: 0.5 + index * 0.1 }}
                                    >
                                        {kpi.description}
                                    </motion.p>
                                </div>
                            </div>

                            {/* Subtle hover effect */}
                            <div className="absolute inset-0 rounded-2xl bg-white/5 opacity-0 group-hover:opacity-100 transition-all duration-500 pointer-events-none" />
                        </div>
                    </motion.div>
                );
            })}
        </motion.div>
    );
} 
"use client";

import { useState, useMemo } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useTranslations } from "next-intl";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { 
    Search, 
    Plus, 
    Edit, 
    Trash2, 
    Eye, 
    Upload,
    Clock,
    Shield,
    Database,
    Server,
    User,
    Calendar,
    HardDrive,
    FileText,
    Filter,
    SortAsc,
    SortDesc,
    MoreVertical
} from "lucide-react";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ExcelUploadModal } from "./ExcelUploadModal";
import { TableColumn } from "./AssetTable";
import { DatasetAsset, DatabaseAsset, SystemAsset } from "@/lib/services/dataAssetsService";
import { AddEditDatasetModal } from "./AddEditDatasetModal";

interface LongDataAssetsProps {
    datasets: DatasetAsset[];
    databases: DatabaseAsset[];
    systems: SystemAsset[];
    loading: boolean;
    error: string | null;
    onAdd: (dataset: Partial<DatasetAsset>) => Promise<void>;
    onEdit: (dataset: DatasetAsset, updates: Partial<DatasetAsset>) => Promise<void>;
    onDelete: (dataset: DatasetAsset) => Promise<void>;
    onView: (dataset: DatasetAsset) => void;
    onBulkImport: (datasets: Partial<DatasetAsset>[]) => Promise<void>;
    searchPlaceholder: string;
    addButtonLabel: string;
    emptyMessage: string;
    emptyDescription: string;
    isRTL?: boolean;
    assetType: string;
    templateFileName: string;
}

// Animation variants
const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
        opacity: 1,
        transition: {
            duration: 0.3,
            staggerChildren: 0.1
        }
    }
};

const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
        opacity: 1,
        y: 0,
        transition: {
            duration: 0.3
        }
    }
};

// Update frequency icons
const updateFrequencyIcons = {
    'Real-time': Clock,
    'Daily': Calendar,
    'Weekly': Calendar,
    'Monthly': Calendar,
    'Quarterly': Calendar,
    'Annually': Calendar
};

// Sensitivity colors
const sensitivityColors = {
    'Public': 'bg-green-100 text-green-800',
    'Internal': 'bg-blue-100 text-blue-800',
    'Confidential': 'bg-yellow-100 text-yellow-800',
    'Restricted': 'bg-red-100 text-red-800'
};

export function LongDataAssets({
    datasets,
    databases,
    systems,
    loading,
    error,
    onAdd,
    onEdit,
    onDelete,
    onView,
    onBulkImport,
    searchPlaceholder,
    addButtonLabel,
    emptyMessage,
    emptyDescription,
    isRTL = false,
    assetType,
    templateFileName
}: LongDataAssetsProps) {
    const [searchTerm, setSearchTerm] = useState("");
    const [sortField, setSortField] = useState<keyof DatasetAsset>("name");
    const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");
    const [selectedSensitivity, setSelectedSensitivity] = useState<string>("");
    const [selectedFrequency, setSelectedFrequency] = useState<string>("");
    const [isAddModalOpen, setIsAddModalOpen] = useState(false);
    const [isEditModalOpen, setIsEditModalOpen] = useState(false);
    const [isExcelModalOpen, setIsExcelModalOpen] = useState(false);
    const [editingDataset, setEditingDataset] = useState<DatasetAsset | null>(null);

    const datasetsT = useTranslations('Datasets');

    // Define dataset columns for Excel template
    const datasetColumns: TableColumn<DatasetAsset>[] = [
        {
            key: 'name',
            label: 'Dataset Name',
            sortable: true,
            editable: true,
            type: 'text',
            required: true,
            placeholder: 'Enter dataset name',
            width: 'w-[200px]',
            priority: 'high'
        },
        {
            key: 'description',
            label: 'Description',
            sortable: false,
            editable: true,
            type: 'text',
            required: true,
            placeholder: 'Enter description',
            width: 'w-[300px]',
            priority: 'high'
        },
        {
            key: 'owner',
            label: 'Owner',
            sortable: true,
            editable: true,
            type: 'text',
            required: true,
            placeholder: 'Enter owner name',
            width: 'w-[150px]',
            priority: 'medium'
        },
        {
            key: 'size',
            label: 'Size',
            sortable: true,
            editable: true,
            type: 'text',
            required: true,
            placeholder: 'e.g., 10GB, 500MB',
            width: 'w-[120px]',
            priority: 'medium'
        },
        {
            key: 'format',
            label: 'Format',
            sortable: true,
            editable: true,
            type: 'text',
            required: true,
            placeholder: 'e.g., CSV, JSON, XML',
            width: 'w-[120px]',
            priority: 'medium'
        },
        {
            key: 'updateFrequency',
            label: 'Update Frequency',
            sortable: true,
            editable: true,
            type: 'select',
            required: true,
            options: ['Real-time', 'Daily', 'Weekly', 'Monthly', 'Quarterly', 'Annually'],
            width: 'w-[140px]',
            priority: 'low'
        },
        {
            key: 'sensitivity',
            label: 'Sensitivity',
            sortable: true,
            editable: true,
            type: 'select',
            required: true,
            options: ['Public', 'Internal', 'Confidential', 'Restricted'],
            width: 'w-[120px]',
            priority: 'low'
        }
    ];

    // Filter and sort datasets
    const filteredAndSortedDatasets = useMemo(() => {
        let filtered = datasets.filter(dataset => {
            const matchesSearch = dataset.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                                dataset.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                                dataset.owner.toLowerCase().includes(searchTerm.toLowerCase());
            
            const matchesSensitivity = !selectedSensitivity || dataset.sensitivity === selectedSensitivity;
            const matchesFrequency = !selectedFrequency || dataset.updateFrequency === selectedFrequency;
            
            return matchesSearch && matchesSensitivity && matchesFrequency;
        });

        // Sort datasets
        filtered.sort((a, b) => {
            const aValue = a[sortField];
            const bValue = b[sortField];
            
            if (typeof aValue === 'string' && typeof bValue === 'string') {
                return sortDirection === 'asc' 
                    ? aValue.localeCompare(bValue)
                    : bValue.localeCompare(aValue);
            }
            
            return 0;
        });

        return filtered;
    }, [datasets, searchTerm, sortField, sortDirection, selectedSensitivity, selectedFrequency]);

    const handleSort = (field: keyof DatasetAsset) => {
        if (sortField === field) {
            setSortDirection(sortDirection === "asc" ? "desc" : "asc");
        } else {
            setSortField(field);
            setSortDirection("asc");
        }
    };

    const handleEdit = (dataset: DatasetAsset) => {
        setEditingDataset(dataset);
        setIsEditModalOpen(true);
    };

    const handleDelete = async (dataset: DatasetAsset) => {
        if (confirm(`Are you sure you want to delete "${dataset.name}"?`)) {
            await onDelete(dataset);
        }
    };

    const getDatabaseName = (databaseId: string) => {
        const database = databases.find(db => db.id === databaseId);
        return database ? database.name : databaseId;
    };

    const getSystemName = (systemId: string) => {
        const system = systems.find(sys => sys.id === systemId);
        return system ? system.name : systemId;
    };

    if (loading) {
        return (
            <div className="flex items-center justify-center py-12">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#003874] mx-auto mb-4"></div>
                    <p className="text-gray-600">Loading datasets...</p>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="text-center py-12">
                <div className="text-red-600 mb-4">Error: {error}</div>
                <Button onClick={() => window.location.reload()}>
                    Retry
                </Button>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Header Controls */}
            <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
                <div className="flex-1 max-w-md">
                    <div className="relative">
                        <Search className={`absolute top-3 w-4 h-4 text-gray-400 ${isRTL ? 'right-3' : 'left-3'}`} />
                        <Input
                            placeholder={searchPlaceholder}
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className={`${isRTL ? 'pr-10' : 'pl-10'} border-gray-300 focus:border-[#003874] focus:ring-[#003874]`}
                        />
                    </div>
                </div>
                
                <div className="flex gap-2 flex-wrap">
                    {/* Filter Controls */}
                    <select
                        value={selectedSensitivity}
                        onChange={(e) => setSelectedSensitivity(e.target.value)}
                        className="px-3 py-2 border border-gray-300 rounded-lg focus:border-[#003874] focus:ring-[#003874]"
                    >
                        <option value="">All Sensitivities</option>
                        <option value="Public">Public</option>
                        <option value="Internal">Internal</option>
                        <option value="Confidential">Confidential</option>
                        <option value="Restricted">Restricted</option>
                    </select>

                    <select
                        value={selectedFrequency}
                        onChange={(e) => setSelectedFrequency(e.target.value)}
                        className="px-3 py-2 border border-gray-300 rounded-lg focus:border-[#003874] focus:ring-[#003874]"
                    >
                        <option value="">All Frequencies</option>
                        <option value="Real-time">Real-time</option>
                        <option value="Daily">Daily</option>
                        <option value="Weekly">Weekly</option>
                        <option value="Monthly">Monthly</option>
                        <option value="Quarterly">Quarterly</option>
                        <option value="Annually">Annually</option>
                    </select>

                    {/* Action Buttons */}
                    <Button
                        onClick={() => setIsExcelModalOpen(true)}
                        variant="outline"
                        className="border-[#003874] text-[#003874] hover:bg-[#003874] hover:text-white"
                    >
                        <Upload className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                        Bulk Import
                    </Button>
                    
                    <Button
                        onClick={() => setIsAddModalOpen(true)}
                        className="bg-gradient-to-r from-[#003874] to-[#2D8DC6] hover:from-[#001f4d] hover:to-[#1e6fa8]"
                    >
                        <Plus className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                        {addButtonLabel}
                    </Button>
                </div>
            </div>

            {/* Datasets Grid */}
            {filteredAndSortedDatasets.length === 0 ? (
                <div className="text-center py-20">
                    <FileText className="mx-auto h-16 w-16 text-gray-400 mb-6" />
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">{emptyMessage}</h3>
                    <p className="text-gray-600 mb-8 max-w-md mx-auto">{emptyDescription}</p>
                    <Button
                        onClick={() => setIsAddModalOpen(true)}
                        className="bg-gradient-to-r from-[#003874] to-[#2D8DC6] hover:from-[#001f4d] hover:to-[#1e6fa8]"
                    >
                        <Plus className={`w-4 h-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                        {addButtonLabel}
                    </Button>
                </div>
            ) : (
                <motion.div
                    variants={containerVariants}
                    initial="hidden"
                    animate="visible"
                    className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6"
                >
                    {filteredAndSortedDatasets.map((dataset) => {
                        const FrequencyIcon = updateFrequencyIcons[dataset.updateFrequency];
                        
                        return (
                            <motion.div
                                key={dataset.id}
                                variants={cardVariants}
                                className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200 overflow-hidden"
                            >
                                {/* Card Header */}
                                <div className="p-6 border-b border-gray-100">
                                    <div className="flex items-start justify-between">
                                        <div className="flex-1">
                                            <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                                                {dataset.name}
                                            </h3>
                                            <p className="text-gray-600 text-sm line-clamp-3 mb-3">
                                                {dataset.description}
                                            </p>
                                            <div className="flex items-center gap-2">
                                                <Badge className={sensitivityColors[dataset.sensitivity]}>
                                                    <Shield className="w-3 h-3 mr-1" />
                                                    {dataset.sensitivity}
                                                </Badge>
                                                <Badge variant="outline" className="text-gray-600">
                                                    <FrequencyIcon className="w-3 h-3 mr-1" />
                                                    {dataset.updateFrequency}
                                                </Badge>
                                            </div>
                                        </div>
                                        
                                        <DropdownMenu>
                                            <DropdownMenuTrigger asChild>
                                                <Button variant="ghost" size="sm" className="ml-2">
                                                    <MoreVertical className="w-4 h-4" />
                                                </Button>
                                            </DropdownMenuTrigger>
                                            <DropdownMenuContent>
                                                <DropdownMenuItem onClick={() => onView(dataset)}>
                                                    <Eye className="w-4 h-4 mr-2" />
                                                    View
                                                </DropdownMenuItem>
                                                <DropdownMenuItem onClick={() => handleEdit(dataset)}>
                                                    <Edit className="w-4 h-4 mr-2" />
                                                    Edit
                                                </DropdownMenuItem>
                                                <DropdownMenuItem 
                                                    onClick={() => handleDelete(dataset)}
                                                    className="text-red-600"
                                                >
                                                    <Trash2 className="w-4 h-4 mr-2" />
                                                    Delete
                                                </DropdownMenuItem>
                                            </DropdownMenuContent>
                                        </DropdownMenu>
                                    </div>
                                </div>

                                {/* Card Body */}
                                <div className="p-6 space-y-4">
                                    {/* Owner & Size */}
                                    <div className="grid grid-cols-2 gap-4">
                                        <div className="flex items-center gap-2">
                                            <User className="w-4 h-4 text-gray-400" />
                                            <span className="text-sm text-gray-600 truncate">{dataset.owner}</span>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <HardDrive className="w-4 h-4 text-gray-400" />
                                            <span className="text-sm text-gray-600">{dataset.size}</span>
                                        </div>
                                    </div>

                                    {/* Format & Last Updated */}
                                    <div className="grid grid-cols-2 gap-4">
                                        <div className="flex items-center gap-2">
                                            <FileText className="w-4 h-4 text-gray-400" />
                                            <span className="text-sm text-gray-600">{dataset.format}</span>
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <Calendar className="w-4 h-4 text-gray-400" />
                                            <span className="text-sm text-gray-600">
                                                {dataset.lastUpdated ? new Date(dataset.lastUpdated.toDate()).toLocaleDateString() : 'N/A'}
                                            </span>
                                        </div>
                                    </div>

                                    {/* Databases Used */}
                                    {dataset.databasesUsed.length > 0 && (
                                        <div>
                                            <div className="flex items-center gap-2 mb-2">
                                                <Database className="w-4 h-4 text-gray-400" />
                                                <span className="text-sm font-medium text-gray-700">Databases Used:</span>
                                            </div>
                                            <div className="flex flex-wrap gap-1">
                                                {dataset.databasesUsed.slice(0, 3).map((dbId) => (
                                                    <Badge key={dbId} variant="outline" className="text-xs">
                                                        {getDatabaseName(dbId)}
                                                    </Badge>
                                                ))}
                                                {dataset.databasesUsed.length > 3 && (
                                                    <Badge variant="outline" className="text-xs">
                                                        +{dataset.databasesUsed.length - 3} more
                                                    </Badge>
                                                )}
                                            </div>
                                        </div>
                                    )}

                                    {/* Systems That Use It */}
                                    {dataset.systemsThatUseIt.length > 0 && (
                                        <div>
                                            <div className="flex items-center gap-2 mb-2">
                                                <Server className="w-4 h-4 text-gray-400" />
                                                <span className="text-sm font-medium text-gray-700">Used by Systems:</span>
                                            </div>
                                            <div className="flex flex-wrap gap-1">
                                                {dataset.systemsThatUseIt.slice(0, 3).map((sysId) => (
                                                    <Badge key={sysId} variant="outline" className="text-xs bg-blue-50 text-blue-700">
                                                        {getSystemName(sysId)}
                                                    </Badge>
                                                ))}
                                                {dataset.systemsThatUseIt.length > 3 && (
                                                    <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700">
                                                        +{dataset.systemsThatUseIt.length - 3} more
                                                    </Badge>
                                                )}
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </motion.div>
                        );
                    })}
                </motion.div>
            )}

            {/* Modals */}
            <AddEditDatasetModal
                isOpen={isAddModalOpen}
                onClose={() => setIsAddModalOpen(false)}
                onSave={onAdd}
                databases={databases}
                systems={systems}
                isRTL={isRTL}
            />

            <AddEditDatasetModal
                isOpen={isEditModalOpen}
                onClose={() => {
                    setIsEditModalOpen(false);
                    setEditingDataset(null);
                }}
                onSave={async (updates: Partial<DatasetAsset>) => {
                    if (editingDataset) {
                        await onEdit(editingDataset, updates);
                    }
                }}
                dataset={editingDataset}
                databases={databases}
                systems={systems}
                isRTL={isRTL}
            />

            <ExcelUploadModal
                isOpen={isExcelModalOpen}
                onClose={() => setIsExcelModalOpen(false)}
                onImport={onBulkImport}
                columns={datasetColumns}
                assetType={assetType}
                templateFileName={templateFileName}
                isRTL={isRTL}
            />
        </div>
    );
} 
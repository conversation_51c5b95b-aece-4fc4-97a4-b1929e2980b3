"use client";

import { motion } from "framer-motion";
import { useTranslations } from "next-intl";
import { 
  Server, 
  Database, 
  BarChart3, 
  FileStack, 
  Network, 
  Monitor,
  ArrowRight,
  Sparkles
} from "lucide-react";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

// Asset types configuration
export type AssetType = 'systems' | 'databases' | 'datasets' | 'structuredFiles' | 'apis' | 'dashboards';

interface AssetCardProps {
  type: AssetType;
  locale: string;
  onClick?: () => void;
  isRTL?: boolean;
}

// Icon mapping for each asset type
const assetIcons: Record<AssetType, React.ElementType> = {
  systems: Server,
  databases: Database,
  datasets: BarChart3,
  structuredFiles: FileStack,
  apis: Network,
  dashboards: Monitor,
};

// Enhanced animation variants
const cardVariants = {
  hidden: { 
    opacity: 0, 
    y: 60,
    scale: 0.9,
    rotateX: 15
  },
  visible: {
    opacity: 1,
    y: 0,
    scale: 1,
    rotateX: 0,
    transition: {
      type: "spring",
      stiffness: 300,
      damping: 30,
      duration: 0.8,
      staggerChildren: 0.1
    }
  },
  hover: {
    y: -20,
    scale: 1.05,
    rotateX: -2,
    transition: {
      type: "spring",
      stiffness: 400,
      damping: 25,
      duration: 0.4
    }
  }
};

const contentVariants = {
  hidden: { 
    opacity: 0,
    y: 30
  },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      delay: 0.3,
      duration: 0.6,
      staggerChildren: 0.15
    }
  }
};

const itemVariants = {
  hidden: { 
    opacity: 0,
    x: -30,
    scale: 0.95
  },
  visible: {
    opacity: 1,
    x: 0,
    scale: 1,
    transition: {
      type: "spring",
      stiffness: 200,
      damping: 20
    }
  }
};

export function AssetCard({ type, locale, onClick, isRTL = false }: AssetCardProps) {
  const t = useTranslations('DataAssets');
  
  const Icon = assetIcons[type];

  const handleCardClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (onClick) {
      onClick();
    }
    // Completely removed all routing logic - parent handles everything
  };
  
  const assetData = {
    title: t(`assetCards.${type}.title`),
    description: t(`assetCards.${type}.description`)
  };

  return (
    <motion.div
      variants={cardVariants}
      initial="hidden"
      animate="visible"
      whileHover="hover"
      className="group w-full perspective-1000 cursor-pointer"
      onClick={handleCardClick}
    >
      <Card className="relative overflow-hidden border-0 bg-gradient-to-br from-[#003874] via-[#2D8DC6] to-[#48D3A5] shadow-2xl hover:shadow-4xl transition-all duration-700 h-full transform-gpu rounded-3xl">
        {/* Hero-style decorative elements */}
        <motion.div
          className={`absolute top-0 w-64 h-64 rounded-full opacity-20 bg-white ${isRTL ? 'left-0' : 'right-0'}`}
          initial={{ x: isRTL ? -100 : 100, y: -100 }}
          animate={{
            x: 0,
            y: 0,
            scale: [1, 1.2, 1],
            rotate: [0, 45, 0],
          }}
          transition={{ duration: 20, repeat: Infinity, repeatType: "reverse" }}
        />
        <motion.div
          className={`absolute bottom-0 w-32 h-32 rounded-full opacity-20 bg-white ${isRTL ? 'right-1/4' : 'left-1/4'}`}
          initial={{ y: 50 }}
          animate={{
            y: [0, 20, 0],
            scale: [1, 1.1, 1],
          }}
          transition={{ duration: 8, repeat: Infinity, repeatType: "reverse" }}
        />
        <motion.div
          className="absolute top-1/3 right-1/4 w-48 h-48 rounded-full opacity-10 bg-white"
          initial={{ y: -20 }}
          animate={{
            y: [0, -30, 0],
            scale: [1, 1.2, 1],
          }}
          transition={{ duration: 12, repeat: Infinity, repeatType: "reverse" }}
        />
        

        {/* Hero Header with Enhanced Design */}
        <CardHeader className="relative z-10 pb-6 border-b border-white/20 pt-10">
          <motion.div 
            variants={contentVariants}
            className="space-y-6"
          >
            {/* Icon and Status Badge */}
            <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
              <motion.div variants={itemVariants}>
                <div className="relative flex-shrink-0">
                  <div className="p-5 bg-white/20 backdrop-blur-md rounded-3xl shadow-2xl group-hover:shadow-3xl transition-all duration-500 transform group-hover:scale-110 group-hover:rotate-3 border border-white/30">
                    <Icon className="h-10 w-10 text-white" />
                  </div>
                  {/* Floating Ring Effect */}
                  <div className="absolute inset-0 rounded-3xl border-2 border-white/50 scale-125 opacity-0 group-hover:opacity-100 group-hover:scale-150 transition-all duration-700" />
                </div>
              </motion.div>
              
              <motion.div 
                variants={itemVariants}
                className={`flex items-center gap-2 bg-white/20 backdrop-blur-md px-4 py-2 rounded-full shadow-md border border-white/30 ${isRTL ? 'flex-row-reverse' : ''}`}
              >
                <Sparkles className="h-4 w-4 text-white" />
                <span className="text-sm font-semibold text-white">
                  {isRTL ? 'جاهز' : 'Ready'}
                </span>
              </motion.div>
            </div>

            {/* Title Section */}
            <motion.div 
              variants={itemVariants}
              className={`${isRTL ? 'text-right' : ''}`}
            >
              <motion.h2 
                variants={itemVariants}
                className={`text-3xl font-black text-white transition-all duration-500 leading-tight mb-4 ${isRTL ? 'font-arabic' : ''}`}
              >
                {assetData.title}
              </motion.h2>
              
              <motion.p 
                variants={itemVariants}
                className={`text-white/90 text-lg leading-relaxed font-medium ${isRTL ? 'font-arabic' : ''}`}
              >
                {assetData.description}
              </motion.p>
            </motion.div>
          </motion.div>
        </CardHeader>

        {/* Enhanced Content Section */}
        <CardContent className="relative z-10 pt-6 pb-8 px-8">
          <motion.div 
            variants={contentVariants}
            className="space-y-6"
          >
            {/* Action Button */}
            <motion.div 
              variants={itemVariants}
              className={`${isRTL ? 'text-right' : ''}`}
            >
              <Button
                variant="ghost"
                className={`w-full group/btn bg-white/20 backdrop-blur-md text-white hover:bg-white/30 transition-all duration-500 border border-white/30 shadow-lg hover:shadow-2xl transform hover:scale-105 font-bold py-6 text-lg rounded-2xl ${isRTL ? 'flex-row-reverse' : ''}`}
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                }}
              >
                <span className={isRTL ? 'font-arabic' : ''}>
                  {isRTL ? 'استكشاف' : 'Explore'}
                </span>
                <motion.div
                  className={`${isRTL ? 'mr-3' : 'ml-3'}`}
                  whileHover={{ x: isRTL ? -5 : 5 }}
                  transition={{ type: "spring", stiffness: 400, damping: 17 }}
                >
                  <ArrowRight className={`h-5 w-5 ${isRTL ? 'rotate-180' : ''}`} />
                </motion.div>
              </Button>
            </motion.div>
          </motion.div>
        </CardContent>

        {/* Subtle hover effect */}
        <div className="absolute inset-0 rounded-3xl bg-white/5 opacity-0 group-hover:opacity-100 transition-all duration-500 pointer-events-none" />
      </Card>
    </motion.div>
  );
} 
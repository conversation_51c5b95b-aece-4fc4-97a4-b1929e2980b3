"use client";

import React, { useState, useRef, useEffect } from "react";
import { motion } from "framer-motion";
import { useTranslations } from "next-intl";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
    Plus,
    Search,
    MoreHorizontal,
    Edit,
    Trash2,
    Check,
    X,
    SortAsc,
    SortDesc,
    Loader2,
    Save,
    Upload,
    ChevronDown,
    ChevronRight,
    Eye,
    EyeOff,
    Columns
} from "lucide-react";
import { AssetData } from "@/lib/services/dataAssetsService";
import { ExcelUploadModal } from "./ExcelUploadModal";

// Column configuration interface
export interface TableColumn<T = any> {
    key: string;
    label: string;
    sortable?: boolean;
    editable?: boolean;
    type?: 'text' | 'select' | 'number';
    options?: string[];
    render?: (value: any, item: T) => React.ReactNode;
    width?: string;
    required?: boolean;
    placeholder?: string;
    priority?: 'high' | 'medium' | 'low'; // For responsive hiding
    hideOnMobile?: boolean; // Explicit mobile hide
}

// Asset table props
interface AssetTableProps<T extends AssetData> {
    assets: T[];
    columns: TableColumn<T>[];
    loading?: boolean;
    error?: string | null;
    onAdd?: (newAsset: Partial<T>) => Promise<void>;
    onEdit?: (asset: T, updates: Partial<T>) => Promise<void>;
    onDelete?: (asset: T) => void;
    onView?: (asset: T) => void;
    onBulkImport?: (assets: Partial<T>[]) => Promise<void>;
    searchPlaceholder?: string;
    addButtonLabel?: string;
    emptyMessage?: string;
    emptyDescription?: string;
    isRTL?: boolean;
    className?: string;
    getNextIndex?: () => Promise<number>;
    assetType?: string;
    templateFileName?: string;
}

const tableVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
        opacity: 1,
        y: 0,
        transition: {
            duration: 0.6,
            staggerChildren: 0.1
        }
    }
};

const rowVariants = {
    hidden: { opacity: 0, x: -20 },
    visible: {
        opacity: 1,
        x: 0,
        transition: {
            duration: 0.4
        }
    }
};

export function AssetTable<T extends AssetData>({
    assets,
    columns,
    loading = false,
    error = null,
    onAdd,
    onEdit,
    onDelete,
    onView,
    onBulkImport,
    searchPlaceholder = "Search assets...",
    addButtonLabel = "Add Asset",
    emptyMessage = "No assets found",
    emptyDescription = "Get started by adding your first asset.",
    isRTL = false,
    className = "",
    getNextIndex,
    assetType = "assets",
    templateFileName = "template"
}: AssetTableProps<T>) {
    const systemsT = useTranslations('Systems');
    const [searchTerm, setSearchTerm] = useState("");
    const [sortColumn, setSortColumn] = useState<string | null>(null);
    const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
    const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
    const [assetToDelete, setAssetToDelete] = useState<T | null>(null);
    const [editingAsset, setEditingAsset] = useState<string | null>(null);
    const [isAddingNew, setIsAddingNew] = useState(false);
    const [newAssetData, setNewAssetData] = useState<Partial<T>>({});
    const [editData, setEditData] = useState<Partial<T>>({});
    const [saving, setSaving] = useState(false);
    const [showExcelUpload, setShowExcelUpload] = useState(false);
    const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());
    const [hiddenColumns, setHiddenColumns] = useState<Set<string>>(new Set());
    const [showColumnToggle, setShowColumnToggle] = useState(false);
    const [isMobileView, setIsMobileView] = useState(false);

    // Check screen size for responsive behavior
    useEffect(() => {
        const checkScreenSize = () => {
            setIsMobileView(window.innerWidth < 768);
        };
        
        checkScreenSize();
        window.addEventListener('resize', checkScreenSize);
        return () => window.removeEventListener('resize', checkScreenSize);
    }, []);

    // Filter and sort assets
    const filteredAndSortedAssets = assets
        .filter(asset => {
            if (!searchTerm) return true;
            
            return columns.some(column => {
                const value = asset[column.key as keyof T];
                return value && 
                    value.toString().toLowerCase().includes(searchTerm.toLowerCase());
            });
        })
        .sort((a, b) => {
            if (!sortColumn) return 0;
            
            const aValue = a[sortColumn as keyof T];
            const bValue = b[sortColumn as keyof T];
            
            if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
            if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
            return 0;
        });

    const handleSort = (columnKey: string) => {
        if (sortColumn === columnKey) {
            setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
        } else {
            setSortColumn(columnKey);
            setSortDirection('asc');
        }
    };

    const handleDeleteClick = (asset: T) => {
        setAssetToDelete(asset);
        setDeleteDialogOpen(true);
    };

    const handleDeleteConfirm = () => {
        if (assetToDelete && onDelete) {
            onDelete(assetToDelete);
        }
        setDeleteDialogOpen(false);
        setAssetToDelete(null);
    };

    const handleAddNew = async () => {
        setIsAddingNew(true);
        const newData: Partial<T> = {};
        
        // Auto-fill index if needed
        if (getNextIndex) {
            try {
                const nextIndex = await getNextIndex();
                (newData as any).systemIndex = nextIndex;
            } catch (error) {
                console.error('Error getting next index:', error);
                // Set a default index
                (newData as any).systemIndex = 1;
            }
        }
        
        // Set default status if it's a status field
        const statusColumn = columns.find(col => col.key === 'status');
        if (statusColumn && statusColumn.options && statusColumn.options.length > 0) {
            (newData as any).status = statusColumn.options[0];
        }
        
        setNewAssetData(newData);
    };

    const handleSaveNew = async () => {
        if (!onAdd) return;
        
        setSaving(true);
        try {
            await onAdd(newAssetData);
            setIsAddingNew(false);
            setNewAssetData({});
        } catch (error) {
            console.error('Error saving new asset:', error);
        } finally {
            setSaving(false);
        }
    };

    const handleCancelNew = () => {
        setIsAddingNew(false);
        setNewAssetData({});
    };

    const handleEdit = (asset: T) => {
        setEditingAsset(asset.id!);
        setEditData({ ...asset });
    };

    const handleSaveEdit = async () => {
        if (!editingAsset || !onEdit) return;
        
        const asset = assets.find(a => a.id === editingAsset);
        if (!asset) return;

        setSaving(true);
        try {
            await onEdit(asset, editData);
            setEditingAsset(null);
            setEditData({});
        } catch (error) {
            console.error('Error saving edit:', error);
        } finally {
            setSaving(false);
        }
    };

    const handleCancelEdit = () => {
        setEditingAsset(null);
        setEditData({});
    };

    const handleBulkImport = async (importData: Partial<T>[]) => {
        if (!onBulkImport) return;
        
        try {
            await onBulkImport(importData);
        } catch (error) {
            console.error('Bulk import failed:', error);
            throw error;
        }
    };

    // Get visible columns based on screen size and user preferences
    const getVisibleColumns = () => {
        return columns.filter(column => {
            // Check if column is hidden by user
            if (hiddenColumns.has(column.key)) return false;
            
            // On mobile, hide low priority and explicitly mobile-hidden columns
            if (isMobileView) {
                if (column.hideOnMobile) return false;
                if (column.priority === 'low') return false;
                // On very small screens, also hide medium priority
                if (window.innerWidth < 640 && column.priority === 'medium') return false;
            }
            
            return true;
        });
    };

    // Get hidden columns for expanded view
    const getHiddenColumns = () => {
        return columns.filter(column => {
            if (hiddenColumns.has(column.key)) return true;
            if (isMobileView && (column.hideOnMobile || column.priority === 'low')) return true;
            if (window.innerWidth < 640 && column.priority === 'medium') return true;
            return false;
        });
    };

    // Toggle row expansion
    const toggleRowExpansion = (assetId: string) => {
        const newExpanded = new Set(expandedRows);
        if (newExpanded.has(assetId)) {
            newExpanded.delete(assetId);
        } else {
            newExpanded.add(assetId);
        }
        setExpandedRows(newExpanded);
    };

    // Toggle column visibility
    const toggleColumnVisibility = (columnKey: string) => {
        const newHidden = new Set(hiddenColumns);
        if (newHidden.has(columnKey)) {
            newHidden.delete(columnKey);
        } else {
            newHidden.add(columnKey);
        }
        setHiddenColumns(newHidden);
    };

    const renderEditableCell = (
        column: TableColumn<T>, 
        value: any, 
        data: Partial<T>, 
        setData: (data: Partial<T>) => void,
        isNew: boolean = false
    ) => {
        if (!column.editable) {
            if (column.render) {
                return column.render(value, data as T);
            }
            return value?.toString() || "-";
        }

        const currentValue = data[column.key as keyof T] ?? value ?? "";

        if (column.type === 'select' && column.options) {
            return (
                <Select
                    value={currentValue as string}
                    onValueChange={(newValue) => {
                        setData({
                            ...data,
                            [column.key]: newValue
                        } as Partial<T>);
                    }}
                >
                    <SelectTrigger className="h-9 border border-slate-200 bg-white text-slate-900 focus:ring-2 focus:ring-[#003874] focus:border-[#003874] rounded-lg font-medium transition-all duration-200">
                        <SelectValue placeholder={column.placeholder || `Select ${column.label}`} />
                    </SelectTrigger>
                    <SelectContent>
                        {column.options.map(option => (
                            <SelectItem key={option} value={option}>
                                {option}
                            </SelectItem>
                        ))}
                    </SelectContent>
                </Select>
            );
        }

        return (
            <Input
                type={column.type === 'number' ? 'number' : 'text'}
                value={currentValue as string}
                onChange={(e) => {
                    const newValue = column.type === 'number' ? 
                        parseInt(e.target.value) || 0 : 
                        e.target.value;
                    setData({
                        ...data,
                        [column.key]: newValue
                    } as Partial<T>);
                }}
                placeholder={column.placeholder || `Enter ${column.label}`}
                className="h-9 border border-slate-200 bg-white text-slate-900 placeholder:text-slate-500 focus:ring-2 focus:ring-[#003874] focus:border-[#003874] rounded-lg font-medium transition-all duration-200"
                disabled={column.key === 'systemIndex' && !isNew} // Disable index editing for existing items
            />
        );
    };

    const renderCellContent = (column: TableColumn<T>, asset: T) => {
        const value = asset[column.key as keyof T];
        
        if (column.render) {
            return column.render(value, asset);
        }
        
        return value?.toString() || "-";
    };

    const isFormValid = (data: Partial<T>) => {
        return columns
            .filter(col => col.required)
            .every(col => data[col.key as keyof T]);
    };

    if (loading) {
        return (
            <div className="flex items-center justify-center py-16">
                <div className="text-center">
                    <Loader2 className="h-12 w-12 animate-spin text-[#003874] mx-auto mb-4" />
                    <p className="text-gray-600 text-lg">Loading assets...</p>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="text-center py-16">
                <div className="text-red-600 text-lg font-semibold mb-2">Error</div>
                <p className="text-gray-600">{error}</p>
            </div>
        );
    }

    return (
        <motion.div
            variants={tableVariants}
            initial="hidden"
            animate="visible"
            className={`space-y-6 ${className}`}
        >
            {/* Header with Search and Action Buttons */}
            <div className={`flex items-center justify-between gap-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div className="relative flex-1 max-w-sm">
                    <Search className={`absolute top-3 h-4 w-4 text-gray-400 ${isRTL ? 'right-3' : 'left-3'}`} />
                    <Input
                        placeholder={searchPlaceholder}
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className={`${isRTL ? 'pr-10' : 'pl-10'}`}
                    />
                </div>
                
                <div className="flex items-center gap-2">
                    {/* Column Toggle Button */}
                    <DropdownMenu open={showColumnToggle} onOpenChange={setShowColumnToggle}>
                        <DropdownMenuTrigger asChild>
                            <Button
                                variant="outline"
                                size="sm"
                                className={`border-slate-300 text-slate-600 hover:bg-slate-100 ${isRTL ? 'flex-row-reverse' : ''}`}
                            >
                                <Columns className={`h-4 w-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                                Columns
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align={isRTL ? "start" : "end"} className="w-56">
                            {columns.map((column) => (
                                <DropdownMenuItem
                                    key={column.key}
                                    onClick={() => toggleColumnVisibility(column.key)}
                                    className={`cursor-pointer ${isRTL ? 'flex-row-reverse' : ''}`}
                                >
                                    {hiddenColumns.has(column.key) ? (
                                        <EyeOff className={`h-4 w-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                                    ) : (
                                        <Eye className={`h-4 w-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                                    )}
                                    {column.label}
                                    {column.priority && (
                                        <Badge 
                                            variant="outline" 
                                            className={`text-xs ${isRTL ? 'mr-auto' : 'ml-auto'} ${
                                                column.priority === 'high' ? 'border-green-300 text-green-700' :
                                                column.priority === 'medium' ? 'border-yellow-300 text-yellow-700' :
                                                'border-gray-300 text-gray-500'
                                            }`}
                                        >
                                            {column.priority}
                                        </Badge>
                                    )}
                                </DropdownMenuItem>
                            ))}
                        </DropdownMenuContent>
                    </DropdownMenu>

                    {onBulkImport && (
                        <Button
                            onClick={() => setShowExcelUpload(true)}
                            variant="outline"
                            className={`border-[#003874] text-[#003874] hover:bg-[#003874] hover:text-white ${isRTL ? 'flex-row-reverse' : ''}`}
                        >
                            <Upload className={`h-4 w-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                            {systemsT('bulkUpload.uploadButton')}
                        </Button>
                    )}
                    {onAdd && (
                        <Button
                            onClick={handleAddNew}
                            disabled={isAddingNew}
                            className={`bg-gradient-to-r from-[#003874] to-[#2D8DC6] hover:from-[#001f4d] hover:to-[#1e6fa8] ${isRTL ? 'flex-row-reverse' : ''}`}
                        >
                            <Plus className={`h-4 w-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                            {addButtonLabel}
                        </Button>
                    )}
                </div>
            </div>

            {/* Table */}
            <motion.div
                variants={rowVariants}
                className="bg-white rounded-xl border border-gray-200 overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300"
            >
                <Table>
                    <TableHeader>
                        <TableRow className="bg-gradient-to-r from-slate-50 via-blue-50 to-indigo-50 border-b-2 border-slate-200">
                            {/* Expand/Collapse column if there are hidden columns */}
                            {getHiddenColumns().length > 0 && (
                                <TableHead className="w-[50px] text-center font-bold text-slate-700 text-sm uppercase tracking-wide h-14">
                                    <div className="flex items-center justify-center">
                                        <ChevronRight className="h-4 w-4" />
                                    </div>
                                </TableHead>
                            )}
                            {getVisibleColumns().map((column) => (
                                <TableHead
                                    key={column.key}
                                    className={`${column.width || ''} ${isRTL ? 'text-right' : 'text-left'} font-bold text-slate-700 text-sm uppercase tracking-wide h-14`}
                                >
                                    {column.sortable ? (
                                        <Button
                                            variant="ghost"
                                            onClick={() => handleSort(column.key)}
                                            className={`h-auto p-2 font-bold text-slate-700 hover:bg-white/70 hover:text-slate-900 rounded-lg transition-all duration-200 ${isRTL ? 'flex-row-reverse' : ''}`}
                                        >
                                            {column.label}
                                            {column.required && <span className="text-red-500 ml-1">*</span>}
                                            {sortColumn === column.key ? (
                                                sortDirection === 'asc' ? (
                                                    <SortAsc className={`h-4 w-4 ${isRTL ? 'mr-1' : 'ml-1'}`} />
                                                ) : (
                                                    <SortDesc className={`h-4 w-4 ${isRTL ? 'mr-1' : 'ml-1'}`} />
                                                )
                                            ) : (
                                                <div className={`h-4 w-4 ${isRTL ? 'mr-1' : 'ml-1'}`} />
                                            )}
                                        </Button>
                                    ) : (
                                        <span className="font-bold text-slate-700">
                                            {column.label}
                                            {column.required && <span className="text-red-500 ml-1">*</span>}
                                        </span>
                                    )}
                                </TableHead>
                            ))}
                            <TableHead className={`w-[120px] ${isRTL ? 'text-right' : 'text-left'} font-bold text-slate-700 text-sm uppercase tracking-wide`}>
                                Actions
                            </TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {/* Helper instruction for new row */}
                        {isAddingNew && (
                            <motion.tr
                                initial={{ opacity: 0, y: -10 }}
                                animate={{ opacity: 1, y: 0 }}
                                className="bg-gradient-to-r from-blue-50 to-indigo-50 border-l-4 border-l-[#003874]"
                            >
                                <TableCell 
                                    colSpan={columns.length + 1} 
                                    className="p-4 text-center"
                                >
                                    <div className="flex items-center justify-center gap-2 text-sm text-blue-700">
                                        <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                                        <span className="font-medium">Fill in the fields below and click the green checkmark to save</span>
                                        <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                                    </div>
                                </TableCell>
                            </motion.tr>
                        )}

                        {/* Add New Row */}
                        {isAddingNew && (
                            <motion.tr
                                initial={{ opacity: 0, y: -10 }}
                                animate={{ opacity: 1, y: 0 }}
                                className="bg-gradient-to-r from-blue-50 to-indigo-50 border-l-4 border-l-[#003874] ring-2 ring-blue-200 ring-opacity-50 shadow-sm"
                            >
                                {/* Expand column placeholder for new row */}
                                {getHiddenColumns().length > 0 && (
                                    <TableCell className="p-4 w-[50px]">
                                        <div className="flex items-center justify-center">
                                            <div className="w-4 h-4" />
                                        </div>
                                    </TableCell>
                                )}
                                {getVisibleColumns().map((column) => (
                                    <TableCell
                                        key={column.key}
                                        className={`${isRTL ? 'text-right' : 'text-left'} p-4 text-slate-700 font-medium h-14`}
                                    >
                                        {renderEditableCell(column, null, newAssetData, setNewAssetData, true)}
                                    </TableCell>
                                ))}
                                <TableCell className="p-4">
                                    <div className="flex items-center gap-2">
                                        <Button
                                            size="sm"
                                            onClick={handleSaveNew}
                                            disabled={!isFormValid(newAssetData) || saving}
                                            className="h-9 w-9 p-0 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 shadow-lg hover:shadow-xl transition-all duration-200 rounded-lg"
                                            title="Save system"
                                        >
                                            {saving ? (
                                                <Loader2 className="h-4 w-4 animate-spin" />
                                            ) : (
                                                <Check className="h-4 w-4" />
                                            )}
                                        </Button>
                                        <Button
                                            size="sm"
                                            variant="outline"
                                            onClick={handleCancelNew}
                                            disabled={saving}
                                            className="h-9 w-9 p-0 border-slate-300 text-slate-600 hover:bg-slate-100 hover:text-slate-800 rounded-lg transition-all duration-200"
                                            title="Cancel"
                                        >
                                            <X className="h-4 w-4" />
                                        </Button>
                                    </div>
                                </TableCell>
                            </motion.tr>
                        )}

                        {/* Existing Assets */}
                        {filteredAndSortedAssets.map((asset, index) => (
                            <React.Fragment key={asset.id}>
                                <motion.tr
                                    variants={rowVariants}
                                    className={`hover:bg-slate-50 transition-all duration-200 border-b border-slate-100 ${
                                        editingAsset === asset.id ? 'bg-gradient-to-r from-amber-50 to-yellow-50 border-l-4 border-l-amber-400 shadow-sm' : ''
                                    }`}
                                >
                                    {/* Expand/Collapse button if there are hidden columns */}
                                    {getHiddenColumns().length > 0 && (
                                        <TableCell className="p-4 w-[50px]">
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                onClick={() => toggleRowExpansion(asset.id!)}
                                                className="h-8 w-8 p-0 text-slate-600 hover:bg-slate-100 rounded"
                                            >
                                                {expandedRows.has(asset.id!) ? (
                                                    <ChevronDown className="h-4 w-4" />
                                                ) : (
                                                    <ChevronRight className="h-4 w-4" />
                                                )}
                                            </Button>
                                        </TableCell>
                                    )}
                                    {getVisibleColumns().map((column) => (
                                        <TableCell
                                            key={column.key}
                                            className={`${isRTL ? 'text-right' : 'text-left'} p-4 text-slate-700 font-medium h-14`}
                                        >
                                            {editingAsset === asset.id ? 
                                                renderEditableCell(column, asset[column.key as keyof T], editData, setEditData) :
                                                renderCellContent(column, asset)
                                            }
                                        </TableCell>
                                    ))}
                                <TableCell className="p-4">
                                    {editingAsset === asset.id ? (
                                        <div className="flex items-center gap-2">
                                            <Button
                                                size="sm"
                                                onClick={handleSaveEdit}
                                                disabled={saving}
                                                className="h-9 w-9 p-0 bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 shadow-lg hover:shadow-xl transition-all duration-200 rounded-lg"
                                            >
                                                {saving ? (
                                                    <Loader2 className="h-4 w-4 animate-spin" />
                                                ) : (
                                                    <Save className="h-4 w-4" />
                                                )}
                                            </Button>
                                            <Button
                                                size="sm"
                                                variant="outline"
                                                onClick={handleCancelEdit}
                                                disabled={saving}
                                                className="h-9 w-9 p-0 border-slate-300 text-slate-600 hover:bg-slate-100 hover:text-slate-800 rounded-lg transition-all duration-200"
                                            >
                                                <X className="h-4 w-4" />
                                            </Button>
                                        </div>
                                    ) : (
                                        <DropdownMenu>
                                            <DropdownMenuTrigger asChild>
                                                <Button
                                                    variant="ghost"
                                                    className="h-9 w-9 p-0 text-slate-600 hover:bg-slate-100 hover:text-slate-800 rounded-lg transition-all duration-200"
                                                >
                                                    <MoreHorizontal className="h-4 w-4" />
                                                </Button>
                                            </DropdownMenuTrigger>
                                            <DropdownMenuContent align={isRTL ? "start" : "end"}>
                                                {onEdit && (
                                                    <DropdownMenuItem
                                                        onClick={() => handleEdit(asset)}
                                                        className={isRTL ? 'flex-row-reverse' : ''}
                                                    >
                                                        <Edit className={`h-4 w-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                                                        Edit
                                                    </DropdownMenuItem>
                                                )}
                                                {onDelete && (
                                                    <DropdownMenuItem
                                                        onClick={() => handleDeleteClick(asset)}
                                                        className={`text-red-600 ${isRTL ? 'flex-row-reverse' : ''}`}
                                                    >
                                                        <Trash2 className={`h-4 w-4 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                                                        Delete
                                                    </DropdownMenuItem>
                                                )}
                                            </DropdownMenuContent>
                                        </DropdownMenu>
                                    )}
                                </TableCell>
                            </motion.tr>

                            {/* Expandable content row for hidden columns */}
                            {expandedRows.has(asset.id!) && getHiddenColumns().length > 0 && (
                                <motion.tr
                                    initial={{ opacity: 0, height: 0 }}
                                    animate={{ opacity: 1, height: 'auto' }}
                                    exit={{ opacity: 0, height: 0 }}
                                    className="bg-slate-50 border-b border-slate-200"
                                >
                                    <TableCell 
                                        colSpan={getVisibleColumns().length + (getHiddenColumns().length > 0 ? 2 : 1)}
                                        className="p-6"
                                    >
                                        <div className="space-y-4">
                                            <h4 className="text-sm font-semibold text-slate-700 mb-3">Additional Details</h4>
                                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                                {getHiddenColumns().map((column) => (
                                                    <div key={column.key} className="space-y-1">
                                                        <div className="text-xs font-medium text-slate-500 uppercase tracking-wide">
                                                            {column.label}
                                                        </div>
                                                        <div className="text-sm text-slate-700 font-medium">
                                                            {renderCellContent(column, asset)}
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                    </TableCell>
                                </motion.tr>
                            )}
                        </React.Fragment>
                        ))}

                        {/* Empty State */}
                        {filteredAndSortedAssets.length === 0 && !isAddingNew && (
                            <TableRow>
                                <TableCell 
                                    colSpan={columns.length + 1} 
                                    className="text-center py-20"
                                >
                                    <motion.div
                                        initial={{ opacity: 0, y: 10 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.5 }}
                                        className="max-w-md mx-auto"
                                    >
                                        <div className="w-16 h-16 bg-gradient-to-br from-[#003874] to-[#2D8DC6] rounded-2xl flex items-center justify-center mx-auto mb-6">
                                            <Plus className="h-8 w-8 text-white" />
                                        </div>
                                        <div className="text-gray-700 text-xl font-semibold mb-3">
                                            {emptyMessage}
                                        </div>
                                        <p className="text-gray-500 mb-8 text-sm leading-relaxed">{emptyDescription}</p>
                                        {onAdd && (
                                            <Button
                                                onClick={handleAddNew}
                                                className="bg-gradient-to-r from-[#003874] to-[#2D8DC6] hover:from-[#001f4d] hover:to-[#1e6fa8] text-white px-6 py-3 rounded-xl font-medium shadow-lg hover:shadow-xl transition-all duration-200"
                                            >
                                                <Plus className={`h-5 w-5 ${isRTL ? 'ml-2' : 'mr-2'}`} />
                                                {addButtonLabel}
                                            </Button>
                                        )}
                                    </motion.div>
                                </TableCell>
                            </TableRow>
                        )}
                    </TableBody>
                </Table>
            </motion.div>

            {/* Delete Confirmation Dialog */}
            <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                            This action cannot be undone. This will permanently delete the asset
                            and remove all associated data.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                            onClick={handleDeleteConfirm}
                            className="bg-red-600 hover:bg-red-700"
                        >
                            Delete
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>

            {/* Excel Upload Modal */}
            {onBulkImport && (
                <ExcelUploadModal
                    isOpen={showExcelUpload}
                    onClose={() => setShowExcelUpload(false)}
                    onImport={handleBulkImport}
                    columns={columns}
                    assetType={assetType}
                    templateFileName={templateFileName}
                    isRTL={isRTL}
                />
            )}
        </motion.div>
    );
} 
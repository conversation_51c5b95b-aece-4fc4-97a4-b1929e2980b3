"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { useTranslations } from "next-intl";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { 
    X, 
    Plus, 
    Minus, 
    Save, 
    Calendar,
    Shield,
    Clock,
    Database,
    Server
} from "lucide-react";
import { DatasetAsset, DatabaseAsset, SystemAsset } from "@/lib/services/dataAssetsService";
import { toast } from "sonner";

interface AddEditDatasetModalProps {
    isOpen: boolean;
    onClose: () => void;
    onSave: (dataset: Partial<DatasetAsset>) => Promise<void>;
    dataset?: DatasetAsset | null;
    databases: DatabaseAsset[];
    systems: SystemAsset[];
    isRTL?: boolean;
}

export function AddEditDatasetModal({
    isOpen,
    onClose,
    onSave,
    dataset,
    databases,
    systems,
    isRTL = false
}: AddEditDatasetModalProps) {
    const [formData, setFormData] = useState<Partial<DatasetAsset>>({
        name: '',
        description: '',
        databasesUsed: [],
        systemsThatUseIt: [],
        owner: '',
        size: '',
        updateFrequency: 'Monthly',
        sensitivity: 'Internal',
        format: ''
    });
    const [saving, setSaving] = useState(false);
    const [selectedDatabases, setSelectedDatabases] = useState<string[]>([]);
    const [selectedSystems, setSelectedSystems] = useState<string[]>([]);

    const datasetsT = useTranslations('Datasets');

    // Initialize form data when modal opens or dataset changes
    useEffect(() => {
        if (isOpen) {
            if (dataset) {
                setFormData({
                    name: dataset.name || '',
                    description: dataset.description || '',
                    databasesUsed: dataset.databasesUsed || [],
                    systemsThatUseIt: dataset.systemsThatUseIt || [],
                    owner: dataset.owner || '',
                    size: dataset.size || '',
                    updateFrequency: dataset.updateFrequency || 'Monthly',
                    sensitivity: dataset.sensitivity || 'Internal',
                    format: dataset.format || ''
                });
                setSelectedDatabases(dataset.databasesUsed || []);
                setSelectedSystems(dataset.systemsThatUseIt || []);
            } else {
                setFormData({
                    name: '',
                    description: '',
                    databasesUsed: [],
                    systemsThatUseIt: [],
                    owner: '',
                    size: '',
                    updateFrequency: 'Monthly',
                    sensitivity: 'Internal',
                    format: ''
                });
                setSelectedDatabases([]);
                setSelectedSystems([]);
            }
        }
    }, [isOpen, dataset]);

    // Handle escape key
    useEffect(() => {
        const handleEscape = (e: KeyboardEvent) => {
            if (e.key === 'Escape' && isOpen) {
                onClose();
            }
        };

        if (isOpen) {
            document.addEventListener('keydown', handleEscape);
            document.body.style.overflow = 'hidden';
        }

        return () => {
            document.removeEventListener('keydown', handleEscape);
            document.body.style.overflow = 'unset';
        };
    }, [isOpen, onClose]);

    const handleInputChange = (field: keyof DatasetAsset, value: any) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const handleDatabaseToggle = (databaseId: string) => {
        const newSelected = selectedDatabases.includes(databaseId)
            ? selectedDatabases.filter(id => id !== databaseId)
            : [...selectedDatabases, databaseId];
        
        setSelectedDatabases(newSelected);
        handleInputChange('databasesUsed', newSelected);
    };

    const handleSystemToggle = (systemId: string) => {
        const newSelected = selectedSystems.includes(systemId)
            ? selectedSystems.filter(id => id !== systemId)
            : [...selectedSystems, systemId];
        
        setSelectedSystems(newSelected);
        handleInputChange('systemsThatUseIt', newSelected);
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        
        // Validation
        if (!formData.name?.trim()) {
            toast.error('Dataset name is required');
            return;
        }
        if (!formData.description?.trim()) {
            toast.error('Description is required');
            return;
        }
        if (!formData.owner?.trim()) {
            toast.error('Owner is required');
            return;
        }
        if (!formData.size?.trim()) {
            toast.error('Size is required');
            return;
        }
        if (!formData.format?.trim()) {
            toast.error('Format is required');
            return;
        }

        setSaving(true);
        try {
            const datasetToSave = {
                ...formData,
                databasesUsed: selectedDatabases,
                systemsThatUseIt: selectedSystems,
                lastUpdated: new Date()
            };
            
            await onSave(datasetToSave);
            onClose();
        } catch (error) {
            console.error('Error saving dataset:', error);
            toast.error('Failed to save dataset');
        } finally {
            setSaving(false);
        }
    };

    if (!isOpen) return null;

    return (
        <div 
            className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/60 backdrop-blur-sm"
            onClick={(e) => {
                if (e.target === e.currentTarget) {
                    onClose();
                }
            }}
        >
            <motion.div
                initial={{ opacity: 0, scale: 0.95, y: 20 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.95, y: 20 }}
                className="relative w-full max-w-4xl max-h-[95vh] bg-white rounded-2xl shadow-2xl overflow-hidden"
                onClick={(e) => e.stopPropagation()}
            >
                {/* Header */}
                <div className="bg-gradient-to-r from-[#003874] via-[#2D8DC6] to-[#48D3A5] p-6">
                    <div className="flex items-center justify-between">
                        <h2 className="text-2xl font-bold text-white">
                            {dataset ? 'Edit Dataset' : 'Add New Dataset'}
                        </h2>
                        <Button
                            onClick={onClose}
                            variant="ghost"
                            size="sm"
                            className="text-white hover:bg-white/20"
                        >
                            <X className="h-4 w-4" />
                        </Button>
                    </div>
                </div>

                {/* Form */}
                <form onSubmit={handleSubmit} className="p-6 overflow-y-auto max-h-[80vh]">
                    <div className="space-y-6">
                        {/* Basic Information */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div className="space-y-2">
                                <Label htmlFor="name">Dataset Name *</Label>
                                <Input
                                    id="name"
                                    value={formData.name || ''}
                                    onChange={(e) => handleInputChange('name', e.target.value)}
                                    placeholder="Enter dataset name"
                                    required
                                />
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="owner">Owner *</Label>
                                <Input
                                    id="owner"
                                    value={formData.owner || ''}
                                    onChange={(e) => handleInputChange('owner', e.target.value)}
                                    placeholder="Enter dataset owner"
                                    required
                                />
                            </div>
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="description">Description *</Label>
                            <Textarea
                                id="description"
                                value={formData.description || ''}
                                onChange={(e) => handleInputChange('description', e.target.value)}
                                placeholder="Enter dataset description"
                                rows={3}
                                required
                            />
                        </div>

                        {/* Technical Details */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div className="space-y-2">
                                <Label htmlFor="size">Size *</Label>
                                <Input
                                    id="size"
                                    value={formData.size || ''}
                                    onChange={(e) => handleInputChange('size', e.target.value)}
                                    placeholder="e.g., 10GB, 500MB"
                                    required
                                />
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="format">Format *</Label>
                                <Input
                                    id="format"
                                    value={formData.format || ''}
                                    onChange={(e) => handleInputChange('format', e.target.value)}
                                    placeholder="e.g., CSV, JSON, XML"
                                    required
                                />
                            </div>
                        </div>

                        {/* Update Frequency & Sensitivity */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div className="space-y-2">
                                <Label htmlFor="updateFrequency" className="flex items-center gap-2">
                                    <Clock className="w-4 h-4" />
                                    Update Frequency
                                </Label>
                                <select
                                    id="updateFrequency"
                                    value={formData.updateFrequency || 'Monthly'}
                                    onChange={(e) => handleInputChange('updateFrequency', e.target.value)}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:border-[#003874] focus:ring-[#003874]"
                                >
                                    <option value="Real-time">Real-time</option>
                                    <option value="Daily">Daily</option>
                                    <option value="Weekly">Weekly</option>
                                    <option value="Monthly">Monthly</option>
                                    <option value="Quarterly">Quarterly</option>
                                    <option value="Annually">Annually</option>
                                </select>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="sensitivity" className="flex items-center gap-2">
                                    <Shield className="w-4 h-4" />
                                    Sensitivity Level
                                </Label>
                                <select
                                    id="sensitivity"
                                    value={formData.sensitivity || 'Internal'}
                                    onChange={(e) => handleInputChange('sensitivity', e.target.value)}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:border-[#003874] focus:ring-[#003874]"
                                >
                                    <option value="Public">Public</option>
                                    <option value="Internal">Internal</option>
                                    <option value="Confidential">Confidential</option>
                                    <option value="Restricted">Restricted</option>
                                </select>
                            </div>
                        </div>

                        {/* Databases Used */}
                        <div className="space-y-3">
                            <Label className="flex items-center gap-2">
                                <Database className="w-4 h-4" />
                                Databases Used
                            </Label>
                            <div className="border border-gray-200 rounded-lg p-4 max-h-40 overflow-y-auto">
                                {databases.length === 0 ? (
                                    <p className="text-gray-500 text-sm">No databases available</p>
                                ) : (
                                    <div className="space-y-2">
                                        {databases.map((database) => (
                                            <label
                                                key={database.id}
                                                className="flex items-center gap-3 p-2 hover:bg-gray-50 rounded cursor-pointer"
                                            >
                                                <input
                                                    type="checkbox"
                                                    checked={selectedDatabases.includes(database.id!)}
                                                    onChange={() => handleDatabaseToggle(database.id!)}
                                                    className="rounded border-gray-300 text-[#003874] focus:ring-[#003874]"
                                                />
                                                <span className="text-sm font-medium">{database.name}</span>
                                                <Badge variant="outline" className="text-xs">
                                                    {database.dbms}
                                                </Badge>
                                            </label>
                                        ))}
                                    </div>
                                )}
                            </div>
                            {selectedDatabases.length > 0 && (
                                <div className="flex flex-wrap gap-1 mt-2">
                                    {selectedDatabases.map((dbId) => {
                                        const db = databases.find(d => d.id === dbId);
                                        return db ? (
                                            <Badge key={dbId} variant="outline" className="text-xs">
                                                {db.name}
                                                <X 
                                                    className="w-3 h-3 ml-1 cursor-pointer hover:text-red-600"
                                                    onClick={() => handleDatabaseToggle(dbId)}
                                                />
                                            </Badge>
                                        ) : null;
                                    })}
                                </div>
                            )}
                        </div>

                        {/* Systems That Use It */}
                        <div className="space-y-3">
                            <Label className="flex items-center gap-2">
                                <Server className="w-4 h-4" />
                                Systems That Use This Dataset
                            </Label>
                            <div className="border border-gray-200 rounded-lg p-4 max-h-40 overflow-y-auto">
                                {systems.length === 0 ? (
                                    <p className="text-gray-500 text-sm">No systems available</p>
                                ) : (
                                    <div className="space-y-2">
                                        {systems.map((system) => (
                                            <label
                                                key={system.id}
                                                className="flex items-center gap-3 p-2 hover:bg-gray-50 rounded cursor-pointer"
                                            >
                                                <input
                                                    type="checkbox"
                                                    checked={selectedSystems.includes(system.id!)}
                                                    onChange={() => handleSystemToggle(system.id!)}
                                                    className="rounded border-gray-300 text-[#003874] focus:ring-[#003874]"
                                                />
                                                <span className="text-sm font-medium">{system.name}</span>
                                                <Badge variant="outline" className="text-xs">
                                                    {system.systemDomain}
                                                </Badge>
                                            </label>
                                        ))}
                                    </div>
                                )}
                            </div>
                            {selectedSystems.length > 0 && (
                                <div className="flex flex-wrap gap-1 mt-2">
                                    {selectedSystems.map((sysId) => {
                                        const sys = systems.find(s => s.id === sysId);
                                        return sys ? (
                                            <Badge key={sysId} variant="outline" className="text-xs bg-blue-50 text-blue-700">
                                                {sys.name}
                                                <X 
                                                    className="w-3 h-3 ml-1 cursor-pointer hover:text-red-600"
                                                    onClick={() => handleSystemToggle(sysId)}
                                                />
                                            </Badge>
                                        ) : null;
                                    })}
                                </div>
                            )}
                        </div>
                    </div>

                    {/* Form Actions */}
                    <div className="flex justify-end gap-3 mt-8 pt-6 border-t border-gray-200">
                        <Button
                            type="button"
                            variant="outline"
                            onClick={onClose}
                            disabled={saving}
                        >
                            Cancel
                        </Button>
                        <Button
                            type="submit"
                            disabled={saving}
                            className="bg-gradient-to-r from-[#003874] to-[#2D8DC6] hover:from-[#001f4d] hover:to-[#1e6fa8]"
                        >
                            {saving ? (
                                <>
                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                    Saving...
                                </>
                            ) : (
                                <>
                                    <Save className="w-4 h-4 mr-2" />
                                    {dataset ? 'Update Dataset' : 'Create Dataset'}
                                </>
                            )}
                        </Button>
                    </div>
                </form>
            </motion.div>
        </div>
    );
} 
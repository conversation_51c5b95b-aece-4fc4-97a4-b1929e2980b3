"use client";

import { useState, useRef, useCallback, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useTranslations } from "next-intl";
import * as ExcelJS from "exceljs";
// Dialog components removed - using custom modal overlay
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import {
    Upload,
    Download,
    FileSpreadsheet,
    CheckCircle,
    AlertCircle,
    ChevronLeft,
    ChevronRight,
    Loader2,
    X,
    Check
} from "lucide-react";
import { toast } from "sonner";
import { TableColumn } from "./AssetTable";

interface ExcelUploadModalProps<T> {
    isOpen: boolean;
    onClose: () => void;
    onImport: (data: Partial<T>[]) => Promise<void>;
    columns: TableColumn<T>[];
    assetType: string;
    templateFileName: string;
    maxFileSize?: number; // in MB
    pageSize?: number; // rows per page
    isRTL?: boolean;
}

interface ValidationError {
    row: number;
    column: string;
    message: string;
}

interface PreviewRow<T> {
    data: Partial<T>;
    errors: ValidationError[];
    selected: boolean;
}

const ROWS_PER_PAGE = 10;

export function ExcelUploadModal<T>({
    isOpen,
    onClose,
    onImport,
    columns,
    assetType,
    templateFileName,
    maxFileSize = 10,
    pageSize = ROWS_PER_PAGE,
    isRTL = false
}: ExcelUploadModalProps<T>) {
    const [step, setStep] = useState<'upload' | 'preview'>('upload');
    const [file, setFile] = useState<File | null>(null);
    const [processing, setProcessing] = useState(false);
    const [importing, setImporting] = useState(false);
    const [previewData, setPreviewData] = useState<PreviewRow<T>[]>([]);
    const [currentPage, setCurrentPage] = useState(0);
    const [dragActive, setDragActive] = useState(false);
    
    const fileInputRef = useRef<HTMLInputElement>(null);
    const systemsT = useTranslations('Systems');
    const bulkT = useTranslations('Systems.bulkUpload');

    // Reset state when modal opens/closes
    useEffect(() => {
        if (!isOpen) {
            setStep('upload');
            setFile(null);
            setPreviewData([]);
            setCurrentPage(0);
        }
    }, [isOpen]);

    // Handle escape key
    useEffect(() => {
        const handleEscape = (e: KeyboardEvent) => {
            if (e.key === 'Escape' && isOpen) {
                onClose();
            }
        };

        if (isOpen) {
            document.addEventListener('keydown', handleEscape);
            document.body.style.overflow = 'hidden'; // Prevent background scroll
        }

        return () => {
            document.removeEventListener('keydown', handleEscape);
            document.body.style.overflow = 'unset';
        };
    }, [isOpen, onClose]);

    const totalPages = Math.ceil(previewData.length / pageSize);
    const startIndex = currentPage * pageSize;
    const endIndex = Math.min(startIndex + pageSize, previewData.length);
    const currentPageData = previewData.slice(startIndex, endIndex);

    // Get editable columns for template
    const editableColumns = columns.filter(col => col.editable);

    // Generate Excel template
    const generateTemplate = useCallback(async () => {
        try {
            const workbook = new ExcelJS.Workbook();
            const worksheet = workbook.addWorksheet('Template');

            // Set headers
            const headers = editableColumns.map(col => bulkT(`templateColumns.${col.key === 'name' ? 'systemName' : col.key}`) || col.label);
            worksheet.addRow(headers);

            // Style headers
            const headerRow = worksheet.getRow(1);
            headerRow.font = { bold: true, color: { argb: 'FFFFFF' } };
            headerRow.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: '003874' }
            };

            // Add example rows
            const exampleRows = [
                editableColumns.map(col => {
                    switch (col.key) {
                        case 'name':
                        case 'systemName':
                            return 'Customer Management System';
                        case 'description':
                            return 'System for managing customer relationships and data';
                        case 'systemDomain':
                            return 'Sales';
                        case 'ownerDepartment':
                            return 'Sales Department';
                        case 'status':
                            return col.options?.[0] || 'Active';
                        default:
                            return col.placeholder || `Example ${col.label}`;
                    }
                }),
                editableColumns.map(col => {
                    switch (col.key) {
                        case 'name':
                        case 'systemName':
                            return 'Financial Reporting System';
                        case 'description':
                            return 'System for generating financial reports and analytics';
                        case 'systemDomain':
                            return 'Finance';
                        case 'ownerDepartment':
                            return 'Finance Department';
                        case 'status':
                            return col.options?.[1] || 'Active';
                        default:
                            return col.placeholder || `Example ${col.label}`;
                    }
                })
            ];

            exampleRows.forEach(row => worksheet.addRow(row));

            // Auto-fit columns
            worksheet.columns.forEach((column, index) => {
                const header = headers[index];
                column.width = Math.max(header.length, 20);
            });

            // Add data validation for select fields
            editableColumns.forEach((col, index) => {
                if (col.type === 'select' && col.options) {
                    const columnLetter = String.fromCharCode(65 + index);
                    const cellRange = `${columnLetter}2:${columnLetter}1000`;
                    
                    // Add a comment to indicate available options
                    const headerCell = worksheet.getCell(`${columnLetter}1`);
                    headerCell.note = `Available options: ${col.options.join(', ')}`;
                }
            });

            // Generate buffer and download
            const buffer = await workbook.xlsx.writeBuffer();
            const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
            const url = URL.createObjectURL(blob);
            
            const link = document.createElement('a');
            link.href = url;
            link.download = `${templateFileName}_template.xlsx`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);

            toast.success(bulkT('downloadTemplate'), {
                description: `Template downloaded: ${templateFileName}_template.xlsx`
            });
        } catch (error) {
            console.error('Error generating template:', error);
            toast.error('Failed to generate template');
        }
    }, [editableColumns, templateFileName, bulkT]);

    // Validate row data
    const validateRow = (data: Partial<T>, rowIndex: number): ValidationError[] => {
        const errors: ValidationError[] = [];

        editableColumns.forEach(col => {
            const value = data[col.key as keyof T];

            // Check required fields
            if (col.required && (!value || value.toString().trim() === '')) {
                errors.push({
                    row: rowIndex,
                    column: col.key,
                    message: bulkT('validation.required')
                });
            }

            // Check select options
            if (col.type === 'select' && col.options && value) {
                if (!col.options.includes(value.toString())) {
                    errors.push({
                        row: rowIndex,
                        column: col.key,
                        message: bulkT('validation.invalidStatus')
                    });
                }
            }

            // Check text length
            if (value && value.toString().length > 255) {
                errors.push({
                    row: rowIndex,
                    column: col.key,
                    message: bulkT('validation.tooLong')
                });
            }
        });

        return errors;
    };

    // Check for duplicates
    const checkDuplicates = (data: Partial<T>[]): ValidationError[] => {
        const errors: ValidationError[] = [];
        const nameMap = new Map<string, number[]>();

        data.forEach((item, index) => {
            const nameField = editableColumns.find(col => col.key === 'name' || col.key === 'systemName');
            if (nameField) {
                const name = item[nameField.key as keyof T];
                if (name) {
                    const nameStr = name.toString().toLowerCase().trim();
                    if (!nameMap.has(nameStr)) {
                        nameMap.set(nameStr, []);
                    }
                    nameMap.get(nameStr)!.push(index);
                }
            }
        });

        nameMap.forEach((indices, name) => {
            if (indices.length > 1) {
                indices.forEach(index => {
                    errors.push({
                        row: index,
                        column: editableColumns.find(col => col.key === 'name' || col.key === 'systemName')?.key || 'name',
                        message: bulkT('validation.duplicate')
                    });
                });
            }
        });

        return errors;
    };

    // Process Excel file
    const processFile = async (file: File) => {
        setProcessing(true);
        try {
            const buffer = await file.arrayBuffer();
            const workbook = new ExcelJS.Workbook();
            await workbook.xlsx.load(buffer);

            const worksheet = workbook.getWorksheet(1);
            if (!worksheet) {
                throw new Error('No worksheet found');
            }

            const rows: Partial<T>[] = [];
            const headerRow = worksheet.getRow(1);
            
            // Map headers to column keys
            const headerMap = new Map<number, string>();
            headerRow.eachCell((cell, colNumber) => {
                const headerText = cell.value?.toString().toLowerCase().trim();
                const matchingCol = editableColumns.find(col => {
                    const translatedHeader = bulkT(`templateColumns.${col.key}`).toLowerCase();
                    const originalHeader = col.label.toLowerCase();
                    return translatedHeader === headerText || originalHeader === headerText;
                });
                if (matchingCol) {
                    headerMap.set(colNumber, matchingCol.key);
                }
            });

            // Process data rows
            worksheet.eachRow((row, rowNumber) => {
                if (rowNumber === 1) return; // Skip header

                const rowData: Partial<T> = {};
                let hasData = false;

                row.eachCell((cell, colNumber) => {
                    const columnKey = headerMap.get(colNumber);
                    if (columnKey && cell.value) {
                        const value = cell.value.toString().trim();
                        if (value) {
                            (rowData as any)[columnKey] = value;
                            hasData = true;
                        }
                    }
                });

                if (hasData) {
                    rows.push(rowData);
                }
            });

            if (rows.length === 0) {
                throw new Error(bulkT('noDataFound'));
            }

            // Validate data
            const duplicateErrors = checkDuplicates(rows);
            const previewRows: PreviewRow<T>[] = rows.map((data, index) => {
                const rowErrors = validateRow(data, index);
                const duplicateErrorsForRow = duplicateErrors.filter(err => err.row === index);
                return {
                    data,
                    errors: [...rowErrors, ...duplicateErrorsForRow],
                    selected: true
                };
            });

            setPreviewData(previewRows);
            setStep('preview');
            toast.success(`${rows.length} ${bulkT('rowsFound')}`);

        } catch (error) {
            console.error('Error processing file:', error);
            toast.error(bulkT('invalidFormat'), {
                description: error instanceof Error ? error.message : 'Unknown error'
            });
        } finally {
            setProcessing(false);
        }
    };

    // Handle file selection
    const handleFileSelect = (file: File) => {
        // Validate file type
        if (!file.name.match(/\.(xlsx|xls)$/i)) {
            toast.error(bulkT('invalidFormat'));
            return;
        }

        // Validate file size
        if (file.size > maxFileSize * 1024 * 1024) {
            toast.error(bulkT('fileTooLarge'));
            return;
        }

        setFile(file);
        processFile(file);
    };

    // Drag and drop handlers
    const handleDrag = useCallback((e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();
        if (e.type === 'dragenter' || e.type === 'dragover') {
            setDragActive(true);
        } else if (e.type === 'dragleave') {
            setDragActive(false);
        }
    }, []);

    const handleDrop = useCallback((e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();
        setDragActive(false);
        
        if (e.dataTransfer.files && e.dataTransfer.files[0]) {
            handleFileSelect(e.dataTransfer.files[0]);
        }
    }, []);

    // Import data
    const handleImport = async () => {
        const selectedRows = previewData.filter(row => row.selected && row.errors.length === 0);
        
        if (selectedRows.length === 0) {
            toast.error('No valid rows selected for import');
            return;
        }

        setImporting(true);
        try {
            await onImport(selectedRows.map(row => row.data));
            toast.success(`${bulkT('importSuccess')} ${selectedRows.length} ${assetType}`);
            onClose();
        } catch (error) {
            console.error('Import failed:', error);
            toast.error(bulkT('importError'));
        } finally {
            setImporting(false);
        }
    };

    // Selection handlers
    const handleSelectAll = () => {
        setPreviewData(prev => prev.map(row => ({ ...row, selected: true })));
    };

    const handleDeselectAll = () => {
        setPreviewData(prev => prev.map(row => ({ ...row, selected: false })));
    };

    const handleRowToggle = (index: number) => {
        setPreviewData(prev => prev.map((row, i) => 
            i === startIndex + index ? { ...row, selected: !row.selected } : row
        ));
    };

    const validRowsCount = previewData.filter(row => row.errors.length === 0).length;
    const selectedValidRowsCount = previewData.filter(row => row.selected && row.errors.length === 0).length;
    const hasErrors = previewData.some(row => row.errors.length > 0);

    return (
        <>
            {isOpen && (
                <div 
                    className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/60 backdrop-blur-sm"
                    onClick={(e) => {
                        if (e.target === e.currentTarget) {
                            onClose();
                        }
                    }}
                    onKeyDown={(e) => {
                        if (e.key === 'Escape') {
                            onClose();
                        }
                    }}
                    tabIndex={-1}
                >
                    <div 
                        className="relative w-full max-w-6xl max-h-[95vh] bg-white rounded-2xl shadow-2xl overflow-hidden"
                        onClick={(e) => e.stopPropagation()}
                    >
                <div className="relative bg-gradient-to-r from-[#003874] via-[#2D8DC6] to-[#48D3A5] p-8 rounded-t-2xl">
                    {/* Close button */}
                    <Button
                        onClick={onClose}
                        variant="ghost"
                        size="sm"
                        className="absolute top-4 right-4 text-white hover:bg-white/20 z-10"
                    >
                        <X className="h-4 w-4" />
                    </Button>

                    {/* Hero content */}
                    <div className="text-center text-white">
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            className="flex items-center justify-center mb-4"
                        >
                            <FileSpreadsheet className="h-12 w-12 mr-4" />
                            <div>
                                <h2 className="text-2xl font-bold text-white">
                                    {bulkT('title')}
                                </h2>
                                <p className="text-white/80 text-lg">
                                    {bulkT('subtitle')}
                                </p>
                            </div>
                        </motion.div>

                        {/* Step indicator */}
                        <div className={`flex justify-center items-center mt-6 ${isRTL ? 'space-x-reverse space-x-4' : 'space-x-4'}`}>
                            <div className={`flex items-center ${step === 'upload' ? 'text-white' : 'text-white/60'} ${isRTL ? 'flex-row-reverse' : ''}`}>
                                <div className={`w-10 h-10 rounded-full flex items-center justify-center border-2 font-bold ${
                                    step === 'upload' ? 'border-white bg-white text-[#003874] shadow-lg' : 'border-white/60'
                                }`}>
                                    1
                                </div>
                                <span className={`font-medium text-lg ${isRTL ? 'mr-3' : 'ml-3'}`}>{bulkT('uploadButton')}</span>
                            </div>
                            <div className="w-12 h-1 bg-white/40 rounded-full"></div>
                            <div className={`flex items-center ${step === 'preview' ? 'text-white' : 'text-white/60'} ${isRTL ? 'flex-row-reverse' : ''}`}>
                                <div className={`w-10 h-10 rounded-full flex items-center justify-center border-2 font-bold ${
                                    step === 'preview' ? 'border-white bg-white text-[#003874] shadow-lg' : 'border-white/60'
                                }`}>
                                    2
                                </div>
                                <span className={`font-medium text-lg ${isRTL ? 'mr-3' : 'ml-3'}`}>{bulkT('preview')}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div className="p-8 overflow-y-auto max-h-[70vh] bg-white rounded-b-2xl">
                    <AnimatePresence mode="wait">
                        {step === 'upload' && (
                            <motion.div
                                key="upload"
                                initial={{ opacity: 0, x: 20 }}
                                animate={{ opacity: 1, x: 0 }}
                                exit={{ opacity: 0, x: -20 }}
                                className="space-y-6"
                            >
                                {/* Download template */}
                                <div className="text-center mb-8">
                                    <Button
                                        onClick={generateTemplate}
                                        variant="outline"
                                        className="border-2 border-[#003874] text-[#003874] hover:bg-[#003874] hover:text-white px-6 py-3 text-lg font-medium shadow-lg hover:shadow-xl transition-all duration-200"
                                    >
                                        <Download className={`h-5 w-5 ${isRTL ? 'ml-3' : 'mr-3'}`} />
                                        {bulkT('downloadTemplate')}
                                    </Button>
                                    <p className="text-sm text-gray-600 mt-3">
                                        Download template to see the required format
                                    </p>
                                </div>

                                {/* Upload area */}
                                <div
                                    className={`relative border-3 border-dashed rounded-2xl p-16 text-center transition-all duration-300 bg-gradient-to-br ${
                                        dragActive 
                                            ? 'border-[#003874] from-blue-50 to-indigo-50 scale-[1.02] shadow-2xl' 
                                            : 'border-gray-300 from-gray-50 to-white hover:border-[#003874] hover:from-blue-50 hover:to-indigo-50 hover:shadow-xl'
                                    }`}
                                    onDragEnter={handleDrag}
                                    onDragLeave={handleDrag}
                                    onDragOver={handleDrag}
                                    onDrop={handleDrop}
                                >
                                    <input
                                        ref={fileInputRef}
                                        type="file"
                                        accept=".xlsx,.xls"
                                        onChange={(e) => e.target.files?.[0] && handleFileSelect(e.target.files[0])}
                                        className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                                        disabled={processing}
                                    />
                                    
                                    {processing ? (
                                        <div className="space-y-6">
                                            <div className="relative">
                                                <div className="w-20 h-20 border-4 border-[#003874]/20 rounded-full mx-auto"></div>
                                                <Loader2 className="h-20 w-20 animate-spin text-[#003874] mx-auto absolute inset-0" />
                                            </div>
                                            <div>
                                                <p className="text-xl font-semibold text-gray-900 mb-2">{bulkT('processing')}</p>
                                                <p className="text-gray-600">Please wait while we analyze your file...</p>
                                            </div>
                                        </div>
                                    ) : (
                                        <div className="space-y-6">
                                            <div className="relative">
                                                <div className="w-24 h-24 bg-gradient-to-br from-[#003874] to-[#2D8DC6] rounded-full flex items-center justify-center mx-auto shadow-xl">
                                                    <Upload className="h-12 w-12 text-white" />
                                                </div>
                                                <div className="absolute -top-2 -right-2 w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                                    <span className="text-white text-lg">+</span>
                                                </div>
                                            </div>
                                            <div>
                                                <p className="text-2xl font-bold text-gray-900 mb-3">
                                                    {bulkT('dragDropText')}
                                                </p>
                                                <div className="space-y-2">
                                                    <div className="flex items-center justify-center gap-2">
                                                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                                        <p className="text-sm font-medium text-gray-700">
                                                            {bulkT('supportedFormats')}
                                                        </p>
                                                    </div>
                                                    <div className="flex items-center justify-center gap-2">
                                                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                                                        <p className="text-sm font-medium text-gray-700">
                                                            {bulkT('maxFileSize')}
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </motion.div>
                        )}

                        {step === 'preview' && (
                            <motion.div
                                key="preview"
                                initial={{ opacity: 0, x: 20 }}
                                animate={{ opacity: 1, x: 0 }}
                                exit={{ opacity: 0, x: -20 }}
                                className="space-y-6"
                            >
                                {/* Preview header */}
                                <div className="flex items-center justify-between">
                                    <div>
                                        <h3 className="text-xl font-semibold text-gray-900">{bulkT('preview')}</h3>
                                        <p className="text-gray-600">{bulkT('previewSubtitle')}</p>
                                    </div>
                                    <Button
                                        onClick={() => setStep('upload')}
                                        variant="outline"
                                        className={isRTL ? 'flex-row-reverse' : ''}
                                    >
                                        {isRTL ? (
                                            <ChevronRight className="h-4 w-4 ml-2" />
                                        ) : (
                                            <ChevronLeft className="h-4 w-4 mr-2" />
                                        )}
                                        {bulkT('back')}
                                    </Button>
                                </div>

                                {/* Stats */}
                                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                                    <div className="bg-blue-50 p-4 rounded-lg">
                                        <div className="text-2xl font-bold text-blue-600">{previewData.length}</div>
                                        <div className="text-sm text-blue-800">Total Rows</div>
                                    </div>
                                    <div className="bg-green-50 p-4 rounded-lg">
                                        <div className="text-2xl font-bold text-green-600">{validRowsCount}</div>
                                        <div className="text-sm text-green-800">Valid Rows</div>
                                    </div>
                                    <div className="bg-red-50 p-4 rounded-lg">
                                        <div className="text-2xl font-bold text-red-600">{previewData.length - validRowsCount}</div>
                                        <div className="text-sm text-red-800">Rows with Errors</div>
                                    </div>
                                    <div className="bg-purple-50 p-4 rounded-lg">
                                        <div className="text-2xl font-bold text-purple-600">{selectedValidRowsCount}</div>
                                        <div className="text-sm text-purple-800">Selected for Import</div>
                                    </div>
                                </div>

                                {/* Status message */}
                                {hasErrors ? (
                                    <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                                        <div className="flex items-center">
                                            <AlertCircle className="h-5 w-5 text-amber-600 mr-2" />
                                            <span className="text-amber-800 font-medium">{bulkT('hasErrors')}</span>
                                        </div>
                                        <p className="text-amber-700 text-sm mt-1">{bulkT('fixErrors')}</p>
                                    </div>
                                ) : (
                                    <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                                        <div className="flex items-center">
                                            <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
                                            <span className="text-green-800 font-medium">{bulkT('validData')}</span>
                                        </div>
                                    </div>
                                )}

                                {/* Selection controls */}
                                <div className="flex items-center gap-4">
                                    <Button
                                        onClick={handleSelectAll}
                                        variant="outline"
                                        size="sm"
                                    >
                                        {bulkT('selectAll')}
                                    </Button>
                                    <Button
                                        onClick={handleDeselectAll}
                                        variant="outline"
                                        size="sm"
                                    >
                                        {bulkT('deselectAll')}
                                    </Button>
                                </div>

                                {/* Preview table */}
                                <div className="border rounded-lg overflow-hidden">
                                    <Table>
                                        <TableHeader>
                                            <TableRow className="bg-gray-50">
                                                <TableHead className="w-12">
                                                    <Checkbox
                                                        checked={currentPageData.every(row => row.selected)}
                                                        onCheckedChange={(checked) => {
                                                            if (checked) {
                                                                currentPageData.forEach((_, index) => {
                                                                    setPreviewData(prev => prev.map((row, i) => 
                                                                        i >= startIndex && i < endIndex ? { ...row, selected: true } : row
                                                                    ));
                                                                });
                                                            } else {
                                                                currentPageData.forEach((_, index) => {
                                                                    setPreviewData(prev => prev.map((row, i) => 
                                                                        i >= startIndex && i < endIndex ? { ...row, selected: false } : row
                                                                    ));
                                                                });
                                                            }
                                                        }}
                                                    />
                                                </TableHead>
                                                <TableHead className="w-16">#</TableHead>
                                                {editableColumns.map(col => (
                                                    <TableHead key={col.key}>
                                                        {bulkT(`templateColumns.${col.key === 'name' ? 'systemName' : col.key}`) || col.label}
                                                        {col.required && <span className="text-red-500 ml-1">*</span>}
                                                    </TableHead>
                                                ))}
                                                <TableHead>Status</TableHead>
                                            </TableRow>
                                        </TableHeader>
                                        <TableBody>
                                            {currentPageData.map((row, index) => (
                                                <TableRow
                                                    key={startIndex + index}
                                                    className={`${row.errors.length > 0 ? 'bg-red-50' : row.selected ? 'bg-blue-50' : ''}`}
                                                >
                                                    <TableCell>
                                                        <Checkbox
                                                            checked={row.selected}
                                                            onCheckedChange={() => handleRowToggle(index)}
                                                            disabled={row.errors.length > 0}
                                                        />
                                                    </TableCell>
                                                    <TableCell className="font-medium">
                                                        {startIndex + index + 1}
                                                    </TableCell>
                                                    {editableColumns.map(col => (
                                                        <TableCell key={col.key}>
                                                            <div>
                                                                {row.data[col.key as keyof T]?.toString() || '-'}
                                                                {row.errors.filter(err => err.column === col.key).map((error, errIndex) => (
                                                                    <div key={errIndex} className="text-xs text-red-600 mt-1">
                                                                        {error.message}
                                                                    </div>
                                                                ))}
                                                            </div>
                                                        </TableCell>
                                                    ))}
                                                    <TableCell>
                                                        {row.errors.length === 0 ? (
                                                            <Badge className="bg-green-100 text-green-800">
                                                                <Check className="h-3 w-3 mr-1" />
                                                                Valid
                                                            </Badge>
                                                        ) : (
                                                            <Badge variant="destructive">
                                                                <AlertCircle className="h-3 w-3 mr-1" />
                                                                {row.errors.length} Error{row.errors.length > 1 ? 's' : ''}
                                                            </Badge>
                                                        )}
                                                    </TableCell>
                                                </TableRow>
                                            ))}
                                        </TableBody>
                                    </Table>
                                </div>

                                {/* Pagination */}
                                {totalPages > 1 && (
                                    <div className="flex items-center justify-between">
                                        <div className="text-sm text-gray-600">
                                            {bulkT('showing')} {startIndex + 1}-{endIndex} {bulkT('of')} {previewData.length} {bulkT('rows')}
                                        </div>
                                        <div className="flex items-center gap-2">
                                            <Button
                                                onClick={() => setCurrentPage(prev => Math.max(0, prev - 1))}
                                                disabled={currentPage === 0}
                                                variant="outline"
                                                size="sm"
                                            >
                                                <ChevronLeft className="h-4 w-4" />
                                                {bulkT('previous')}
                                            </Button>
                                            <span className="text-sm font-medium px-3">
                                                {currentPage + 1} / {totalPages}
                                            </span>
                                            <Button
                                                onClick={() => setCurrentPage(prev => Math.min(totalPages - 1, prev + 1))}
                                                disabled={currentPage === totalPages - 1}
                                                variant="outline"
                                                size="sm"
                                            >
                                                {bulkT('next')}
                                                <ChevronRight className="h-4 w-4" />
                                            </Button>
                                        </div>
                                    </div>
                                )}

                                {/* Import button */}
                                <div className="flex justify-end pt-4 border-t">
                                    <Button
                                        onClick={handleImport}
                                        disabled={selectedValidRowsCount === 0 || importing}
                                        className="bg-gradient-to-r from-[#003874] to-[#2D8DC6] hover:from-[#001f4d] hover:to-[#1e6fa8]"
                                    >
                                        {importing ? (
                                            <>
                                                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                                Importing...
                                            </>
                                        ) : (
                                            <>
                                                <Upload className="h-4 w-4 mr-2" />
                                                {bulkT('importButton')} ({selectedValidRowsCount})
                                            </>
                                        )}
                                    </Button>
                                </div>
                            </motion.div>
                        )}
                    </AnimatePresence>
                        </div>
                    </div>
                </div>
            )}
        </>
    );
} 
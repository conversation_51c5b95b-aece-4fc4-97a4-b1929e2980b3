"use client";

import { motion } from "framer-motion";
import { useTranslations } from "next-intl";
import { 
    Server, 
    Activity, 
    Shield, 
    AlertTriangle,
    TrendingUp,
    Users,
    Building
} from "lucide-react";
import { SystemAsset } from "@/lib/services/dataAssetsService";

interface SystemKPIsProps {
    systems: SystemAsset[];
    isRTL?: boolean;
    loading?: boolean;
}

interface KPIData {
    key: string;
    value: number | string;
    label: string;
    description: string;
    icon: React.ElementType;
    gradientFrom: string;
    gradientTo: string;
    textColor: string;
    percentage?: number;
    trend?: 'up' | 'down' | 'stable';
}

const cardVariants = {
    hidden: { 
        opacity: 0, 
        y: 30,
        scale: 0.95
    },
    visible: {
        opacity: 1,
        y: 0,
        scale: 1,
        transition: {
            type: "spring",
            stiffness: 300,
            damping: 30,
            duration: 0.6
        }
    }
};

const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
        opacity: 1,
        transition: {
            staggerChildren: 0.1,
            delayChildren: 0.2
        }
    }
};

export function SystemKPIs({ systems, isRTL = false, loading = false }: SystemKPIsProps) {
    const systemsT = useTranslations('Systems.analytics');

    // Calculate KPI data from real systems
    const calculateKPIs = (): KPIData[] => {
        const totalSystems = systems.length;
        const activeSystems = systems.filter(s => s.status === 'Active').length;
        const inactiveSystems = systems.filter(s => s.status === 'Inactive').length;
        const retiredSystems = systems.filter(s => s.status === 'Retired').length;
        const underReviewSystems = systems.filter(s => s.status === 'Under Review').length;
        
        // Calculate health percentage (Active systems are healthy)
        const healthPercentage = totalSystems > 0 ? Math.round((activeSystems / totalSystems) * 100) : 0;
        
        // Risk systems = Inactive + Under Review
        const riskSystems = inactiveSystems + underReviewSystems;
        
        // Unique domains count
        const uniqueDomains = new Set(systems.map(s => s.systemDomain)).size;
        
        // Unique departments count
        const uniqueDepartments = new Set(systems.map(s => s.ownerDepartment)).size;

        return [
            {
                key: 'totalSystems',
                value: totalSystems,
                label: systemsT('kpis.totalSystems'),
                description: systemsT('insights.totalSystemsDesc'),
                icon: Server,
                gradientFrom: '#003874',
                gradientTo: '#2D8DC6',
                textColor: 'text-white',
                trend: 'stable'
            },
            {
                key: 'activeSystems',
                value: activeSystems,
                label: systemsT('kpis.activeSystems'),
                description: systemsT('insights.activeSystemsDesc'),
                icon: Activity,
                gradientFrom: '#10B981',
                gradientTo: '#34D399',
                textColor: 'text-white',
                percentage: totalSystems > 0 ? Math.round((activeSystems / totalSystems) * 100) : 0,
                trend: 'up'
            },
            {
                key: 'systemHealth',
                value: `${healthPercentage}%`,
                label: systemsT('kpis.systemHealth'),
                description: systemsT('insights.systemHealthDesc'),
                icon: Shield,
                gradientFrom: healthPercentage >= 80 ? '#10B981' : healthPercentage >= 60 ? '#F59E0B' : '#EF4444',
                gradientTo: healthPercentage >= 80 ? '#34D399' : healthPercentage >= 60 ? '#FCD34D' : '#F87171',
                textColor: 'text-white',
                percentage: healthPercentage,
                trend: healthPercentage >= 80 ? 'up' : healthPercentage >= 60 ? 'stable' : 'down'
            },
            {
                key: 'riskSystems',
                value: riskSystems,
                label: systemsT('kpis.riskSystems'),
                description: systemsT('insights.riskSystemsDesc'),
                icon: AlertTriangle,
                gradientFrom: riskSystems === 0 ? '#10B981' : riskSystems <= totalSystems * 0.2 ? '#F59E0B' : '#EF4444',
                gradientTo: riskSystems === 0 ? '#34D399' : riskSystems <= totalSystems * 0.2 ? '#FCD34D' : '#F87171',
                textColor: 'text-white',
                percentage: totalSystems > 0 ? Math.round((riskSystems / totalSystems) * 100) : 0,
                trend: riskSystems === 0 ? 'up' : 'down'
            },
            {
                key: 'systemsByDomain',
                value: uniqueDomains,
                label: systemsT('kpis.systemsByDomain'),
                description: 'Number of unique business domains covered',
                icon: Building,
                gradientFrom: '#8B5CF6',
                gradientTo: '#A78BFA',
                textColor: 'text-white',
                trend: 'stable'
            },
            {
                key: 'systemsByDepartment',
                value: uniqueDepartments,
                label: systemsT('kpis.systemsByDepartment'),
                description: 'Number of departments managing systems',
                icon: Users,
                gradientFrom: '#06B6D4',
                gradientTo: '#67E8F9',
                textColor: 'text-white',
                trend: 'stable'
            }
        ];
    };

    const kpiData = calculateKPIs();

    if (loading) {
        return (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {[...Array(6)].map((_, index) => (
                    <div
                        key={index}
                        className="h-40 bg-gradient-to-br from-slate-200 to-slate-300 rounded-2xl animate-pulse"
                    />
                ))}
            </div>
        );
    }

    return (
        <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
        >
            {kpiData.map((kpi, index) => {
                const Icon = kpi.icon;
                
                return (
                    <motion.div
                        key={kpi.key}
                        variants={cardVariants}
                        className="group relative overflow-hidden"
                    >
                        <div 
                            className="relative h-40 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:scale-105 cursor-pointer"
                            style={{
                                background: `linear-gradient(135deg, ${kpi.gradientFrom} 0%, ${kpi.gradientTo} 100%)`
                            }}
                        >
                            {/* Decorative elements */}
                            <motion.div
                                className={`absolute top-0 w-32 h-32 rounded-full opacity-20 bg-white ${isRTL ? 'left-0' : 'right-0'}`}
                                initial={{ x: isRTL ? -50 : 50, y: -50 }}
                                animate={{
                                    x: 0,
                                    y: 0,
                                    scale: [1, 1.1, 1],
                                    rotate: [0, 180, 360],
                                }}
                                transition={{ 
                                    duration: 20 + index * 2, 
                                    repeat: Infinity, 
                                    repeatType: "reverse" 
                                }}
                            />
                            <motion.div
                                className={`absolute bottom-0 w-20 h-20 rounded-full opacity-15 bg-white ${isRTL ? 'right-1/4' : 'left-1/4'}`}
                                initial={{ y: 25 }}
                                animate={{
                                    y: [0, -10, 0],
                                    scale: [1, 1.05, 1],
                                }}
                                transition={{ 
                                    duration: 8 + index, 
                                    repeat: Infinity, 
                                    repeatType: "reverse" 
                                }}
                            />

                            {/* Content */}
                            <div className="relative z-10 p-6 h-full flex flex-col justify-between">
                                {/* Header with icon and trend */}
                                <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                                    <div className="p-3 bg-white/20 backdrop-blur-md rounded-xl shadow-lg group-hover:scale-110 transition-transform duration-300">
                                        <Icon className="h-6 w-6 text-white" />
                                    </div>
                                    
                                    {kpi.trend && (
                                        <div className={`flex items-center gap-1 bg-white/20 backdrop-blur-md px-2 py-1 rounded-lg ${isRTL ? 'flex-row-reverse' : ''}`}>
                                            <TrendingUp 
                                                className={`h-4 w-4 text-white ${
                                                    kpi.trend === 'down' ? 'rotate-180' : 
                                                    kpi.trend === 'stable' ? 'rotate-90' : ''
                                                }`} 
                                            />
                                        </div>
                                    )}
                                </div>

                                {/* Value and label */}
                                <div className={`${isRTL ? 'text-right' : 'text-left'}`}>
                                    <div className="flex items-baseline gap-2 mb-1">
                                        <motion.span 
                                            className={`text-3xl font-black ${kpi.textColor} ${isRTL ? 'font-arabic' : ''}`}
                                            initial={{ scale: 0.8, opacity: 0 }}
                                            animate={{ scale: 1, opacity: 1 }}
                                            transition={{ delay: 0.3 + index * 0.1 }}
                                        >
                                            {kpi.value}
                                        </motion.span>
                                        {kpi.percentage !== undefined && kpi.key !== 'systemHealth' && (
                                            <span className="text-sm font-medium text-white/80">
                                                ({kpi.percentage}%)
                                            </span>
                                        )}
                                    </div>
                                    <motion.h3 
                                        className={`text-sm font-bold text-white mb-1 ${isRTL ? 'font-arabic' : ''}`}
                                        initial={{ y: 10, opacity: 0 }}
                                        animate={{ y: 0, opacity: 1 }}
                                        transition={{ delay: 0.4 + index * 0.1 }}
                                    >
                                        {kpi.label}
                                    </motion.h3>
                                    <motion.p 
                                        className={`text-xs text-white/80 leading-tight ${isRTL ? 'font-arabic' : ''}`}
                                        initial={{ y: 10, opacity: 0 }}
                                        animate={{ y: 0, opacity: 1 }}
                                        transition={{ delay: 0.5 + index * 0.1 }}
                                    >
                                        {kpi.description}
                                    </motion.p>
                                </div>
                            </div>

                            {/* Hover effect overlay */}
                            <div className="absolute inset-0 bg-white/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl" />
                        </div>
                    </motion.div>
                );
            })}
        </motion.div>
    );
} 
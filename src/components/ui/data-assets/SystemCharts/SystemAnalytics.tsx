"use client";

import { motion } from "framer-motion";
import { useTranslations } from "next-intl";
import { SystemAsset } from "@/lib/services/dataAssetsService";
import { SystemKPIs } from "./SystemKPIs";
import { SystemCharts } from "./SystemCharts";
import { BarChart3, Loader2 } from "lucide-react";

interface SystemAnalyticsProps {
    systems: SystemAsset[];
    isRTL?: boolean;
    loading?: boolean;
}

const sectionVariants = {
    hidden: { 
        opacity: 0, 
        y: 20 
    },
    visible: {
        opacity: 1,
        y: 0,
        transition: {
            duration: 0.6,
            staggerChildren: 0.2
        }
    }
};

const titleVariants = {
    hidden: { 
        opacity: 0, 
        x: -30 
    },
    visible: {
        opacity: 1,
        x: 0,
        transition: {
            type: "spring",
            stiffness: 300,
            damping: 30
        }
    }
};

export function SystemAnalytics({ systems, isRTL = false, loading = false }: SystemAnalyticsProps) {
    const systemsT = useTranslations('Systems.analytics');

    if (loading) {
        return (
            <div className="flex items-center justify-center py-20">
                <div className="text-center">
                    <Loader2 className="h-12 w-12 animate-spin text-[#003874] mx-auto mb-4" />
                    <p className="text-gray-600 text-lg">{systemsT('loadingCharts')}</p>
                </div>
            </div>
        );
    }

    return (
        <motion.div
            variants={sectionVariants}
            initial="hidden"
            animate="visible"
            className="space-y-12"
        >
            {/* Analytics Header */}
            <motion.div 
                variants={titleVariants}
                className={`${isRTL ? 'text-right' : 'text-left'}`}
            >
                <div className={`flex items-center gap-4 mb-6 ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <div className="p-3 bg-gradient-to-r from-[#003874] to-[#2D8DC6] rounded-2xl shadow-lg">
                        <BarChart3 className="h-8 w-8 text-white" />
                    </div>
                    <div>
                        <h2 className={`text-3xl font-bold text-gray-900 mb-2 ${isRTL ? 'font-arabic' : ''}`}>
                            {systemsT('title')}
                        </h2>
                        <p className={`text-gray-600 text-lg ${isRTL ? 'font-arabic' : ''}`}>
                            Real-time insights and metrics from your systems inventory
                        </p>
                    </div>
                </div>
            </motion.div>

            {/* KPIs Section */}
            <motion.section variants={sectionVariants}>
                <motion.div 
                    variants={titleVariants}
                    className={`mb-8 ${isRTL ? 'text-right' : 'text-left'}`}
                >
                    <h3 className={`text-2xl font-bold text-gray-800 mb-2 ${isRTL ? 'font-arabic' : ''}`}>
                        Key Performance Indicators
                    </h3>
                    <p className={`text-gray-600 ${isRTL ? 'font-arabic' : ''}`}>
                        Overview of your systems health and distribution metrics
                    </p>
                </motion.div>
                
                <SystemKPIs 
                    systems={systems} 
                    isRTL={isRTL} 
                    loading={loading} 
                />
            </motion.section>

            {/* Charts Section */}
            <motion.section variants={sectionVariants}>
                <motion.div 
                    variants={titleVariants}
                    className={`mb-8 ${isRTL ? 'text-right' : 'text-left'}`}
                >
                    <h3 className={`text-2xl font-bold text-gray-800 mb-2 ${isRTL ? 'font-arabic' : ''}`}>
                        Distribution Analytics
                    </h3>
                    <p className={`text-gray-600 ${isRTL ? 'font-arabic' : ''}`}>
                        Visual breakdown of systems by status, domain, and department
                    </p>
                </motion.div>
                
                <SystemCharts 
                    systems={systems} 
                    isRTL={isRTL} 
                    loading={loading} 
                />
            </motion.section>

            {/* Additional Insights Section (if we have data) */}
            {systems.length > 0 && (
                <motion.section variants={sectionVariants}>
                    <div className="bg-gradient-to-r from-slate-50 via-blue-50 to-indigo-50 rounded-2xl p-8 border border-blue-100">
                        <motion.div 
                            variants={titleVariants}
                            className={`${isRTL ? 'text-right' : 'text-left'}`}
                        >
                            <h3 className={`text-xl font-bold text-gray-800 mb-4 ${isRTL ? 'font-arabic' : ''}`}>
                                System Insights
                            </h3>
                            
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                {/* Health Status Insight */}
                                <div className="bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-white/50">
                                    <div className={`${isRTL ? 'text-right' : 'text-left'}`}>
                                        <h4 className={`font-semibold text-gray-800 mb-2 ${isRTL ? 'font-arabic' : ''}`}>
                                            System Health
                                        </h4>
                                        <p className={`text-sm text-gray-600 leading-relaxed ${isRTL ? 'font-arabic' : ''}`}>
                                            {systems.filter(s => s.status === 'Active').length} out of {systems.length} systems 
                                            are currently active and operational, representing{' '}
                                            {Math.round((systems.filter(s => s.status === 'Active').length / systems.length) * 100)}% 
                                            system availability.
                                        </p>
                                    </div>
                                </div>

                                {/* Domain Coverage Insight */}
                                <div className="bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-white/50">
                                    <div className={`${isRTL ? 'text-right' : 'text-left'}`}>
                                        <h4 className={`font-semibold text-gray-800 mb-2 ${isRTL ? 'font-arabic' : ''}`}>
                                            Domain Coverage
                                        </h4>
                                        <p className={`text-sm text-gray-600 leading-relaxed ${isRTL ? 'font-arabic' : ''}`}>
                                            Your systems span across{' '}
                                            {new Set(systems.map(s => s.systemDomain)).size} unique business domains, 
                                            providing comprehensive coverage of organizational functions.
                                        </p>
                                    </div>
                                </div>

                                {/* Department Distribution Insight */}
                                <div className="bg-white/60 backdrop-blur-sm rounded-xl p-6 border border-white/50">
                                    <div className={`${isRTL ? 'text-right' : 'text-left'}`}>
                                        <h4 className={`font-semibold text-gray-800 mb-2 ${isRTL ? 'font-arabic' : ''}`}>
                                            Ownership Distribution
                                        </h4>
                                        <p className={`text-sm text-gray-600 leading-relaxed ${isRTL ? 'font-arabic' : ''}`}>
                                            System ownership is distributed across{' '}
                                            {new Set(systems.map(s => s.ownerDepartment)).size} departments, 
                                            ensuring balanced responsibility and management.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </motion.div>
                    </div>
                </motion.section>
            )}
        </motion.div>
    );
} 
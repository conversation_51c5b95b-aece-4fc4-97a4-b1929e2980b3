"use client";

import { motion } from "framer-motion";
import { useTranslations } from "next-intl";
import { 
    <PERSON><PERSON><PERSON>, 
    <PERSON>, 
    Cell, 
    <PERSON><PERSON>hart, 
    Bar, 
    XAxis, 
    <PERSON>A<PERSON><PERSON>, 
    CartesianGrid, 
    Responsive<PERSON><PERSON><PERSON>,
    <PERSON>,
    Tooltip
} from "recharts";
import { SystemAsset } from "@/lib/services/dataAssetsService";

interface SystemChartsProps {
    systems: SystemAsset[];
    isRTL?: boolean;
    loading?: boolean;
}

const cardVariants = {
    hidden: { 
        opacity: 0, 
        y: 30,
        scale: 0.95
    },
    visible: {
        opacity: 1,
        y: 0,
        scale: 1,
        transition: {
            type: "spring",
            stiffness: 300,
            damping: 30,
            duration: 0.6
        }
    }
};

const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
        opacity: 1,
        transition: {
            staggerChildren: 0.2,
            delayChildren: 0.3
        }
    }
};

// Color palette for charts
const COLORS = {
    status: {
        'Active': '#10B981',
        'Inactive': '#6B7280', 
        'Retired': '#EF4444',
        'Under Review': '#F59E0B'
    },
    domains: [
        '#003874', '#2D8DC6', '#48D3A5', '#8B5CF6', 
        '#06B6D4', '#F59E0B', '#EF4444', '#10B981'
    ],
    departments: [
        '#8B5CF6', '#06B6D4', '#F59E0B', '#EF4444', 
        '#10B981', '#003874', '#2D8DC6', '#48D3A5'
    ]
};

export function SystemCharts({ systems, isRTL = false, loading = false }: SystemChartsProps) {
    const systemsT = useTranslations('Systems.analytics');

    // Process data for charts
    const processChartData = () => {
        // Status distribution
        const statusCounts = systems.reduce((acc, system) => {
            acc[system.status] = (acc[system.status] || 0) + 1;
            return acc;
        }, {} as Record<string, number>);

        const statusData = Object.entries(statusCounts).map(([status, count]) => ({
            name: systemsT(`statusLabels.${status}`),
            value: count,
            color: COLORS.status[status as keyof typeof COLORS.status] || '#6B7280'
        }));

        // Domain distribution
        const domainCounts = systems.reduce((acc, system) => {
            const domain = system.systemDomain || 'Unknown';
            acc[domain] = (acc[domain] || 0) + 1;
            return acc;
        }, {} as Record<string, number>);

        const domainData = Object.entries(domainCounts)
            .map(([domain, count], index) => ({
                name: domain,
                value: count,
                color: COLORS.domains[index % COLORS.domains.length]
            }))
            .sort((a, b) => b.value - a.value);

        // Department distribution 
        const departmentCounts = systems.reduce((acc, system) => {
            const dept = system.ownerDepartment || 'Unknown';
            acc[dept] = (acc[dept] || 0) + 1;
            return acc;
        }, {} as Record<string, number>);

        const departmentData = Object.entries(departmentCounts)
            .map(([department, count], index) => ({
                name: department,
                value: count,
                color: COLORS.departments[index % COLORS.departments.length]
            }))
            .sort((a, b) => b.value - a.value);

        return {
            statusData,
            domainData,
            departmentData
        };
    };

    const { statusData, domainData, departmentData } = processChartData();

    // Custom tooltip component
    const CustomTooltip = ({ active, payload, label }: any) => {
        if (active && payload && payload.length) {
            return (
                <div className="bg-white p-3 rounded-lg shadow-lg border border-gray-200">
                    <p className="font-semibold text-gray-800">{`${payload[0].payload.name}: ${payload[0].value}`}</p>
                    <p className="text-sm text-gray-600">
                        {((payload[0].value / systems.length) * 100).toFixed(1)}% of total
                    </p>
                </div>
            );
        }
        return null;
    };

    // Custom label for pie charts
    const renderCustomLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent }: any) => {
        if (percent < 0.05) return null; // Don't show labels for slices < 5%
        
        const RADIAN = Math.PI / 180;
        const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
        const x = cx + radius * Math.cos(-midAngle * RADIAN);
        const y = cy + radius * Math.sin(-midAngle * RADIAN);

        return (
            <text 
                x={x} 
                y={y} 
                fill="white" 
                textAnchor={x > cx ? 'start' : 'end'} 
                dominantBaseline="central"
                fontSize={12}
                fontWeight="bold"
            >
                {`${(percent * 100).toFixed(0)}%`}
            </text>
        );
    };

    if (loading) {
        return (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {[...Array(3)].map((_, index) => (
                    <div
                        key={index}
                        className="h-80 bg-gradient-to-br from-slate-200 to-slate-300 rounded-2xl animate-pulse"
                    />
                ))}
            </div>
        );
    }

    if (systems.length === 0) {
        return (
            <div className="text-center py-12">
                <div className="text-gray-500 text-lg">{systemsT('noChartData')}</div>
                <p className="text-gray-400 text-sm mt-2">Add some systems to see charts and analytics</p>
            </div>
        );
    }

    return (
        <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="space-y-8"
        >
            {/* Status Distribution Chart */}
            <motion.div variants={cardVariants}>
                <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
                    <div className="bg-gradient-to-r from-[#003874] to-[#2D8DC6] p-6">
                        <h3 className={`text-xl font-bold text-white ${isRTL ? 'text-right font-arabic' : 'text-left'}`}>
                            {systemsT('charts.statusDistribution')}
                        </h3>
                        <p className={`text-white/80 text-sm mt-1 ${isRTL ? 'text-right font-arabic' : 'text-left'}`}>
                            Distribution of systems by operational status
                        </p>
                    </div>
                    <div className="p-6">
                        <ResponsiveContainer width="100%" height={300}>
                            <PieChart>
                                <Pie
                                    data={statusData}
                                    cx="50%"
                                    cy="50%"
                                    labelLine={false}
                                    label={renderCustomLabel}
                                    outerRadius={100}
                                    fill="#8884d8"
                                    dataKey="value"
                                    animationBegin={0}
                                    animationDuration={1000}
                                >
                                    {statusData.map((entry, index) => (
                                        <Cell key={`cell-${index}`} fill={entry.color} />
                                    ))}
                                </Pie>
                                <Tooltip content={<CustomTooltip />} />
                                <Legend 
                                    wrapperStyle={{ 
                                        paddingTop: '20px',
                                        fontSize: '14px',
                                        fontWeight: '500'
                                    }}
                                />
                            </PieChart>
                        </ResponsiveContainer>
                    </div>
                </div>
            </motion.div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Domain Distribution Chart */}
                <motion.div variants={cardVariants}>
                    <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden h-full">
                        <div className="bg-gradient-to-r from-[#8B5CF6] to-[#A78BFA] p-6">
                            <h3 className={`text-xl font-bold text-white ${isRTL ? 'text-right font-arabic' : 'text-left'}`}>
                                {systemsT('charts.domainDistribution')}
                            </h3>
                            <p className={`text-white/80 text-sm mt-1 ${isRTL ? 'text-right font-arabic' : 'text-left'}`}>
                                Systems grouped by business domain
                            </p>
                        </div>
                        <div className="p-6">
                            <ResponsiveContainer width="100%" height={280}>
                                <BarChart
                                    data={domainData}
                                    margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
                                >
                                    <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                                    <XAxis 
                                        dataKey="name" 
                                        tick={{ fontSize: 12, fill: '#6B7280' }}
                                        angle={-45}
                                        textAnchor="end"
                                        height={80}
                                    />
                                    <YAxis 
                                        tick={{ fontSize: 12, fill: '#6B7280' }}
                                        axisLine={false}
                                        tickLine={false}
                                    />
                                    <Tooltip content={<CustomTooltip />} />
                                    <Bar 
                                        dataKey="value" 
                                        radius={[4, 4, 0, 0]}
                                        animationDuration={1200}
                                    >
                                        {domainData.map((entry, index) => (
                                            <Cell key={`domain-cell-${index}`} fill={entry.color} />
                                        ))}
                                    </Bar>
                                </BarChart>
                            </ResponsiveContainer>
                        </div>
                    </div>
                </motion.div>

                {/* Department Distribution Chart */}
                <motion.div variants={cardVariants}>
                    <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden h-full">
                        <div className="bg-gradient-to-r from-[#06B6D4] to-[#67E8F9] p-6">
                            <h3 className={`text-xl font-bold text-white ${isRTL ? 'text-right font-arabic' : 'text-left'}`}>
                                {systemsT('charts.departmentDistribution')}
                            </h3>
                            <p className={`text-white/80 text-sm mt-1 ${isRTL ? 'text-right font-arabic' : 'text-left'}`}>
                                Systems ownership by department
                            </p>
                        </div>
                        <div className="p-6">
                            <ResponsiveContainer width="100%" height={280}>
                                <BarChart
                                    data={departmentData}
                                    margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
                                >
                                    <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                                    <XAxis 
                                        dataKey="name" 
                                        tick={{ fontSize: 12, fill: '#6B7280' }}
                                        angle={-45}
                                        textAnchor="end"
                                        height={80}
                                    />
                                    <YAxis 
                                        tick={{ fontSize: 12, fill: '#6B7280' }}
                                        axisLine={false}
                                        tickLine={false}
                                    />
                                    <Tooltip content={<CustomTooltip />} />
                                    <Bar 
                                        dataKey="value" 
                                        radius={[4, 4, 0, 0]}
                                        animationDuration={1400}
                                    >
                                        {departmentData.map((entry, index) => (
                                            <Cell key={`dept-cell-${index}`} fill={entry.color} />
                                        ))}
                                    </Bar>
                                </BarChart>
                            </ResponsiveContainer>
                        </div>
                    </div>
                </motion.div>
            </div>
        </motion.div>
    );
} 
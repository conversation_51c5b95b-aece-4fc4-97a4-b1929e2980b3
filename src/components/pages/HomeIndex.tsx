"use client";
import { useEffect, useState } from "react";
import { useTranslations } from "next-intl";

export default function HomeIndex() {
  const _t = useTranslations("Index");
  const [_isRTL, setIsRTL] = useState(false);

  useEffect(() => {
    setIsRTL(document.documentElement.dir === "rtl");
  }, []);

  return (
    <div className="flex flex-col min-h-screen w-full">


      <h1>Hello</h1>

    </div>
  );
}

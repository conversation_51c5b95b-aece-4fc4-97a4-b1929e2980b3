import { NextRequest, NextResponse } from "next/server";
import createIntlMiddleware from "next-intl/middleware";
import { routing } from "./i18n/routing";

// Create the internationalization middleware
const intlMiddleware = createIntlMiddleware(routing);

export default async function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname;
  
  // Handle root path redirect to login
  if (pathname === '/') {
    // Default redirect to /login preserving the locale if present
    const locale = request.headers.get('accept-language')?.split(',')[0]?.split('-')[0] || 'en';
    const supportedLocales = ['en', 'ar'];
    const targetLocale = supportedLocales.includes(locale) ? locale : 'en';
    
    return NextResponse.redirect(new URL(`/${targetLocale}/login`, request.url));
  }
  
  // Check for duplicate locale patterns like /en/en/ or /ar/en/
  const localePattern = /^\/(en|ar)\/(en|ar)\//;
  if (localePattern.test(pathname)) {
    // Extract the segments
    const segments = pathname.split('/').filter(Boolean);
    // Use the second locale as the correct one
    const correctLocale = segments[1];
    // Remove the duplicate locale
    const correctedPath = `/${correctLocale}/${segments.slice(2).join('/')}`;
    return NextResponse.redirect(new URL(correctedPath, request.url));
  }
  
  // Apply internationalization middleware for all other routes
  return intlMiddleware(request);
}

export const config = {
  // Match all pathnames except for
  // - … if they start with `/api`, `/trpc`, `/_next` or `/_vercel`
  // - … the ones containing a dot (e.g. `favicon.ico`)
  matcher: ["/((?!api|trpc|_next|_vercel|.*\\..*).*)", "/"],
};

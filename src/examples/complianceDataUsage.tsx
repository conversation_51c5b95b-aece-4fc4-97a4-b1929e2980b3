/**
 * Example usage of the Comprehensive Compliance Data Service
 * This shows how to fetch all compliance data for AI context
 */

import React from 'react';
import { useComplianceData } from '@/hooks/useComplianceData';
import ComplianceDataService from '@/lib/services/complianceDataService';

// Example 1: Using the React Hook
export const ComplianceDataExample: React.FC<{
  projectId: string;
  assessmentId: string;
}> = ({ projectId, assessmentId }) => {
  const { data, loading, error, formatForAI, refetch } = useComplianceData(
    projectId,
    assessmentId,
    'en'
  );

  if (loading) return <div>Loading comprehensive compliance data...</div>;
  if (error) return <div>Error: {error}</div>;
  if (!data) return <div>No data available</div>;

  // Get AI-formatted context
  const aiContext = formatForAI();

  return (
    <div className="space-y-6">
      <h2>Compliance Assessment Overview</h2>
      
      {/* Assessment Summary */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h3>Assessment: {data.assessment.name.en}</h3>
        <p>Framework: {data.framework.name.en}</p>
        <p>Overall Score: {data.assessment.overallScore}%</p>
        <p>Completion: {data.assessment.completionPercentage}%</p>
      </div>

      {/* Framework Structure */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h3>Framework Structure</h3>
        <p>Domains: {data.statistics.framework.totalDomains}</p>
        <p>Controls: {data.statistics.framework.totalControls}</p>
        <p>Specifications: {data.statistics.framework.totalSpecifications}</p>
      </div>

      {/* Domain Performance */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h3>Domain Performance</h3>
        {data.framework.domains.map(domain => {
          const domainRatings = data.ratings.byDomain[domain.id];
          const domainStats = data.statistics.domains[domain.id];
          
          return (
            <div key={domain.id} className="mb-4 p-4 border rounded">
              <h4>{domain.name.en}</h4>
              <p>Average Score: {domainStats?.averageScore?.toFixed(1) || 0}%</p>
              <p>Completion: {domainStats?.completionRate?.toFixed(1) || 0}%</p>
              <p>Weight: {domainStats?.weight || 0}%</p>
              <p>Specifications: {domainRatings?.ratedSpecifications || 0}/{domainRatings?.totalSpecifications || 0}</p>
            </div>
          );
        })}
      </div>

      {/* AI Context Preview */}
      <div className="bg-gray-50 p-6 rounded-lg">
        <h3>AI Context (Preview)</h3>
        <pre className="text-sm overflow-auto max-h-96">
          {aiContext.substring(0, 1000)}...
        </pre>
      </div>

      <button 
        onClick={refetch}
        className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
      >
        Refresh Data
      </button>
    </div>
  );
};

// Example 2: Direct Service Usage (for server-side or utility functions)
export const getComplianceDataForAI = async (
  projectId: string,
  assessmentId: string
): Promise<string> => {
  try {
    // Fetch all compliance data
    const complianceData = await ComplianceDataService.getAllComplianceData(
      projectId,
      assessmentId,
      'en'
    );

    // Format for AI consumption
    const aiContext = ComplianceDataService.formatForAI(complianceData);
    
    return aiContext;
  } catch (error) {
    console.error('Failed to get compliance data for AI:', error);
    throw error;
  }
};

// Example 3: Specific Data Extraction
export const extractSpecificInsights = async (
  projectId: string,
  assessmentId: string
) => {
  const data = await ComplianceDataService.getAllComplianceData(
    projectId,
    assessmentId
  );

  return {
    // High-level metrics
    overallCompliance: data.assessment.overallScore,
    completionRate: data.assessment.completionPercentage,
    
    // Risk areas (low-scoring domains)
    riskAreas: data.framework.domains
      .filter(domain => {
        const domainStats = data.statistics.domains[domain.id];
        return domainStats && domainStats.averageScore < 50;
      })
      .map(domain => ({
        name: domain.name.en,
        score: data.statistics.domains[domain.id]?.averageScore || 0,
        weight: data.statistics.domains[domain.id]?.weight || 0
      })),
    
    // Top performing domains
    strongAreas: data.framework.domains
      .filter(domain => {
        const domainStats = data.statistics.domains[domain.id];
        return domainStats && domainStats.averageScore >= 80;
      })
      .map(domain => ({
        name: domain.name.en,
        score: data.statistics.domains[domain.id]?.averageScore || 0,
        weight: data.statistics.domains[domain.id]?.weight || 0
      })),
    
    // Unrated specifications
    pendingRatings: data.framework.domains.flatMap(domain =>
      domain.specifications
        .filter(spec => !data.ratings.individual[spec.id])
        .map(spec => ({
          id: spec.id,
          name: spec.name.en,
          domain: domain.name.en,
          control: spec.control?.name.en || 'Unknown'
        }))
    ),
    
    // Recent activity
    recentRatings: Object.values(data.ratings.individual)
      .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
      .slice(0, 10)
      .map(rating => ({
        specificationId: rating.specificationId,
        status: rating.complianceStatus,
        score: rating.currentRating,
        updatedAt: rating.updatedAt,
        comments: rating.comments
      }))
  };
};

// Example 4: Generate Assessment Report Data
export const generateAssessmentReport = async (
  projectId: string,
  assessmentId: string
) => {
  const data = await ComplianceDataService.getAllComplianceData(
    projectId,
    assessmentId
  );

  return {
    metadata: {
      assessmentName: data.assessment.name.en,
      frameworkName: data.framework.name.en,
      generatedAt: new Date().toISOString(),
      assessor: data.assessment.assessor.name,
      period: {
        start: data.assessment.startDate,
        target: data.assessment.targetCompletionDate
      }
    },
    
    executiveSummary: {
      overallScore: data.assessment.overallScore,
      completionRate: data.assessment.completionPercentage,
      totalSpecifications: data.statistics.framework.totalSpecifications,
      ratedSpecifications: data.statistics.ratings.totalRated,
      complianceRate: data.statistics.compliance.complianceRate
    },
    
    domainBreakdown: data.framework.domains.map(domain => {
      const domainStats = data.statistics.domains[domain.id];
      const domainRatings = data.ratings.byDomain[domain.id];
      
      return {
        name: domain.name.en,
        description: domain.description?.en || '',
        score: domainStats?.averageScore || 0,
        weight: domainStats?.weight || 0,
        completion: domainStats?.completionRate || 0,
        totalSpecifications: domainRatings?.totalSpecifications || 0,
        ratedSpecifications: domainRatings?.ratedSpecifications || 0,
        controls: domain.controls.length
      };
    }),
    
    complianceDistribution: data.statistics.compliance,
    
    recommendations: {
      priorityAreas: data.framework.domains
        .filter(domain => {
          const stats = data.statistics.domains[domain.id];
          return stats && stats.averageScore < 60 && stats.weight > 10;
        })
        .map(domain => domain.name.en),
      
      quickWins: data.framework.domains
        .filter(domain => {
          const stats = data.statistics.domains[domain.id];
          return stats && stats.completionRate < 50;
        })
        .map(domain => domain.name.en)
    }
  };
};

export default {
  ComplianceDataExample,
  getComplianceDataForAI,
  extractSpecificInsights,
  generateAssessmentReport
}; 
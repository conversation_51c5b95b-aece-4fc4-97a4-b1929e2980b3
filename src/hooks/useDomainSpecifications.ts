import { useState, useEffect, useCallback } from 'react';
import { 
  doc, 
  getDoc, 
  collection,
  getDocs
} from 'firebase/firestore';
import { db } from '@/lib/firebaseClient';

interface Domain {
  id: string;
  name: {
    en: string;
    ar: string;
  };
  score: number;
  maxScore: number;
  description?: {
    en: string;
    ar: string;
  };
  specifications: Specification[];
}

interface Control {
  id: string;
  name?: {
    en: string;
    ar: string;
  };
  description?: {
    en: string;
    ar: string;
  };
}

interface Specification {
  id: string;
  name?: {
    en: string;
    ar: string;
  };
  description?: {
    en: string;
    ar: string;
  };
  domainId?: string;
  controlId?: string;
  control?: Control;
  maturityLevel?: number;
  percentageValue?: number;
  complianceStatus?: string;
  currentRating?: number;
  dataRating?: string | null;
  swotCompleted?: boolean;
  comments?: string;
  assessmentDate?: Date | string | null;
  [key: string]: unknown;
}

interface UseDomainSpecificationsReturn {
  domain: Domain | null;
  specifications: Specification[];
  controls: Control[];
  loading: boolean;
  error: string | null;
}

export const useDomainSpecifications = (
  projectId: string,
  assessmentId: string,
  domainName: string
): UseDomainSpecificationsReturn => {
  const [domain, setDomain] = useState<Domain | null>(null);
  const [specifications, setSpecifications] = useState<Specification[]>([]);
  const [controls, setControls] = useState<Control[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchDomainSpecifications = useCallback(async () => {
    if (!projectId || !assessmentId || !domainName) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // First, get the assessment to find the framework
      const assessmentRef = doc(db, `projects/${projectId}/ComplianceAssessment/${assessmentId}`);
      const assessmentSnap = await getDoc(assessmentRef);

      if (!assessmentSnap.exists()) {
        setError('Assessment not found');
        return;
      }

      const assessmentData = assessmentSnap.data();
      const frameworkId = assessmentData.frameworkId;

      if (!frameworkId) {
        setError('Framework not found in assessment');
        return;
      }

      // Try to find domain by name in framework's domains
      let foundDomain: Domain | null = null;
      let domainId: string | null = null;

      // Check framework subcollection first
      const domainsCollectionRef = collection(db, `frameworks/${frameworkId}/domains`);
      const domainsSnapshot = await getDocs(domainsCollectionRef);

      if (!domainsSnapshot.empty) {
        // Search through domains to find matching name
        domainsSnapshot.forEach((domainDoc) => {
          const domainData = domainDoc.data();
          const domainNameEn = domainData.name?.en || domainDoc.id;
          const domainNameAr = domainData.name?.ar || domainDoc.id;
          
          if (domainNameEn === domainName || domainNameAr === domainName || domainDoc.id === domainName) {
            foundDomain = {
              id: domainDoc.id,
              name: domainData.name || { en: domainDoc.id, ar: domainDoc.id },
              score: domainData.score || 0,
              maxScore: domainData.maxScore || 100,
              description: domainData.description,
              specifications: []
            };
            domainId = domainDoc.id;
          }
        });

        if (foundDomain && domainId) {
          // Fetch controls for this domain, then specifications from each control
          const controlsRef = collection(db, `frameworks/${frameworkId}/domains/${domainId}/controls`);
          const controlsSnapshot = await getDocs(controlsRef);
          
          const fetchedSpecifications: Specification[] = [];
          const fetchedControls: Control[] = [];
          
          // For each control, fetch its specifications
          for (const controlDoc of controlsSnapshot.docs) {
            const controlData = controlDoc.data();
            const control: Control = {
              id: controlDoc.id,
              name: controlData.name || { en: controlDoc.id, ar: controlDoc.id },
              description: controlData.description
            };
            fetchedControls.push(control);
            
            const specsRef = collection(db, `frameworks/${frameworkId}/domains/${domainId}/controls/${controlDoc.id}/specifications`);
            const specsSnapshot = await getDocs(specsRef);
            
            specsSnapshot.forEach((specDoc) => {
              fetchedSpecifications.push({
                id: specDoc.id,
                ...specDoc.data(),
                domainId: domainId || undefined,
                controlId: controlDoc.id,
                control: control
              });
            });
          }
          
          setControls(fetchedControls);

          // Fetch ratings for these specifications if project exists
          if (projectId && assessmentId) {
            // Try new structure first: projects/{projectId}/ComplianceAssessment/{assessmentId}/ratings/{specificationId}
            const ratingsCollection = collection(db, `projects/${projectId}/ComplianceAssessment/${assessmentId}/ratings`);
            const ratingsSnapshot = await getDocs(ratingsCollection);
            const ratingsMap = new Map();

            ratingsSnapshot.forEach(ratingDoc => {
              ratingsMap.set(ratingDoc.id, ratingDoc.data());
            });

            // If no ratings found, fallback to old structure
            if (ratingsMap.size === 0) {
              const oldRatingsCollection = collection(db, `projects/${projectId}/ratings`);
              const oldRatingsSnapshot = await getDocs(oldRatingsCollection);

              oldRatingsSnapshot.forEach(ratingDoc => {
                const ratingId = ratingDoc.id;
                if (ratingId.startsWith('specifications_')) {
                  const specId = ratingId.replace('specifications_', '');
                  ratingsMap.set(specId, ratingDoc.data());
                }
              });
            }
            
            // Apply ratings to specifications
            const enhancedSpecifications = fetchedSpecifications.map(spec => {
              const rating = ratingsMap.get(spec.id);
              if (rating) {
                return {
                  ...spec,
                  ...(rating.ratingType === 'compliance' && {
                    complianceStatus: rating.complianceStatus || 'notCompleted'
                  }),
                  ...(rating.ratingType === 'percentage' && {
                    percentageValue: rating.percentageValue || 0
                  }),
                  currentRating: rating.currentRating || 0,
                  dataRating: rating.dataRating || spec.dataRating,
                  swotCompleted: rating.swotCompleted || spec.swotCompleted,
                  comments: rating.comments || spec.comments,
                  assessmentDate: rating.updatedAt || spec.assessmentDate
                };
              }
              return {
                ...spec,
                complianceStatus: 'notCompleted',
                currentRating: 0
              };
            });
            
            setSpecifications(enhancedSpecifications);
          } else {
            setSpecifications(fetchedSpecifications);
          }

          setDomain(foundDomain);
        }
      } else {
        // Try NPC specific path if framework is npc
        if (frameworkId === 'npc') {
          const npcDomainsRef = collection(db, `npc/document/domains`);
          const npcDomainsSnapshot = await getDocs(npcDomainsRef);

          npcDomainsSnapshot.forEach((domainDoc) => {
            const domainData = domainDoc.data();
            const domainNameEn = domainData.name?.en || domainDoc.id;
            const domainNameAr = domainData.name?.ar || domainDoc.id;
            
            if (domainNameEn === domainName || domainNameAr === domainName || domainDoc.id === domainName) {
              foundDomain = {
                id: domainDoc.id,
                name: domainData.name || { en: domainDoc.id, ar: domainDoc.id },
                score: domainData.score || 0,
                maxScore: domainData.maxScore || 100,
                description: domainData.description,
                specifications: []
              };
              domainId = domainDoc.id;
            }
          });

          if (foundDomain && domainId) {
            // Fetch specifications for NPC domain
            const specificationsRef = collection(db, `npc/document/domains/${domainId}/specifications`);
            const specificationsSnapshot = await getDocs(specificationsRef);
            
                      const fetchedSpecifications: Specification[] = [];
          specificationsSnapshot.forEach((specDoc) => {
            fetchedSpecifications.push({
              id: specDoc.id,
              ...specDoc.data(),
              domainId: domainId || undefined
            });
          });

            setSpecifications(fetchedSpecifications);
            setDomain(foundDomain);
          }
        }
      }

      if (!foundDomain) {
        setError('Domain not found');
      }

    } catch (err) {
      console.error('Error fetching domain specifications:', err);
      setError('Failed to fetch domain specifications');
    } finally {
      setLoading(false);
    }
  }, [projectId, assessmentId, domainName]);

  useEffect(() => {
    fetchDomainSpecifications();
  }, [fetchDomainSpecifications]);

  return {
    domain,
    specifications,
    controls,
    loading,
    error
  };
}; 
'use client';

import { useAuthContext } from '@/context/AuthContext';
import { useLocale } from 'next-intl';
import { useRouter } from 'next/navigation';

/**
 * Hook to access the authenticated user's session data
 * @returns The authenticated user's session data and auth-related utilities
 */
export function useAuthSession() {
  const { 
    user, 
    isLoading, 
    isInitialized,
    error,
    logout
  } = useAuthContext();
  
  const _router = useRouter();
  const _locale = useLocale();
  
  // Removing the automatic locale update logic to prevent redirection issues
  
  return {
    session: user,
    isLoading,
    isInitialized,
    isAuthenticated: !!user,
    error,
    logout,
    // Return user session data in a structured format for easy access
    sessionData: user ? {
      uid: user.uid,
      email: user.email,
      role: user.role,
      assignedProjectIds: user.assignedProjectIds,
      organizationId: user.organizationId,
      locale: user.locale,
      name: user.name
    } : null
  };
} 
import { useEffect, useState, useCallback } from 'react';
import { collection, getDocs, doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebaseClient';

interface UseDomainRatingsProps {
  projectId?: string;
  domainId?: string;
}

interface DomainRating {
  specId: string;
  value: number; // percentage value 0-100
}

export const useDomainRatings = ({ projectId, domainId }: UseDomainRatingsProps) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [specRatings, setSpecRatings] = useState<DomainRating[]>([]);
  const [avgRating, setAvgRating] = useState<number | null>(null);

  // Helper function to collect all specification IDs for a domain
  const collectDomainSpecificationIds = useCallback(async (frameworkId: string, domainId: string): Promise<string[]> => {
    const specIds: string[] = [];
    
    try {
      // First try to fetch specifications directly under the domain
      const domainSpecsRef = collection(db, `frameworks/${frameworkId}/domains/${domainId}/specifications`);
      const domainSpecsSnapshot = await getDocs(domainSpecsRef);
      
      if (!domainSpecsSnapshot.empty) {
        domainSpecsSnapshot.forEach(doc => {
          specIds.push(doc.id);
        });
      }
      
      // Then fetch controls and their specifications
      const controlsRef = collection(db, `frameworks/${frameworkId}/domains/${domainId}/controls`);
      const controlsSnapshot = await getDocs(controlsRef);
      
      // For each control, get its specifications
      const controlPromises = controlsSnapshot.docs.map(async controlDoc => {
        const controlId = controlDoc.id;
        const specsRef = collection(db, `frameworks/${frameworkId}/domains/${domainId}/controls/${controlId}/specifications`);
        const specsSnapshot = await getDocs(specsRef);
        
        specsSnapshot.forEach(specDoc => {
          specIds.push(specDoc.id);
        });
      });
      
      await Promise.all(controlPromises);
      
      return specIds;
    } catch (error) {
      console.error('Error collecting domain specification IDs:', error);
      return [];
    }
  }, []);

  useEffect(() => {
    const fetchRatings = async () => {
      if (!projectId || !domainId) {
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        console.log(`Fetching ratings for domain ${domainId} in project ${projectId}`);
        
        // First, get the project to determine its framework
        const projectRef = doc(db, `projects/${projectId}`);
        const projectSnapshot = await getDoc(projectRef);
        
        if (!projectSnapshot.exists()) {
          console.error('Project not found');
          setIsLoading(false);
          return;
        }
        
        const frameworkId = projectSnapshot.data()?.frameworkId;
        
        if (!frameworkId) {
          console.error('Project has no frameworkId');
          setIsLoading(false);
          return;
        }
        
        // Collect all specification IDs that belong to this domain
        const domainSpecIds = await collectDomainSpecificationIds(frameworkId, domainId);
        console.log(`Found ${domainSpecIds.length} specifications for domain ${domainId}`);
        
        // Path: /projects/{projectId}/ratings
        const ratingsCollectionRef = collection(db, 'projects', projectId, 'ratings');
        
        // Get all ratings and filter them client-side
        const ratingsSnapshot = await getDocs(ratingsCollectionRef);
        console.log(`Found ${ratingsSnapshot.size} total ratings in the project`);
        
        const ratings: DomainRating[] = [];
        
        ratingsSnapshot.forEach((doc) => {
          const data = doc.data();
          
          // Check if the rating ID starts with specifications_
          if (doc.id.startsWith('specifications_')) {
            // Extract the specification ID
            const specId = doc.id.replace('specifications_', '');
            
            // Check if this specification belongs to our domain
            if (domainSpecIds.includes(specId)) {
              console.log(`Found rating for specification ${specId} in domain ${domainId}`);
              
              // Extract the rating value based on rating type
              let value = 0;
              
              if (data.percentageValue !== undefined) {
                // Percentage ratings are already 0-100
                value = data.percentageValue;
              } else if (data.currentRating !== undefined) {
                // Maturity ratings are typically 1-5, normalize to percentage
                value = data.currentRating;
              } else if (data.complianceStatus) {
                // Compliance ratings - convert to numeric value based on status
                switch(data.complianceStatus.toLowerCase()) {
                  case 'compliant':
                    value = 100;
                    break;
                  case 'partially compliant':
                    value = 50;
                    break;
                  case 'non-compliant':
                  default:
                    value = 0;
                    break;
                }
              } else if (data.value !== undefined) {
                // Direct value field
                value = data.value;
              }
              
              ratings.push({
                specId: specId,
                value: value
              });
              console.log(`Added rating for spec ${specId} with value: ${value}`);
            }
          }
          // Also check for direct domain ratings (rare but possible)
          else if (doc.id === `domain_${domainId}`) {
            console.log(`Found direct domain rating for domain: ${domainId}`);
            // Extract the rating value
            const value = data.percentageValue ?? data.currentRating ?? data.value ?? 0;
            
            ratings.push({
              specId: domainId,
              value: typeof value === 'number' ? value : 0
            });
          }
        });
        
        setSpecRatings(ratings);
        console.log(`Final filtered ratings count: ${ratings.length}`);
        
        // Calculate average rating
        if (ratings.length > 0) {
          const sum = ratings.reduce((acc, rating) => acc + rating.value, 0);
          const avg = sum / ratings.length;
          console.log(`Calculated average rating: ${avg} from ${ratings.length} ratings`);
          setAvgRating(avg);
        } else {
          console.log('No ratings found for this domain');
          setAvgRating(null);
        }
        
        setError(null);
      } catch (err) {
        console.error('Error fetching domain ratings:', err);
        setError(err instanceof Error ? err : new Error('Error fetching domain ratings'));
      } finally {
        setIsLoading(false);
      }
    };

    fetchRatings();
  }, [projectId, domainId, collectDomainSpecificationIds]);

  return {
    isLoading,
    error,
    specRatings,
    avgRating,
  };
}; 
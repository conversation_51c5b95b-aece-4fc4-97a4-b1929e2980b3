import { useState, useEffect, useCallback } from 'react';
import ComplianceDataService, { ComplianceDataContext } from '@/lib/services/complianceDataService';

interface UseComplianceDataReturn {
  data: ComplianceDataContext | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  formatForAI: () => string;
}

/**
 * React hook for fetching comprehensive compliance assessment data
 * Perfect for AI context and comprehensive analysis
 */
export const useComplianceData = (
  projectId: string,
  assessmentId: string,
  locale: string = 'en',
  autoFetch: boolean = true
): UseComplianceDataReturn => {
  const [data, setData] = useState<ComplianceDataContext | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchData = useCallback(async () => {
    if (!projectId || !assessmentId) {
      setError('Project ID and Assessment ID are required');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      console.log(`Fetching compliance data for project: ${projectId}, assessment: ${assessmentId}`);
      
      const result = await ComplianceDataService.getAllComplianceData(
        projectId,
        assessmentId,
        locale
      );
      
      setData(result);
      console.log('Compliance data fetched successfully');
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch compliance data';
      console.error('Error fetching compliance data:', errorMessage);
      setError(errorMessage);
      setData(null);
    } finally {
      setLoading(false);
    }
  }, [projectId, assessmentId, locale]);

  // Auto-fetch on mount and when dependencies change
  useEffect(() => {
    if (autoFetch && projectId && assessmentId) {
      fetchData();
    }
  }, [autoFetch, fetchData]);

  // Format data for AI context
  const formatForAI = useCallback(() => {
    if (!data) {
      return 'No compliance data available';
    }
    return ComplianceDataService.formatForAI(data);
  }, [data]);

  return {
    data,
    loading,
    error,
    refetch: fetchData,
    formatForAI
  };
};

export default useComplianceData; 
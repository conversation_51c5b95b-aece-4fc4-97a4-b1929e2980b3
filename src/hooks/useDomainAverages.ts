import { useState, useEffect } from 'react';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebaseClient';

export interface DomainAverage {
  average: number;
  name: { en: string; ar: string };
  totalSpecifications: number;
  ratedSpecifications: number;
  domainId: string;
  weight?: number; // Domain weight from assessment criteria
}

interface UseDomainAveragesReturn {
  domainAverages: Record<string, DomainAverage>;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export const useDomainAverages = (
  projectId: string,
  assessmentId: string
): UseDomainAveragesReturn => {
  const [domainAverages, setDomainAverages] = useState<Record<string, DomainAverage>>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchDomainAverages = async () => {
    if (!projectId || !assessmentId) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Fetch assessment document which should contain domainAverages
      const assessmentRef = doc(db, `projects/${projectId}/ComplianceAssessment/${assessmentId}`);
      const assessmentSnap = await getDoc(assessmentRef);

      if (!assessmentSnap.exists()) {
        setError('Assessment not found');
        setDomainAverages({});
        return;
      }

      const assessmentData = assessmentSnap.data();
      
      // Get domain averages from the assessment document
      if (assessmentData.domainAverages) {
        setDomainAverages(assessmentData.domainAverages);
      } else {
        // If no domain averages exist, return empty object
        // This might happen for new assessments that haven't been rated yet
        setDomainAverages({});
      }
    } catch (err) {
      console.error('Error fetching domain averages:', err);
      setError('Failed to fetch domain averages');
      setDomainAverages({});
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDomainAverages();
  }, [projectId, assessmentId]);

  const refetch = async () => {
    await fetchDomainAverages();
  };

  return {
    domainAverages,
    loading,
    error,
    refetch
  };
}; 
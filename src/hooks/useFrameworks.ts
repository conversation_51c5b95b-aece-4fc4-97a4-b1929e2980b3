import { useState, useEffect } from 'react';
import { collection, getDocs } from 'firebase/firestore';
import { db } from '@/lib/firebaseClient';
import { Framework } from '@/types';

export const useFrameworks = () => {
  const [frameworks, setFrameworks] = useState<Framework[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchFrameworks = async () => {
      try {
        setLoading(true);
        const frameworksRef = collection(db, 'frameworks');
        const snapshot = await getDocs(frameworksRef);
        
        const fetchedFrameworks: Framework[] = [];
        snapshot.forEach((doc) => {
          fetchedFrameworks.push({
            id: doc.id,
            ...doc.data()
          } as Framework);
        });
        
        setFrameworks(fetchedFrameworks);
        setError(null);
      } catch (err) {
        console.error('Error fetching frameworks:', err);
        setError('Failed to fetch frameworks');
      } finally {
        setLoading(false);
      }
    };

    fetchFrameworks();
  }, []);

  return { frameworks, loading, error };
}; 
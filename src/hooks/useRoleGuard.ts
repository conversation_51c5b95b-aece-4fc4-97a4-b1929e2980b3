'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useLocale } from 'next-intl';
import { useAuthContext } from '@/context/AuthContext';

type AllowedRole = 'Client' | 'Consultant' | 'Both';

/**
 * Hook to protect routes based on user role
 * @param allowedRole The role that is allowed to access the route
 */
export function useRoleGuard(allowedRole: AllowedRole) {
  const router = useRouter();
  const locale = useLocale();
  const { user, isInitialized, isLoading } = useAuthContext();
  
  useEffect(() => {
    // Only run the check if auth is initialized and not loading
    if (!isInitialized || isLoading) return;
    
    // If no user, redirect to login
    if (!user) {
      router.push(`/${locale}/login`);
      return;
    }
    
    // Check if user has the allowed role
    const hasAllowedRole = 
      allowedRole === 'Both' || 
      user.role === allowedRole;
    
    // Redirect to unauthorized page if role doesn't match
    if (!hasAllowedRole) {
      router.push(`/${locale}/unauthorized`);
    }

    // If we're in a client-only or consultant-only section with the wrong role, redirect to dashboard
    if (allowedRole !== 'Both' && user.role !== allowedRole) {
      router.push(`/${locale}/dashboard`);
    }
  }, [user, isInitialized, isLoading, allowedRole, router, locale]);
  
  return {
    isAuthorized: !!(user && (allowedRole === 'Both' || user.role === allowedRole)),
    isLoading: isLoading || !isInitialized,
    user
  };
} 
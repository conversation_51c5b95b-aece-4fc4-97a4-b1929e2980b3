import { useState, useEffect, useCallback } from 'react';
import { collection, getDocs, query, orderBy, doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebaseClient';

interface Assessment {
  id: string;
  name: {
    en: string;
    ar: string;
  };
  description: string;
  frameworkId: string;
  frameworkName?: {
    en: string;
    ar: string;
  };
  status: string;
  createdAt: Date | string | null;
  updatedAt: Date | string | null;
}

export const useAssessments = (projectId: string | null) => {
  const [assessments, setAssessments] = useState<Assessment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchAssessments = useCallback(async () => {
    if (!projectId) {
      setAssessments([]);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      const assessmentsRef = collection(db, `projects/${projectId}/ComplianceAssessment`);
      const q = query(assessmentsRef, orderBy('createdAt', 'desc'));
      const snapshot = await getDocs(q);
      
      const fetchedAssessments: Assessment[] = [];
      
      // Process each assessment and fetch framework details
      for (const docSnap of snapshot.docs) {
        const assessmentData = {
          id: docSnap.id,
          ...docSnap.data()
        } as Assessment;

        // Fetch framework name if frameworkId exists
        if (assessmentData.frameworkId) {
          try {
            const frameworkRef = doc(db, `frameworks/${assessmentData.frameworkId}`);
            const frameworkSnap = await getDoc(frameworkRef);
            
            if (frameworkSnap.exists()) {
              const frameworkData = frameworkSnap.data();
              assessmentData.frameworkName = frameworkData.name;
            }
          } catch (frameworkError) {
            console.error('Error fetching framework details:', frameworkError);
            // Continue without framework name
          }
        }

        fetchedAssessments.push(assessmentData);
      }
      
      setAssessments(fetchedAssessments);
      setError(null);
    } catch (err) {
      console.error('Error fetching assessments:', err);
      setError('Failed to fetch assessments');
    } finally {
      setLoading(false);
    }
  }, [projectId]);

  useEffect(() => {
    fetchAssessments();
  }, [fetchAssessments]);

  return { 
    assessments, 
    loading, 
    error, 
    refetch: fetchAssessments 
  };
}; 
import { useState, useEffect } from 'react';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebaseClient';
import { AssessmentCriteria } from '@/types';
import { 
  getDomainWeight, 
  getAllDomainWeights, 
  formatDomainWeight,
  getDomainWeightConfig,
  validateDomainWeights
} from '@/lib/ratings/assessmentCriteriaUtils';

interface UseAssessmentCriteriaReturn {
  assessmentCriteria: AssessmentCriteria | null;
  loading: boolean;
  error: string | null;
  // Domain weight utilities
  getDomainWeight: (domainId: string) => number | null;
  getAllDomainWeights: () => Array<{ domainId: string; weight: number }>;
  formatDomainWeight: (weight: number | null, locale?: string, showPercentage?: boolean) => string;
  getDomainWeightConfig: (weight: number | null, locale?: string) => {
    display: string;
    color: string;
    bgColor: string;
    isSet: boolean;
  };
  validateDomainWeights: (allowPartialWeights?: boolean) => {
    isValid: boolean;
    totalWeight: number;
    issues: string[];
  };
  hasDomainWeights: boolean;
  totalDomainWeight: number;
}

export const useAssessmentCriteria = (frameworkId: string): UseAssessmentCriteriaReturn => {
  const [assessmentCriteria, setAssessmentCriteria] = useState<AssessmentCriteria | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchAssessmentCriteria = async () => {
      if (!frameworkId) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // Fetch assessment criteria from the root assessmentCriteria collection
        const criteriaRef = doc(db, `assessmentCriteria/${frameworkId}`);
        const criteriaSnap = await getDoc(criteriaRef);

        if (criteriaSnap.exists()) {
          const criteriaData = {
            id: criteriaSnap.id,
            ...criteriaSnap.data()
          } as AssessmentCriteria;
          
          setAssessmentCriteria(criteriaData);
        } else {
          setError('Assessment criteria not found');
        }
      } catch (err) {
        console.error('Error fetching assessment criteria:', err);
        setError('Failed to fetch assessment criteria');
      } finally {
        setLoading(false);
      }
    };

    fetchAssessmentCriteria();
  }, [frameworkId]);

  // Domain weight utility methods
  const getDomainWeightUtil = (domainId: string) => {
    return getDomainWeight(domainId, assessmentCriteria);
  };

  const getAllDomainWeightsUtil = () => {
    return getAllDomainWeights(assessmentCriteria);
  };

  const formatDomainWeightUtil = (weight: number | null, locale: string = 'en', showPercentage: boolean = true) => {
    return formatDomainWeight(weight, locale, showPercentage);
  };

  const getDomainWeightConfigUtil = (weight: number | null, locale: string = 'en') => {
    return getDomainWeightConfig(weight, locale);
  };

  const validateDomainWeightsUtil = (allowPartialWeights: boolean = true) => {
    const allWeights = getAllDomainWeights(assessmentCriteria);
    return validateDomainWeights(allWeights, allowPartialWeights);
  };

  // Computed properties
  const hasDomainWeights = Boolean(assessmentCriteria?.domainWeights && 
    Array.isArray(assessmentCriteria.domainWeights) && 
    assessmentCriteria.domainWeights.length > 0);

  const totalDomainWeight = hasDomainWeights 
    ? assessmentCriteria?.domainWeights?.reduce((sum, dw) => sum + dw.weight, 0) || 0
    : 0;

  return {
    assessmentCriteria,
    loading,
    error,
    // Domain weight utilities
    getDomainWeight: getDomainWeightUtil,
    getAllDomainWeights: getAllDomainWeightsUtil,
    formatDomainWeight: formatDomainWeightUtil,
    getDomainWeightConfig: getDomainWeightConfigUtil,
    validateDomainWeights: validateDomainWeightsUtil,
    hasDomainWeights,
    totalDomainWeight
  };
}; 
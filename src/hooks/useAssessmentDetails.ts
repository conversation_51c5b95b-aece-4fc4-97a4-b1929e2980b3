import { useState, useEffect, useCallback } from 'react';
import { doc, getDoc, collection, getDocs } from 'firebase/firestore';
import { db } from '@/lib/firebaseClient';
import { Framework, Domain } from '@/types';

interface Assessment {
  id: string;
  name: {
    en: string;
    ar: string;
  };
  description: string;
  frameworkId: string;
  status: string;
  createdAt: Date | string | null;
  updatedAt: Date | string | null;
}

interface UseAssessmentDetailsReturn {
  assessment: Assessment | null;
  framework: Framework | null;
  domains: Domain[];
  loading: boolean;
  error: string | null;
  domainsLoaded: boolean;
  loadDomains: () => Promise<void>;
}

export const useAssessmentDetails = (
  projectId: string,
  assessmentId: string
): UseAssessmentDetailsReturn => {
  const [assessment, setAssessment] = useState<Assessment | null>(null);
  const [framework, setFramework] = useState<Framework | null>(null);
  const [domains, setDomains] = useState<Domain[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [domainsLoaded, setDomainsLoaded] = useState(false);

  // Fetch domains function (optimized for compliance assessment only)
  const fetchDomains = useCallback(async (frameworkId: string) => {
    try {
      // Check if framework uses subcollection for domains
      const domainsCollectionRef = collection(db, `frameworks/${frameworkId}/domains`);
      const domainsSnapshot = await getDocs(domainsCollectionRef);

      if (!domainsSnapshot.empty) {
        // Domains exist as subcollection - fetch only basic domain info for fast loading
        const fetchedDomains: Domain[] = [];
        
        domainsSnapshot.forEach((doc) => {
          const domainData = doc.data();
          fetchedDomains.push({
            id: doc.id,
            name: domainData.name || { en: doc.id, ar: doc.id },
            score: domainData.score || 0,
            maxScore: domainData.maxScore || 100,
            description: domainData.description,
            specifications: [] // Keep empty for lazy loading
          });
        });
        
        // For compliance assessment, we only need domain names initially
        // Specifications will be loaded when domain is expanded
        setDomains(fetchedDomains);
      } else {
        // Try framework-specific paths (like npc)
        if (frameworkId === 'npc') {
          const npcDomainsRef = collection(db, `npc/document/domains`);
          const npcDomainsSnapshot = await getDocs(npcDomainsRef);

          if (!npcDomainsSnapshot.empty) {
            const fetchedDomains: Domain[] = [];
            npcDomainsSnapshot.forEach((doc) => {
              const domainData = doc.data();
              fetchedDomains.push({
                id: doc.id,
                name: domainData.name || { en: doc.id, ar: doc.id },
                score: domainData.score || 0,
                maxScore: domainData.maxScore || 100,
                description: domainData.description,
                specifications: []
              });
            });
            setDomains(fetchedDomains);
          }
        } else {
          // Fallback to generic domains collection
          const genericDomainsRef = collection(db, 'domains');
          const genericDomainsSnapshot = await getDocs(genericDomainsRef);

          const fetchedDomains: Domain[] = [];
          genericDomainsSnapshot.forEach((doc) => {
            const domainData = doc.data();
            fetchedDomains.push({
              id: doc.id,
              name: domainData.name || { en: doc.id, ar: doc.id },
              score: domainData.score || 0,
              maxScore: domainData.maxScore || 100,
              description: domainData.description,
              specifications: []
            });
          });
          setDomains(fetchedDomains);
        }
      }
    } catch (error) {
      console.error('Error fetching domains:', error);
      setDomains([]);
    }
  }, []);

  useEffect(() => {
    const fetchAssessmentDetails = async () => {
      if (!projectId || !assessmentId) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // Fetch assessment data
        const assessmentRef = doc(db, `projects/${projectId}/ComplianceAssessment/${assessmentId}`);
        const assessmentSnap = await getDoc(assessmentRef);

        if (!assessmentSnap.exists()) {
          setError('Assessment not found');
          return;
        }

        const assessmentData = {
          id: assessmentSnap.id,
          ...assessmentSnap.data()
        } as Assessment;

        setAssessment(assessmentData);

        // Fetch framework data if frameworkId exists
        if (assessmentData.frameworkId) {
          const frameworkRef = doc(db, `frameworks/${assessmentData.frameworkId}`);
          const frameworkSnap = await getDoc(frameworkRef);

          if (frameworkSnap.exists()) {
            const frameworkData = {
              id: frameworkSnap.id,
              ...frameworkSnap.data()
            } as Framework;

            setFramework(frameworkData);
            // Don't fetch domains immediately for lazy loading
          }
        }
      } catch (err) {
        console.error('Error fetching assessment details:', err);
        setError('Failed to fetch assessment details');
      } finally {
        setLoading(false);
      }
    };

    fetchAssessmentDetails();
  }, [projectId, assessmentId]);

  // Lazy load domains function
  const loadDomains = useCallback(async () => {
    if (!framework || domainsLoaded) return;
    
    try {
      setDomainsLoaded(true);
      await fetchDomains(framework.id);
    } catch (error) {
      console.error('Error loading domains:', error);
      setError('Failed to load domains');
    }
  }, [framework, domainsLoaded, fetchDomains]);

  return {
    assessment,
    framework,
    domains,
    loading,
    error,
    domainsLoaded,
    loadDomains
  };
}; 
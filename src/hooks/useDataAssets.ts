import { useState, useEffect, useCallback } from 'react';
import { dataAssetsService, AssetData, SystemAsset } from '@/lib/services/dataAssetsService';

interface UseDataAssetsOptions {
    projectId: string;
    assetType: string;
    autoFetch?: boolean;
}

interface UseDataAssetsReturn<T extends AssetData> {
    assets: T[];
    loading: boolean;
    error: string | null;
    createAsset: (assetData: Omit<T, 'id' | 'createdAt' | 'updatedAt'>) => Promise<string>;
    updateAsset: (assetId: string, updates: Partial<Omit<T, 'id' | 'createdAt' | 'updatedAt'>>) => Promise<void>;
    deleteAsset: (assetId: string) => Promise<void>;
    fetchAssets: () => Promise<void>;
    getNextSystemIndex: () => Promise<number>;
}

export function useDataAssets<T extends AssetData>({
    projectId,
    assetType,
    autoFetch = true
}: UseDataAssetsOptions): UseDataAssetsReturn<T> {
    const [assets, setAssets] = useState<T[]>([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const fetchAssets = useCallback(async () => {
        if (!projectId) return;
        
        setLoading(true);
        setError(null);
        
        try {
            const fetchedAssets = await dataAssetsService.getAssets<T>(projectId, assetType);
            setAssets(fetchedAssets);
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to fetch assets');
            console.error('Error fetching assets:', err);
        } finally {
            setLoading(false);
        }
    }, [projectId, assetType]);

    const createAsset = useCallback(async (
        assetData: Omit<T, 'id' | 'createdAt' | 'updatedAt'>
    ): Promise<string> => {
        setError(null);
        
        try {
            const assetId = await dataAssetsService.createAsset<T>(
                projectId,
                assetType,
                assetData
            );
            
            // Refresh the assets list
            await fetchAssets();
            
            return assetId;
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to create asset';
            setError(errorMessage);
            throw new Error(errorMessage);
        }
    }, [projectId, assetType, fetchAssets]);

    const updateAsset = useCallback(async (
        assetId: string,
        updates: Partial<Omit<T, 'id' | 'createdAt' | 'updatedAt'>>
    ): Promise<void> => {
        setError(null);
        
        try {
            await dataAssetsService.updateAsset<T>(
                projectId,
                assetType,
                assetId,
                updates
            );
            
            // Refresh the assets list
            await fetchAssets();
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to update asset';
            setError(errorMessage);
            throw new Error(errorMessage);
        }
    }, [projectId, assetType, fetchAssets]);

    const deleteAsset = useCallback(async (assetId: string): Promise<void> => {
        setError(null);
        
        try {
            await dataAssetsService.deleteAsset(projectId, assetType, assetId);
            
            // Refresh the assets list
            await fetchAssets();
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : 'Failed to delete asset';
            setError(errorMessage);
            throw new Error(errorMessage);
        }
    }, [projectId, assetType, fetchAssets]);

    const getNextSystemIndex = useCallback(async (): Promise<number> => {
        try {
            return await dataAssetsService.getNextSystemIndex(projectId);
        } catch (err) {
            console.error('Error getting next system index:', err);
            return 1;
        }
    }, [projectId]);

    useEffect(() => {
        if (autoFetch && projectId) {
            fetchAssets();
        }
    }, [autoFetch, projectId, fetchAssets]);

    return {
        assets,
        loading,
        error,
        createAsset,
        updateAsset,
        deleteAsset,
        fetchAssets,
        getNextSystemIndex
    };
}

// Specialized hook for systems
export function useSystems(projectId: string | null) {
    return useDataAssets<SystemAsset>({
        projectId: projectId || '',
        assetType: 'systems'
    });
} 
import { AssessmentCriteria } from '@/types/assessmentCriteria';

export interface Domain {
  id: string;
  name: string | { en: string; ar: string };
  specifications?: Array<{
    id: string;
    currentRating?: number;
    maturityLevel?: number;
    percentageValue?: number;
    complianceStatus?: string;
    [key: string]: unknown;
  }>;
  [key: string]: unknown;
}

/**
 * Calculate the domain rating based on its specifications and assessment criteria
 */
export function calculateDomainRating(
  domain: Domain,
  assessmentCriteria?: AssessmentCriteria | null
): number {
  if (!domain.specifications || domain.specifications.length === 0) {
    return 0;
  }

  const specs = domain.specifications;
  let totalRating = 0;
  let ratedSpecs = 0;
  let applicableSpecs = 0; // Track specifications that are applicable

  for (const spec of specs) {
    let rating = 0;
    let isApplicable = true;

    if (assessmentCriteria?.type === 'maturity') {
      // Use maturityLevel or currentRating
      rating = spec.maturityLevel || spec.currentRating || 0;
      if (rating > 0) {
        applicableSpecs++;
      }
    } else if (assessmentCriteria?.type === 'percentage') {
      // Use percentageValue or currentRating
      rating = spec.percentageValue || spec.currentRating || 0;
      if (rating > 0) {
        applicableSpecs++;
      }
    } else if (assessmentCriteria?.type === 'compliance') {
      // Convert compliance status to numeric value
      if (spec.complianceStatus) {
        switch (spec.complianceStatus.toLowerCase()) {
          case 'compliant':
            rating = 100;
            applicableSpecs++;
            break;
          case 'partially-compliant':
          case 'partially compliant':
            rating = 50;
            applicableSpecs++;
            break;
          case 'non-compliant':
          case 'non compliant':
            rating = 0;
            applicableSpecs++;
            break;
          case 'not-applicable':
          case 'not applicable':
            // Skip N/A items from calculation but don't count as applicable
            isApplicable = false;
            continue;
          default:
            rating = 0;
            applicableSpecs++;
        }
      } else if (spec.currentRating !== undefined) {
        rating = spec.currentRating;
        applicableSpecs++;
      } else {
        // No rating data available, count as applicable but not rated
        applicableSpecs++;
      }
    } else {
      // Default to currentRating or maturityLevel
      rating = spec.currentRating || spec.maturityLevel || 0;
      if (rating > 0) {
        applicableSpecs++;
      }
    }

    if (isApplicable && rating >= 0) { // Include 0 ratings for compliance (non-compliant)
      totalRating += rating;
      ratedSpecs++;
    }
  }

  // If no applicable specifications, return -1 to indicate "not applicable"
  if (applicableSpecs === 0) {
    return -1;
  }

  return ratedSpecs > 0 ? totalRating / ratedSpecs : 0;
}

/**
 * Calculate overall rating across multiple domains
 */
export function calculateOverallRating(
  domains: Domain[],
  assessmentCriteria?: AssessmentCriteria | null
): number {
  if (!domains || domains.length === 0) {
    return 0;
  }

  let totalRating = 0;
  let ratedDomains = 0;
  let applicableDomains = 0;

  for (const domain of domains) {
    const domainRating = calculateDomainRating(domain, assessmentCriteria);
    
    // Skip domains that are not applicable (-1)
    if (domainRating === -1) {
      continue;
    }
    
    applicableDomains++;
    
    // Include domains with rating >= 0 (including 0 for non-compliant)
    if (domainRating >= 0) {
      totalRating += domainRating;
      ratedDomains++;
    }
  }

  // If no applicable domains, return -1
  if (applicableDomains === 0) {
    return -1;
  }

  return ratedDomains > 0 ? totalRating / ratedDomains : 0;
}

/**
 * Check if existing ratings are compatible with the current assessment criteria type
 */
export function areRatingsCompatibleWithCriteriaType(
  domain: Domain,
  assessmentCriteria?: AssessmentCriteria | null
): boolean {
  if (!domain.specifications || domain.specifications.length === 0) {
    return true; // No ratings to be incompatible
  }

  if (!assessmentCriteria) {
    return true; // No criteria to check against
  }

  const criteriaType = assessmentCriteria.type;
  
  for (const spec of domain.specifications) {
    // Check if the specification has the appropriate rating type
    switch (criteriaType) {
      case 'maturity':
        // Compatible if has maturityLevel or generic currentRating
        if (spec.maturityLevel !== undefined || spec.currentRating !== undefined) {
          return true;
        }
        break;
      case 'percentage':
        // Compatible if has percentageValue or generic currentRating
        if (spec.percentageValue !== undefined || spec.currentRating !== undefined) {
          return true;
        }
        break;
      case 'compliance':
        // Compatible if has complianceStatus or generic currentRating
        if (spec.complianceStatus !== undefined || spec.currentRating !== undefined) {
          return true;
        }
        break;
      default:
        // For unknown types, consider currentRating as compatible
        if (spec.currentRating !== undefined) {
          return true;
        }
    }
  }

  return false; // No compatible ratings found
}

/**
 * Get compliance status from dynamic levels based on rating percentage
 */
export function getComplianceStatusFromLevels(
  rating: number,
  assessmentCriteria?: AssessmentCriteria | null,
  locale: string = 'en'
): { key: string; label: string; value: number } | null {
  if (!assessmentCriteria?.levels || assessmentCriteria.type !== 'compliance') {
    return null;
  }

  // Sort levels by value (ascending)
  const sortedLevels = Object.entries(assessmentCriteria.levels)
    .map(([key, level]) => ({ key, ...level }))
    .sort((a, b) => a.value - b.value);

  // Find the appropriate level based on rating
  // For compliance, we typically want the highest level that the rating qualifies for
  let matchingLevel = sortedLevels[0]; // Default to lowest level

  for (const level of sortedLevels) {
    if (rating >= level.value) {
      matchingLevel = level;
    } else {
      break;
    }
  }

  const label = typeof matchingLevel.label === 'string' 
    ? matchingLevel.label 
    : matchingLevel.label[locale] || matchingLevel.label.en;

  return {
    key: matchingLevel.key,
    label,
    value: matchingLevel.value
  };
}

/**
 * Format rating value for display based on assessment criteria type
 */
export function formatRatingForDisplay(
  rating: number,
  assessmentCriteria?: AssessmentCriteria | null,
  locale: string = 'en'
): string {
  if (!assessmentCriteria) {
    return rating.toString();
  }

  // Handle special case where all specifications are not applicable
  if (rating === -1) {
    return locale === 'ar' ? 'غير قابل للتطبيق' : 'Not Applicable';
  }

  switch (assessmentCriteria.type) {
    case 'maturity':
      // Display as level number
      return rating.toFixed(1);
    case 'percentage':
      // Display as percentage
      return `${Math.round(rating)}%`;
    case 'compliance':
      // Use dynamic compliance levels from assessmentCriteria
      const complianceStatus = getComplianceStatusFromLevels(rating, assessmentCriteria, locale);
      if (complianceStatus) {
        return complianceStatus.label;
      }
      // Fallback to hardcoded values if no dynamic levels found
      if (rating >= 90) return locale === 'ar' ? 'متوافق' : 'Compliant';
      if (rating >= 50) return locale === 'ar' ? 'متوافق جزئياً' : 'Partially Compliant';
      if (rating >= 0) return locale === 'ar' ? 'غير متوافق' : 'Non-Compliant';
      return locale === 'ar' ? 'غير قابل للتطبيق' : 'Not Applicable';
    default:
      return rating.toString();
  }
} 
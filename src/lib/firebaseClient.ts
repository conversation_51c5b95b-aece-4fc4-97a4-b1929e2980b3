import { initializeApp, getApp, getApps } from 'firebase/app';
import { getAuth } from 'firebase/auth';
import { getFirestore } from 'firebase/firestore';
import { enableIndexedDbPersistence } from 'firebase/firestore';

// Your web app's Firebase configuration
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID
};

// Initialize Firebase
const app = !getApps().length ? initializeApp(firebaseConfig) : getApp();

// Initialize Firebase services
const auth = getAuth(app);
const db = getFirestore(app);

// Enable offline persistence if we're in a browser environment
if (typeof window !== 'undefined') {
  // Try to enable persistence with better error handling and network status monitoring
  const enablePersistence = async () => {
    try {
      // enableIndexedDbPersistence allows only one tab to synchronize with the backend
      // enableMultiTabIndexedDbPersistence allows multiple tabs to synchronize with the backend
      await enableIndexedDbPersistence(db);
      
      console.log('Firebase persistence enabled successfully');
    } catch (err: unknown) {
      const error = err as { code?: string };
      console.error('Firebase persistence error:', error.code);
      
      if (error.code === 'failed-precondition') {
        // Multiple tabs open, persistence can only be enabled in one tab at a time
        console.warn('Firebase persistence not enabled: Multiple tabs open');
      } else if (error.code === 'unimplemented') {
        // The current browser does not support all of the features required to enable persistence
        console.warn('Firebase persistence not supported in this browser');
      }
    }
  };
  
  // Initial attempt to enable persistence
  enablePersistence();
  
  // Monitor online/offline status and handle reconnection
  window.addEventListener('online', () => {
    console.log('Application is back online');
    // No need to manually reconnect - Firestore handles this automatically
  });
  
  window.addEventListener('offline', () => {
    console.log('Application is offline');
    // Firestore will automatically use cached data when offline
  });
}

export { auth, db }; 
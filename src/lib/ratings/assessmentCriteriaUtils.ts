import { AssessmentCriteria } from '@/types';

/**
 * Gets the default rating object based on the assessment criteria type
 */
export function getDefaultRatingForType(
  type: 'maturity' | 'percentage' | 'compliance',
  specificationId: string,
  userId: string,
  initialMaturityLevel?: number,
  initialTargetLevel?: number,
  assessmentCriteria?: AssessmentCriteria | null
): Record<string, unknown> {
  const now = new Date();
  switch (type) {
    case 'maturity':
      return {
        ratingType: 'maturity',
        currentRating: initialMaturityLevel || 1,
        targetRating: initialTargetLevel || 5,
        comments: '',
        createdAt: now,
        updatedAt: now,
        createdBy: userId,
        updatedBy: userId,
        specificationId
      };
    case 'compliance': {
      // Use dynamic compliance level or fallback
      let defaultComplianceStatus = 'Non-Compliant';
      
      if (assessmentCriteria?.levels) {
        // Find the lowest level (by value) to use as default
        const sortedLevels = Object.values(assessmentCriteria.levels).sort((a, b) => a.value - b.value);
        if (sortedLevels.length > 0) {
          const label = sortedLevels[0].label;
          defaultComplianceStatus = typeof label === 'string' ? label : label.en;
        }
      }
      
      return {
        ratingType: 'compliance',
        currentRating: 0, // Non-compliant
        targetRating: 1, // Compliant
        complianceStatus: defaultComplianceStatus,
        comments: '',
        createdAt: now,
        updatedAt: now,
        createdBy: userId,
        updatedBy: userId,
        specificationId
      };
    }
    case 'percentage':
      return {
        ratingType: 'percentage',
        currentRating: 0,
        targetRating: 100,
        percentageValue: 0,
        comments: '',
        createdAt: now,
        updatedAt: now,
        createdBy: userId,
        updatedBy: userId,
        specificationId
      };
    default:
      return {
        ratingType: 'unknown',
        comments: '',
        createdAt: now,
        updatedAt: now,
        createdBy: userId,
        updatedBy: userId,
        specificationId
      };
  }
}

/**
 * Gets the domain weight for a specific domain from assessment criteria
 */
export function getDomainWeight(
  domainId: string, 
  assessmentCriteria?: AssessmentCriteria | null
): number | null {
  if (!assessmentCriteria?.domainWeights || !Array.isArray(assessmentCriteria.domainWeights)) {
    return null;
  }
  
  const domainWeight = assessmentCriteria.domainWeights.find(dw => dw.domainId === domainId);
  return domainWeight?.weight ?? null;
}

/**
 * Gets all domain weights from assessment criteria
 */
export function getAllDomainWeights(
  assessmentCriteria?: AssessmentCriteria | null
): Array<{ domainId: string; weight: number }> {
  if (!assessmentCriteria?.domainWeights || !Array.isArray(assessmentCriteria.domainWeights)) {
    return [];
  }
  
  return assessmentCriteria.domainWeights.map(dw => ({
    domainId: dw.domainId,
    weight: dw.weight
  }));
}

/**
 * Formats domain weight for display
 */
export function formatDomainWeight(
  weight: number | null,
  locale: string = 'en',
  showPercentage: boolean = true
): string {
  if (weight === null || weight === undefined) {
    return locale === 'ar' ? 'غير محدد' : 'Not Set';
  }
  
  return showPercentage ? `${weight}%` : weight.toString();
}

/**
 * Gets domain weight display configuration for UI components
 */
export function getDomainWeightConfig(
  weight: number | null,
  locale: string = 'en'
): {
  display: string;
  color: string;
  bgColor: string;
  isSet: boolean;
} {
  const isSet = weight !== null && weight !== undefined;
  
  if (!isSet) {
    return {
      display: locale === 'ar' ? 'غير محدد' : 'Not Set',
      color: 'text-gray-600',
      bgColor: 'bg-gray-100',
      isSet: false
    };
  }
  
  // Color coding based on weight value
  let color = 'text-blue-700';
  let bgColor = 'bg-blue-50';
  
  if (weight >= 80) {
    color = 'text-red-700';
    bgColor = 'bg-red-50';
  } else if (weight >= 60) {
    color = 'text-orange-700';
    bgColor = 'bg-orange-50';
  } else if (weight >= 40) {
    color = 'text-yellow-700';
    bgColor = 'bg-yellow-50';
  } else if (weight >= 20) {
    color = 'text-green-700';
    bgColor = 'bg-green-50';
  }
  
  return {
    display: `${weight}%`,
    color,
    bgColor,
    isSet: true
  };
}

/**
 * Validates domain weights (should sum to 100% if all domains have weights)
 */
export function validateDomainWeights(
  domainWeights: Array<{ domainId: string; weight: number }>,
  allowPartialWeights: boolean = true
): {
  isValid: boolean;
  totalWeight: number;
  issues: string[];
} {
  const issues: string[] = [];
  const totalWeight = domainWeights.reduce((sum, dw) => sum + dw.weight, 0);
  
  // Check for negative weights
  const negativeWeights = domainWeights.filter(dw => dw.weight < 0);
  if (negativeWeights.length > 0) {
    issues.push('Domain weights cannot be negative');
  }
  
  // Check for weights over 100%
  const overWeights = domainWeights.filter(dw => dw.weight > 100);
  if (overWeights.length > 0) {
    issues.push('Individual domain weights cannot exceed 100%');
  }
  
  // Check total weight
  if (!allowPartialWeights && Math.abs(totalWeight - 100) > 0.01) {
    issues.push('Total domain weights must sum to 100%');
  } else if (totalWeight > 100) {
    issues.push('Total domain weights cannot exceed 100%');
  }
  
  return {
    isValid: issues.length === 0,
    totalWeight,
    issues
  };
}

/**
 * Gets the default label for a compliance status
 */
export function getDefaultComplianceLabels(status: string, locale: string = 'en'): string {
  const labels = {
    compliant: {
      en: 'Compliant',
      ar: 'متوافق'
    },
    partiallyCompliant: {
      en: 'Partially Compliant',
      ar: 'متوافق جزئياً'
    },
    nonCompliant: {
      en: 'Non-Compliant',
      ar: 'غير متوافق'
    },
    notApplicable: {
      en: 'Not Applicable',
      ar: 'غير قابل للتطبيق'
    }
  };

  return labels[status as keyof typeof labels]?.[locale as keyof typeof labels.compliant] || status;
}

/**
 * Gets the color for a maturity level based on assessment criteria or default colors
 */
export function getMaturityLevelColor(level: number, assessmentCriteria?: AssessmentCriteria | null): string {
  // If assessment criteria is provided, check if it has custom colors for levels
  if (assessmentCriteria?.levels) {
    const levelObj = Object.values(assessmentCriteria.levels).find(l => l.value === level);
    if (levelObj?.color) {
      return levelObj.color;
    }
  }
  
  // Default colors for maturity levels if not specified in assessment criteria
  switch (level) {
    case 1:
      return "bg-red-500 text-white dark:bg-red-600";
    case 2:
      return "bg-orange-500 text-white dark:bg-orange-600";
    case 3:
      return "bg-yellow-500 text-white dark:bg-yellow-600";
    case 4:
      return "bg-blue-500 text-white dark:bg-blue-600";
    case 5:
      return "bg-green-500 text-white dark:bg-green-600";
    default:
      // For any other level, use a gradient based on the level value
      if (level <= 0) {
        return "bg-gray-500 text-white dark:bg-gray-600";
      } else if (level > 5) {
        return "bg-purple-500 text-white dark:bg-purple-600";
      } else {
        return "bg-teal-500 text-white dark:bg-teal-600";
      }
  }
}

/**
 * Gets the color for a percentage value
 */
export function getPercentageColor(percentage: number): string {
  if (percentage >= 80) {
    return "bg-green-500 text-white dark:bg-green-600";
  } else if (percentage >= 60) {
    return "bg-blue-500 text-white dark:bg-blue-600";
  } else if (percentage >= 40) {
    return "bg-yellow-500 text-white dark:bg-yellow-600";
  } else if (percentage >= 20) {
    return "bg-orange-500 text-white dark:bg-orange-600";
  } else {
    return "bg-red-500 text-white dark:bg-red-600";
  }
}

/**
 * Gets the color for a compliance status
 */
export function getComplianceStatusColor(status: string): string {
  switch (status.toLowerCase()) {
    case 'compliant':
    case 'fully compliant':
      return "bg-green-500 text-white dark:bg-green-600";
    case 'partially compliant':
    case 'partial':
    case 'partial very fully compliant':
      return "bg-yellow-500 text-white dark:bg-yellow-600";
    case 'noncompliant':
    case 'non-compliant':
    case 'non compliant':
    case 'initial':
      return "bg-red-500 text-white dark:bg-red-600";
    case 'not applicable':
    case 'n/a':
      return "bg-gray-500 text-white dark:bg-gray-600";
    default:
      return "bg-blue-500 text-white dark:bg-blue-600";
  }
} 
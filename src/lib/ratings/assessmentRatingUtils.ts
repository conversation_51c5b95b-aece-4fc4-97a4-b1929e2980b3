import { AssessmentCriteria } from '@/types/assessmentCriteria';

/**
 * Maps a percentage value (0-100) to the closest maturity level
 * @param percentage Percentage value (0-100)
 * @param assessmentCriteria Assessment criteria object containing levels
 * @returns The closest level object or null if no match
 */
export const mapPercentageToMaturityLevel = (
  percentage: number,
  assessmentCriteria?: AssessmentCriteria | null
) => {
  if (!assessmentCriteria?.levels || percentage === null || percentage === undefined) {
    return null;
  }

  // Handle special case for not applicable (-1)
  if (percentage === -1) {
    return null;
  }

  // Sort levels by value ascending
  const sortedLevels = Object.entries(assessmentCriteria.levels)
    .map(([key, level]) => ({ key, ...level }))
    .sort((a, b) => a.value - b.value);

  // Find the highest level that's less than or equal to the percentage
  let closestLevel = null;
  for (const level of sortedLevels) {
    if (level.value <= percentage) {
      closestLevel = level;
    } else {
      break;
    }
  }

  // If all levels are higher than the percentage, use the lowest level
  if (!closestLevel && sortedLevels.length > 0) {
    closestLevel = sortedLevels[0];
  }

  return closestLevel;
};

/**
 * Maps a percentage value (0-100) to the closest compliance status
 * @param percentage Percentage value (0-100)
 * @param assessmentCriteria Assessment criteria object containing levels
 * @returns The closest compliance status or null if no match
 */
export const mapPercentageToComplianceStatus = (
  percentage: number,
  assessmentCriteria?: AssessmentCriteria | null
) => {
  return mapPercentageToMaturityLevel(percentage, assessmentCriteria);
};

/**
 * Gets the formatted maturity level display text
 * @param percentage Percentage value (0-100)
 * @param assessmentCriteria Assessment criteria object containing levels
 * @param locale Locale code ('en' or 'ar')
 * @returns Formatted text showing level and index (e.g., "Optimizing (2/3)")
 */
export const getMaturityLevelText = (
  percentage: number,
  assessmentCriteria?: AssessmentCriteria | null,
  locale: string = 'en'
) => {
  if (!assessmentCriteria?.levels) {
    return locale === 'ar' ? 'لم يبدأ التقييم' : 'Assessment Not Started';
  }

  // Handle special case for not applicable (-1)
  if (percentage === -1) {
    return locale === 'ar' ? 'غير قابل للتطبيق' : 'Not Applicable';
  }

  const level = mapPercentageToMaturityLevel(percentage, assessmentCriteria);
  if (!level) {
    return locale === 'ar' ? 'لم يبدأ التقييم' : 'Assessment Not Started';
  }

  // Get total levels count
  const totalLevels = Object.keys(assessmentCriteria.levels).length;
  
  // Find the index of this level
  const levelIndex = Object.values(assessmentCriteria.levels)
    .sort((a, b) => a.value - b.value)
    .findIndex(l => l.value === level.value) + 1;
  
  // Get localized label
  const label = level.label
    ? (locale === 'ar' && typeof level.label === 'object' && 'ar' in level.label && level.label.ar 
       ? level.label.ar 
       : (typeof level.label === 'object' && 'en' in level.label ? level.label.en : ''))
    : '';
  
  return `${label} (${levelIndex}/${totalLevels})`;
}; 
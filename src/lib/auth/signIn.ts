import { signInWithEmailAndPassword, AuthError } from 'firebase/auth';
import { doc, getDoc, updateDoc, serverTimestamp } from 'firebase/firestore';
import { auth, db } from '../firebaseClient';

export type AuthErrorCode = 
  | 'auth/invalid-credential'
  | 'auth/user-disabled'
  | 'auth/user-not-found'
  | 'auth/wrong-password'
  | 'auth/too-many-requests'
  | 'auth/network-request-failed'
  | 'auth/firestore-error'
  | 'auth/user-inactive'
  | 'auth/role-invalid'
  | 'auth/unknown-error';

// Define a type for the user data
export interface UserData {
  role: string;
  status: string;
  lastLogin?: Date;
  [key: string]: unknown;
}

export interface SignInResult {
  success: boolean;
  error?: AuthErrorCode;
  errorMessage?: string;
  userData?: UserData;
  isTemporaryError?: boolean;
}

/**
 * Signs in a user with email and password, validates their role,
 * and updates their last login timestamp
 */
export async function signIn(email: string, password: string, retryCount = 2): Promise<SignInResult> {
  try {
    // Attempt to sign in with Firebase Auth
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    const { user } = userCredential;
    
    try {
      // Fetch user document from Firestore
      const userDocRef = doc(db, 'users', user.uid);
      const userDoc = await getDoc(userDocRef);
      
      if (!userDoc.exists()) {
        // Sign out if user document doesn't exist in Firestore
        await auth.signOut();
        return { 
          success: false, 
          error: 'auth/user-not-found',
          errorMessage: 'User not found in database' 
        };
      }
      
      const userData = userDoc.data() as UserData;
      
      // Validate user status is active
      if (userData.status !== 'Active') {
        await auth.signOut();
        return { 
          success: false, 
          error: 'auth/user-inactive',
          errorMessage: 'User account is inactive' 
        };
      }
      
      // Validate user has correct role
      if (userData.role !== 'Client' && userData.role !== 'Consultant') {
        await auth.signOut();
        return { 
          success: false, 
          error: 'auth/role-invalid',
          errorMessage: 'Invalid role for this portal' 
        };
      }
      
      // Try to update last login timestamp, but don't block success if it fails
      try {
        await updateDoc(userDocRef, {
          lastLogin: serverTimestamp()
        });
      } catch (updateError) {
        // Log error but continue - updating last login is not critical
        console.warn('Failed to update last login timestamp:', updateError);
      }
      
      return {
        success: true,
        userData
      };
    } catch (firestoreError) {
      console.error('Firestore error during sign in:', firestoreError);
      
      // Check if we should retry the Firestore operations
      if (retryCount > 0) {
        console.log(`Retrying Firestore operations, attempts left: ${retryCount}`);
        await new Promise(resolve => setTimeout(resolve, 800));
        return signIn(email, password, retryCount - 1);
      }
      
      // If authentication succeeded but Firestore failed, we can still consider this a "partial success"
      // The user is authenticated, even if we couldn't fetch their data
      // The AuthContext will handle redirecting them
      return {
        success: false,
        error: 'auth/firestore-error',
        errorMessage: 'Connection to database failed',
        isTemporaryError: true // Mark as temporary so UI can handle appropriately
      };
    }
  } catch (error) {
    const authError = error as AuthError;
    const errorCode = authError.code as AuthErrorCode || 'auth/unknown-error';
    
    // Determine if this is a temporary error that might resolve on its own
    const isTemporaryError = errorCode === 'auth/network-request-failed' || 
                            errorCode === 'auth/too-many-requests';
    
    let errorMessage: string;
    switch (errorCode) {
      case 'auth/invalid-credential':
      case 'auth/wrong-password':
      case 'auth/user-not-found':
        errorMessage = 'Invalid email or password';
        break;
      case 'auth/user-disabled':
        errorMessage = 'User account is inactive';
        break;
      case 'auth/too-many-requests':
        errorMessage = 'Too many attempts. Try again later';
        break;
      case 'auth/network-request-failed':
        errorMessage = 'Network connection error';
        break;
      default:
        errorMessage = 'An error occurred during sign in';
    }
    
    return {
      success: false,
      error: errorCode,
      errorMessage,
      isTemporaryError
    };
  }
} 
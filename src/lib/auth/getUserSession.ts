import { doc, getDoc } from 'firebase/firestore';
import { db } from '../firebaseClient';
import { User } from 'firebase/auth';

export interface UserSession {
  uid: string;
  email: string | null;
  role: 'Client' | 'Consultant';
  assignedProjectIds: string[];
  organizationId: string;
  locale: string;
  name?: string;
  status: string;
}

/**
 * Fetches the user session data from Firestore based on Firebase Auth user
 * Includes retry logic for handling connection issues
 */
export async function getUserSession(user: User | null, retryCount = 3): Promise<UserSession | null> {
  if (!user) return null;
  
  try {
    const userDocRef = doc(db, 'users', user.uid);
    const userDoc = await getDoc(userDocRef);
    
    if (!userDoc.exists()) {
      console.error('User document not found in Firestore');
      return null;
    }
    
    const userData = userDoc.data();
    
    // Validate required user role and status
    if (!userData.role || (userData.role !== 'Client' && userData.role !== 'Consultant')) {
      console.error('Invalid user role for client/consultant portal');
      return null;
    }
    
    if (userData.status !== 'Active') {
      console.error('User account is not active');
      return null;
    }
    
    // Construct user session object with defaults for missing fields
    const userSession: UserSession = {
      uid: user.uid,
      email: user.email,
      role: userData.role as 'Client' | 'Consultant',
      assignedProjectIds: userData.assignedProjectIds || [],
      organizationId: userData.organizationId || '',
      locale: userData.locale || 'en',
      name: userData.name,
      status: userData.status
    };
    
    return userSession;
  } catch (error) {
    console.error('Error fetching user session data:', error);
    
    // Implement retry logic for connection issues
    if (retryCount > 0) {
      console.log(`Retrying getUserSession, attempts left: ${retryCount - 1}`);
      // Wait for 500ms before retrying
      await new Promise(resolve => setTimeout(resolve, 500));
      return getUserSession(user, retryCount - 1);
    }
    
    return null;
  }
} 
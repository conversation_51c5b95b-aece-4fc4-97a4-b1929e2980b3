import { 
  doc, 
  getDoc, 
  collection,
  getDocs,
  query,
  where
} from 'firebase/firestore';
import { db } from '@/lib/firebaseClient';

// Types for the comprehensive data structure
export interface ComplianceDataContext {
  assessment: AssessmentData;
  framework: FrameworkData;
  ratings: RatingsData;
  criteria: AssessmentCriteriaData;
  statistics: StatisticsData;
  metadata: MetadataInfo;
}

export interface AssessmentData {
  id: string;
  name: { en: string; ar: string };
  frameworkId: string;
  projectId: string;
  status: string;
  type: string;
  startDate: string;
  targetCompletionDate: string;
  assessor: {
    id: string;
    name: string;
    email: string;
  };
  domainAverages: Record<string, DomainAverage>;
  overallScore: number;
  completionPercentage: number;
  createdAt: string;
  updatedAt: string;
}

export interface DomainAverage {
  average: number;
  name: { en: string; ar: string };
  totalSpecifications: number;
  ratedSpecifications: number;
  domainId: string;
  weight?: number;
  weightedContribution?: number;
}

export interface FrameworkData {
  id: string;
  name: { en: string; ar: string };
  version: string;
  domains: DomainData[];
  totalSpecifications: number;
  totalControls: number;
}

export interface DomainData {
  id: string;
  name: { en: string; ar: string };
  description?: { en: string; ar: string };
  score: number;
  maxScore: number;
  controls: ControlData[];
  specifications: SpecificationData[];
  weight?: number;
}

export interface ControlData {
  id: string;
  name: { en: string; ar: string };
  description?: { en: string; ar: string };
  domainId: string;
  specifications: SpecificationData[];
}

export interface SpecificationData {
  id: string;
  name: { en: string; ar: string };
  description?: { en: string; ar: string };
  domainId: string;
  controlId: string;
  control?: {
    id: string;
    name: { en: string; ar: string };
  };
  maturityLevel?: number;
  percentageValue?: number;
  complianceStatus?: string;
  currentRating?: number;
  subSpecifications?: SubSpecificationData[];
  rating?: RatingData;
}

export interface SubSpecificationData {
  id: string;
  name: { en: string; ar: string };
  description?: { en: string; ar: string };
  versionHistory?: Array<{
    version: string;
    date: string;
  }>;
  updatedAt?: string;
}

export interface RatingsData {
  individual: Record<string, RatingData>;
  summary: RatingsSummary;
  byDomain: Record<string, DomainRatings>;
  byControl: Record<string, ControlRatings>;
}

export interface RatingData {
  ratingType: string;
  specificationId: string;
  complianceStatus: string;
  targetStatus: string;
  currentRating: number;
  targetRating: number;
  comments: string;
  domainName: { en: string; ar: string };
  controlName: { en: string; ar: string };
  evidence?: Array<{
    type: string;
    name: string;
    url: string;
  }>;
  assessmentDate: string;
  updatedAt: string;
  updatedBy: string;
  createdAt: string;
  createdBy: string;
}

export interface RatingsSummary {
  totalRatings: number;
  totalSpecifications: number;
  completionRate: number;
  averageRating: number;
  complianceDistribution: {
    compliant: number;
    partiallyCompliant: number;
    nonCompliant: number;
    notApplicable: number;
    notRated: number;
  };
}

export interface DomainRatings {
  domainId: string;
  domainName: { en: string; ar: string };
  totalSpecifications: number;
  ratedSpecifications: number;
  averageRating: number;
  weight?: number;
  weightedScore?: number;
  ratings: RatingData[];
}

export interface ControlRatings {
  controlId: string;
  controlName: { en: string; ar: string };
  domainId: string;
  totalSpecifications: number;
  ratedSpecifications: number;
  averageRating: number;
  ratings: RatingData[];
}

export interface AssessmentCriteriaData {
  id: string;
  name: { en: string; ar: string };
  type: string;
  version: string;
  levels: Record<string, CriteriaLevel>;
  domainWeights: Array<{
    domainId: string;
    weight: number;
  }>;
  totalWeight: number;
  createdAt: string;
  updatedAt: string;
}

export interface CriteriaLevel {
  value: number;
  label: { en: string; ar: string };
  description?: { en: string; ar: string };
  color: string;
}

export interface StatisticsData {
  framework: {
    totalDomains: number;
    totalControls: number;
    totalSpecifications: number;
    totalSubSpecifications: number;
  };
  ratings: {
    totalRated: number;
    totalUnrated: number;
    completionPercentage: number;
    averageScore: number;
    weightedScore: number;
  };
  compliance: {
    compliantCount: number;
    partiallyCompliantCount: number;
    nonCompliantCount: number;
    notApplicableCount: number;
    complianceRate: number;
  };
  domains: Record<string, {
    completionRate: number;
    averageScore: number;
    weight: number;
    contribution: number;
  }>;
}

export interface MetadataInfo {
  fetchedAt: string;
  dataVersion: string;
  sources: {
    framework: string;
    assessment: string;
    ratings: string;
    criteria: string;
  };
  locale: string;
  projectInfo: {
    id: string;
    name?: string;
  };
}

/**
 * Comprehensive service to fetch all compliance assessment data
 * Perfect for providing complete context to AI systems
 */
export class ComplianceDataService {
  
  /**
   * Fetch all compliance data for a specific assessment
   */
  static async getAllComplianceData(
    projectId: string,
    assessmentId: string,
    locale: string = 'en'
  ): Promise<ComplianceDataContext> {
    try {
      console.log(`Fetching comprehensive compliance data for assessment: ${assessmentId}`);
      
      // Step 1: Get assessment metadata to find framework
      const assessment = await this.fetchAssessmentData(projectId, assessmentId);
      
      // Step 2: Get framework structure
      const framework = await this.fetchFrameworkData(assessment.frameworkId);
      
      // Step 3: Get assessment criteria and weights
      const criteria = await this.fetchAssessmentCriteria(assessment.frameworkId);
      
      // Step 4: Get all ratings
      const ratings = await this.fetchAllRatings(projectId, assessmentId, framework);
      
      // Step 5: Enhance framework data with ratings
      const enhancedFramework = this.enhanceFrameworkWithRatings(framework, ratings);
      
      // Step 6: Calculate comprehensive statistics
      const statistics = this.calculateStatistics(enhancedFramework, ratings, criteria);
      
      // Step 7: Create metadata
      const metadata: MetadataInfo = {
        fetchedAt: new Date().toISOString(),
        dataVersion: '1.0',
        sources: {
          framework: `frameworks/${assessment.frameworkId}`,
          assessment: `projects/${projectId}/ComplianceAssessment/${assessmentId}`,
          ratings: `projects/${projectId}/ComplianceAssessment/${assessmentId}/ratings`,
          criteria: `assessmentCriteria/${assessment.frameworkId}`
        },
        locale,
        projectInfo: {
          id: projectId
        }
      };

      const result: ComplianceDataContext = {
        assessment,
        framework: enhancedFramework,
        ratings,
        criteria,
        statistics,
        metadata
      };

      console.log('Successfully fetched comprehensive compliance data');
      return result;

    } catch (error) {
      console.error('Error fetching comprehensive compliance data:', error);
      throw new Error(`Failed to fetch compliance data: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Fetch assessment metadata
   */
  private static async fetchAssessmentData(projectId: string, assessmentId: string): Promise<AssessmentData> {
    const assessmentRef = doc(db, `projects/${projectId}/ComplianceAssessment/${assessmentId}`);
    const assessmentSnap = await getDoc(assessmentRef);
    
    if (!assessmentSnap.exists()) {
      throw new Error('Assessment not found');
    }
    
    const data = assessmentSnap.data();
    return {
      id: assessmentSnap.id,
      name: data.name || { en: 'Unnamed Assessment', ar: 'تقييم غير مسمى' },
      frameworkId: data.frameworkId,
      projectId: data.projectId || projectId,
      status: data.status || 'unknown',
      type: data.type || 'compliance',
      startDate: data.startDate || '',
      targetCompletionDate: data.targetCompletionDate || '',
      assessor: data.assessor || { id: '', name: '', email: '' },
      domainAverages: data.domainAverages || {},
      overallScore: data.overallScore || 0,
      completionPercentage: data.completionPercentage || 0,
      createdAt: data.createdAt?.toDate?.()?.toISOString() || '',
      updatedAt: data.updatedAt?.toDate?.()?.toISOString() || ''
    };
  }

  /**
   * Fetch complete framework structure
   */
  private static async fetchFrameworkData(frameworkId: string): Promise<FrameworkData> {
    // Get framework metadata
    const frameworkRef = doc(db, `frameworks/${frameworkId}`);
    const frameworkSnap = await getDoc(frameworkRef);
    
    const frameworkData = frameworkSnap.exists() ? frameworkSnap.data() : {};
    
    // Get all domains
    const domainsRef = collection(db, `frameworks/${frameworkId}/domains`);
    const domainsSnapshot = await getDocs(domainsRef);
    
    const domains: DomainData[] = [];
    let totalSpecifications = 0;
    let totalControls = 0;
    
    for (const domainDoc of domainsSnapshot.docs) {
      const domainData = domainDoc.data();
      
      // Get controls for this domain
      const controlsRef = collection(db, `frameworks/${frameworkId}/domains/${domainDoc.id}/controls`);
      const controlsSnapshot = await getDocs(controlsRef);
      
      const controls: ControlData[] = [];
      const domainSpecifications: SpecificationData[] = [];
      
      for (const controlDoc of controlsSnapshot.docs) {
        const controlData = controlDoc.data();
        totalControls++;
        
        // Get specifications for this control
        const specsRef = collection(db, `frameworks/${frameworkId}/domains/${domainDoc.id}/controls/${controlDoc.id}/specifications`);
        const specsSnapshot = await getDocs(specsRef);
        
        const controlSpecifications: SpecificationData[] = [];
        
        for (const specDoc of specsSnapshot.docs) {
          const specData = specDoc.data();
          totalSpecifications++;
          
          const specification: SpecificationData = {
            id: specDoc.id,
            name: specData.name || { en: specDoc.id, ar: specDoc.id },
            description: specData.description,
            domainId: domainDoc.id,
            controlId: controlDoc.id,
            control: {
              id: controlDoc.id,
              name: controlData.name || { en: controlDoc.id, ar: controlDoc.id }
            },
            maturityLevel: specData.maturityLevel,
            percentageValue: specData.percentageValue,
            complianceStatus: specData.complianceStatus,
            currentRating: specData.currentRating,
            subSpecifications: specData.subSpecifications || []
          };
          
          controlSpecifications.push(specification);
          domainSpecifications.push(specification);
        }
        
        controls.push({
          id: controlDoc.id,
          name: controlData.name || { en: controlDoc.id, ar: controlDoc.id },
          description: controlData.description,
          domainId: domainDoc.id,
          specifications: controlSpecifications
        });
      }
      
      domains.push({
        id: domainDoc.id,
        name: domainData.name || { en: domainDoc.id, ar: domainDoc.id },
        description: domainData.description,
        score: domainData.score || 0,
        maxScore: domainData.maxScore || 100,
        controls,
        specifications: domainSpecifications
      });
    }
    
    return {
      id: frameworkId,
      name: frameworkData.name || { en: frameworkId, ar: frameworkId },
      version: frameworkData.version || '1.0',
      domains,
      totalSpecifications,
      totalControls
    };
  }

  /**
   * Fetch assessment criteria and weights
   */
  private static async fetchAssessmentCriteria(frameworkId: string): Promise<AssessmentCriteriaData> {
    const criteriaRef = doc(db, `assessmentCriteria/${frameworkId}`);
    const criteriaSnap = await getDoc(criteriaRef);
    
    if (!criteriaSnap.exists()) {
      throw new Error('Assessment criteria not found');
    }
    
    const data = criteriaSnap.data();
    const domainWeights = data.domainWeights || [];
    const totalWeight = domainWeights.reduce((sum: number, dw: any) => sum + dw.weight, 0);
    
    return {
      id: criteriaSnap.id,
      name: data.name || { en: 'Assessment Criteria', ar: 'معايير التقييم' },
      type: data.type || 'compliance',
      version: data.version || '1.0',
      levels: data.levels || {},
      domainWeights,
      totalWeight,
      createdAt: data.createdAt?.toDate?.()?.toISOString() || '',
      updatedAt: data.updatedAt?.toDate?.()?.toISOString() || ''
    };
  }

  /**
   * Fetch all ratings for the assessment
   */
  private static async fetchAllRatings(
    projectId: string, 
    assessmentId: string, 
    framework: FrameworkData
  ): Promise<RatingsData> {
    // Try new structure first
    const ratingsRef = collection(db, `projects/${projectId}/ComplianceAssessment/${assessmentId}/ratings`);
    const ratingsSnapshot = await getDocs(ratingsRef);
    
    const individual: Record<string, RatingData> = {};
    
    // Process ratings from new structure
    ratingsSnapshot.forEach(doc => {
      const data = doc.data();
      individual[doc.id] = {
        ratingType: data.ratingType || 'compliance',
        specificationId: doc.id,
        complianceStatus: data.complianceStatus || '',
        targetStatus: data.targetStatus || 'Compliant',
        currentRating: data.currentRating || 0,
        targetRating: data.targetRating || 100,
        comments: data.comments || '',
        domainName: data.domainName || { en: '', ar: '' },
        controlName: data.controlName || { en: '', ar: '' },
        evidence: data.evidence || [],
        assessmentDate: data.assessmentDate?.toDate?.()?.toISOString() || '',
        updatedAt: data.updatedAt?.toDate?.()?.toISOString() || '',
        updatedBy: data.updatedBy || '',
        createdAt: data.createdAt?.toDate?.()?.toISOString() || '',
        createdBy: data.createdBy || ''
      };
    });
    
    // Fallback to legacy structure if no ratings found
    if (Object.keys(individual).length === 0) {
      const legacyRatingsRef = collection(db, `projects/${projectId}/ratings`);
      const legacyRatingsSnapshot = await getDocs(legacyRatingsRef);
      
      legacyRatingsSnapshot.forEach(doc => {
        const ratingId = doc.id;
        if (ratingId.startsWith('specifications_')) {
          const specId = ratingId.replace('specifications_', '');
          const data = doc.data();
          individual[specId] = {
            ratingType: data.ratingType || 'compliance',
            specificationId: specId,
            complianceStatus: data.complianceStatus || '',
            targetStatus: 'Compliant',
            currentRating: data.currentRating || 0,
            targetRating: 100,
            comments: data.comments || '',
            domainName: { en: '', ar: '' },
            controlName: { en: '', ar: '' },
            evidence: [],
            assessmentDate: data.assessmentDate?.toDate?.()?.toISOString() || '',
            updatedAt: data.updatedAt?.toDate?.()?.toISOString() || '',
            updatedBy: data.updatedBy || '',
            createdAt: data.createdAt?.toDate?.()?.toISOString() || '',
            createdBy: data.createdBy || ''
          };
        }
      });
    }
    
    // Calculate summary statistics
    const summary = this.calculateRatingsSummary(individual, framework);
    
    // Group by domain and control
    const byDomain = this.groupRatingsByDomain(individual, framework);
    const byControl = this.groupRatingsByControl(individual, framework);
    
    return {
      individual,
      summary,
      byDomain,
      byControl
    };
  }

  /**
   * Enhance framework data with rating information
   */
  private static enhanceFrameworkWithRatings(
    framework: FrameworkData, 
    ratings: RatingsData
  ): FrameworkData {
    const enhancedDomains = framework.domains.map(domain => {
      const enhancedControls = domain.controls.map(control => {
        const enhancedSpecifications = control.specifications.map(spec => ({
          ...spec,
          rating: ratings.individual[spec.id]
        }));
        
        return {
          ...control,
          specifications: enhancedSpecifications
        };
      });
      
      const enhancedSpecifications = domain.specifications.map(spec => ({
        ...spec,
        rating: ratings.individual[spec.id]
      }));
      
      return {
        ...domain,
        controls: enhancedControls,
        specifications: enhancedSpecifications
      };
    });
    
    return {
      ...framework,
      domains: enhancedDomains
    };
  }

  /**
   * Calculate comprehensive statistics
   */
  private static calculateStatistics(
    framework: FrameworkData,
    ratings: RatingsData,
    criteria: AssessmentCriteriaData
  ): StatisticsData {
    const totalSubSpecifications = framework.domains.reduce((total, domain) => 
      total + domain.specifications.reduce((specTotal, spec) => 
        specTotal + (spec.subSpecifications?.length || 0), 0), 0);
    
    // Calculate domain statistics with weights
    const domainStats: Record<string, any> = {};
    let totalWeightedScore = 0;
    let totalWeight = 0;
    
    framework.domains.forEach(domain => {
      const domainRatings = ratings.byDomain[domain.id];
      const domainWeight = criteria.domainWeights.find(dw => dw.domainId === domain.id);
      const weight = domainWeight?.weight || 0;
      
      if (domainRatings) {
        const contribution = (domainRatings.averageRating * weight) / 100;
        totalWeightedScore += contribution;
        totalWeight += weight;
        
        domainStats[domain.id] = {
          completionRate: (domainRatings.ratedSpecifications / domainRatings.totalSpecifications) * 100,
          averageScore: domainRatings.averageRating,
          weight,
          contribution
        };
      } else {
        domainStats[domain.id] = {
          completionRate: 0,
          averageScore: 0,
          weight,
          contribution: 0
        };
      }
    });
    
    return {
      framework: {
        totalDomains: framework.domains.length,
        totalControls: framework.totalControls,
        totalSpecifications: framework.totalSpecifications,
        totalSubSpecifications
      },
      ratings: {
        totalRated: ratings.summary.totalRatings,
        totalUnrated: ratings.summary.totalSpecifications - ratings.summary.totalRatings,
        completionPercentage: ratings.summary.completionRate,
        averageScore: ratings.summary.averageRating,
        weightedScore: totalWeight > 0 ? totalWeightedScore : ratings.summary.averageRating
      },
      compliance: {
        compliantCount: ratings.summary.complianceDistribution.compliant,
        partiallyCompliantCount: ratings.summary.complianceDistribution.partiallyCompliant,
        nonCompliantCount: ratings.summary.complianceDistribution.nonCompliant,
        notApplicableCount: ratings.summary.complianceDistribution.notApplicable,
        complianceRate: (ratings.summary.complianceDistribution.compliant / Math.max(ratings.summary.totalRatings, 1)) * 100
      },
      domains: domainStats
    };
  }

  /**
   * Calculate ratings summary
   */
  private static calculateRatingsSummary(
    ratings: Record<string, RatingData>,
    framework: FrameworkData
  ): RatingsSummary {
    const ratingValues = Object.values(ratings);
    const totalRatings = ratingValues.length;
    const totalSpecifications = framework.totalSpecifications;
    
    const averageRating = totalRatings > 0 
      ? ratingValues.reduce((sum, rating) => sum + rating.currentRating, 0) / totalRatings 
      : 0;
    
    const complianceDistribution = {
      compliant: 0,
      partiallyCompliant: 0,
      nonCompliant: 0,
      notApplicable: 0,
      notRated: totalSpecifications - totalRatings
    };
    
    ratingValues.forEach(rating => {
      const status = rating.complianceStatus.toLowerCase();
      if (status.includes('compliant') && !status.includes('partially') && !status.includes('non')) {
        complianceDistribution.compliant++;
      } else if (status.includes('partially')) {
        complianceDistribution.partiallyCompliant++;
      } else if (status.includes('non')) {
        complianceDistribution.nonCompliant++;
      } else if (status.includes('not applicable') || status.includes('n/a')) {
        complianceDistribution.notApplicable++;
      }
    });
    
    return {
      totalRatings,
      totalSpecifications,
      completionRate: (totalRatings / Math.max(totalSpecifications, 1)) * 100,
      averageRating,
      complianceDistribution
    };
  }

  /**
   * Group ratings by domain
   */
  private static groupRatingsByDomain(
    ratings: Record<string, RatingData>,
    framework: FrameworkData
  ): Record<string, DomainRatings> {
    const byDomain: Record<string, DomainRatings> = {};
    
    framework.domains.forEach(domain => {
      const domainRatings = domain.specifications
        .map(spec => ratings[spec.id])
        .filter(Boolean);
      
      const averageRating = domainRatings.length > 0
        ? domainRatings.reduce((sum, rating) => sum + rating.currentRating, 0) / domainRatings.length
        : 0;
      
      byDomain[domain.id] = {
        domainId: domain.id,
        domainName: domain.name,
        totalSpecifications: domain.specifications.length,
        ratedSpecifications: domainRatings.length,
        averageRating,
        ratings: domainRatings
      };
    });
    
    return byDomain;
  }

  /**
   * Group ratings by control
   */
  private static groupRatingsByControl(
    ratings: Record<string, RatingData>,
    framework: FrameworkData
  ): Record<string, ControlRatings> {
    const byControl: Record<string, ControlRatings> = {};
    
    framework.domains.forEach(domain => {
      domain.controls.forEach(control => {
        const controlRatings = control.specifications
          .map(spec => ratings[spec.id])
          .filter(Boolean);
        
        const averageRating = controlRatings.length > 0
          ? controlRatings.reduce((sum, rating) => sum + rating.currentRating, 0) / controlRatings.length
          : 0;
        
        byControl[control.id] = {
          controlId: control.id,
          controlName: control.name,
          domainId: domain.id,
          totalSpecifications: control.specifications.length,
          ratedSpecifications: controlRatings.length,
          averageRating,
          ratings: controlRatings
        };
      });
    });
    
    return byControl;
  }

  /**
   * Get a formatted summary for AI context
   */
  static formatForAI(data: ComplianceDataContext): string {
    const { assessment, framework, ratings, criteria, statistics } = data;
    
    return `
COMPLIANCE ASSESSMENT CONTEXT

Assessment Overview:
- Name: ${assessment.name.en} (${assessment.name.ar})
- Framework: ${framework.name.en} (${framework.name.ar})
- Status: ${assessment.status}
- Overall Score: ${assessment.overallScore}%
- Completion: ${assessment.completionPercentage}%

Framework Structure:
- Domains: ${statistics.framework.totalDomains}
- Controls: ${statistics.framework.totalControls}
- Specifications: ${statistics.framework.totalSpecifications}
- Sub-specifications: ${statistics.framework.totalSubSpecifications}

Rating Summary:
- Total Rated: ${statistics.ratings.totalRated}/${statistics.framework.totalSpecifications}
- Completion Rate: ${statistics.ratings.completionPercentage.toFixed(1)}%
- Average Score: ${statistics.ratings.averageScore.toFixed(1)}%
- Weighted Score: ${statistics.ratings.weightedScore.toFixed(1)}%

Compliance Distribution:
- Compliant: ${statistics.compliance.compliantCount}
- Partially Compliant: ${statistics.compliance.partiallyCompliantCount}
- Non-Compliant: ${statistics.compliance.nonCompliantCount}
- Not Applicable: ${statistics.compliance.notApplicableCount}
- Compliance Rate: ${statistics.compliance.complianceRate.toFixed(1)}%

Domain Performance:
${framework.domains.map(domain => {
  const domainStats = statistics.domains[domain.id];
  const domainRatings = ratings.byDomain[domain.id];
  return `- ${domain.name.en}: ${domainStats?.averageScore?.toFixed(1) || 0}% (${domainStats?.completionRate?.toFixed(1) || 0}% complete, weight: ${domainStats?.weight || 0}%)`;
}).join('\n')}

Assessment Criteria:
- Type: ${criteria.type}
- Levels: ${Object.keys(criteria.levels).length}
- Total Domain Weight: ${criteria.totalWeight}%

This data represents the complete state of the compliance assessment and can be used for analysis, reporting, and AI-driven insights.
    `.trim();
  }
}

// Export the service as default
export default ComplianceDataService; 
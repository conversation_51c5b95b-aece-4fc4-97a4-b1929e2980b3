import { 
    collection, 
    doc, 
    addDoc, 
    updateDoc, 
    deleteDoc, 
    getDocs, 
    getDoc, 
    query, 
    where, 
    orderBy,
    serverTimestamp,
    setDoc
} from 'firebase/firestore';
import { db } from '@/lib/firebase';

// Base asset interface
export interface BaseAsset {
    id?: string;
    name: string;
    description: string;
    projectId: string;
    createdAt?: any;
    updatedAt?: any;
    createdBy?: string;
    updatedBy?: string;
}

// System-specific interface
export interface SystemAsset extends BaseAsset {
    systemIndex: number;
    systemDomain: string;
    ownerDepartment: string;
    status: 'Active' | 'Inactive' | 'Retired' | 'Under Review';
}

// Database-specific interface
export interface DatabaseAsset extends BaseAsset {
    name: string; // Database Name
    databaseSystem?: string; // Linked system ID or null
    dbms: 'Oracle' | 'MySQL' | 'SQL Server' | 'PostgreSQL';
    technology: string; // Technology
    storageDevice: string; // Storage Device
    databaseLocation: string; // Database Location
    status: 'Online' | 'Offline' | 'Maintenance' | 'Archived';
}

export interface DatasetAsset extends BaseAsset {
    databasesUsed: string[]; // Array of database IDs
    systemsThatUseIt: string[]; // Array of system IDs
    owner: string;
    lastUpdated: any;
    size: string;
    updateFrequency: 'Real-time' | 'Daily' | 'Weekly' | 'Monthly' | 'Quarterly' | 'Annually';
    sensitivity: 'Public' | 'Internal' | 'Confidential' | 'Restricted';
    format: string;
}

export type AssetData = SystemAsset | DatabaseAsset | DatasetAsset;

class DataAssetsService {
    // Get collection path for specific asset type
    private getCollectionPath(projectId: string, assetType: string): string {
        return `projects/${projectId}/${assetType}`;
    }

    // Create a new asset
    async createAsset<T extends AssetData>(
        projectId: string,
        assetType: string,
        assetData: Omit<T, 'id' | 'createdAt' | 'updatedAt'>
    ): Promise<string> {
        try {
            const collectionPath = this.getCollectionPath(projectId, assetType);
            
            // Use system name as document ID (sanitized for Firestore)
            // Generate unique document ID if there's a collision
            const documentId = await this.generateUniqueDocumentId(projectId, assetType, assetData.name);
            
            const docRef = doc(db, collectionPath, documentId);
            await setDoc(docRef, {
                ...assetData,
                createdAt: serverTimestamp(),
                updatedAt: serverTimestamp()
            });
            
            return documentId;
        } catch (error) {
            console.error('Error creating asset:', error);
            throw error;
        }
    }

    // Sanitize string to be used as Firestore document ID
    private sanitizeDocumentId(name: string): string {
        // Remove invalid characters and replace with underscores
        // Firestore document IDs cannot contain: / \ . # $ [ ]
        return name
            .trim()
            .replace(/[\/\\\.#$\[\]]/g, '_')
            .replace(/\s+/g, '_')
            .replace(/_+/g, '_')
            .replace(/^_|_$/g, '')
            .substring(0, 1500) // Firestore has a 1500 character limit for document IDs
            || 'unnamed_asset'; // fallback if name becomes empty
    }

    // Generate a unique document ID if there's a collision
    async generateUniqueDocumentId(
        projectId: string,
        assetType: string,
        baseName: string
    ): Promise<string> {
        let documentId = this.sanitizeDocumentId(baseName);
        let counter = 1;
        
        // Check if document exists, if so, append counter
        while (await this.getAsset(projectId, assetType, documentId)) {
            const baseId = this.sanitizeDocumentId(baseName);
            documentId = `${baseId}_${counter}`;
            counter++;
            
            // Safety check to prevent infinite loop
            if (counter > 1000) {
                documentId = `${baseId}_${Date.now()}`;
                break;
            }
        }
        
        return documentId;
    }

    // Get all assets of a specific type for a project
    async getAssets<T extends AssetData>(
        projectId: string,
        assetType: string
    ): Promise<T[]> {
        try {
            const collectionPath = this.getCollectionPath(projectId, assetType);
            const q = query(
                collection(db, collectionPath),
                orderBy('createdAt', 'desc')
            );
            const querySnapshot = await getDocs(q);
            
            return querySnapshot.docs.map(doc => ({
                id: doc.id,
                ...doc.data()
            })) as T[];
        } catch (error) {
            console.error('Error fetching assets:', error);
            throw error;
        }
    }

    // Get a specific asset by ID
    async getAsset<T extends AssetData>(
        projectId: string,
        assetType: string,
        assetId: string
    ): Promise<T | null> {
        try {
            const collectionPath = this.getCollectionPath(projectId, assetType);
            const docRef = doc(db, collectionPath, assetId);
            const docSnap = await getDoc(docRef);
            
            if (docSnap.exists()) {
                return {
                    id: docSnap.id,
                    ...docSnap.data()
                } as T;
            }
            return null;
        } catch (error) {
            console.error('Error fetching asset:', error);
            throw error;
        }
    }

    // Update an asset
    async updateAsset<T extends AssetData>(
        projectId: string,
        assetType: string,
        assetId: string,
        updates: Partial<Omit<T, 'id' | 'createdAt' | 'updatedAt'>>
    ): Promise<void> {
        try {
            const collectionPath = this.getCollectionPath(projectId, assetType);
            
            // Check if name is being updated (which requires document ID change)
            if (updates.name && updates.name.trim() !== '') {
                const newDocumentId = this.sanitizeDocumentId(updates.name);
                
                // If the new document ID is different from current, we need to create new and delete old
                if (newDocumentId !== assetId) {
                    // Get the current document data
                    const currentAsset = await this.getAsset<T>(projectId, assetType, assetId);
                    if (!currentAsset) {
                        throw new Error('Asset not found');
                    }
                    
                    // Check if new document ID already exists
                    const existingDoc = await this.getAsset(projectId, assetType, newDocumentId);
                    if (existingDoc) {
                        throw new Error(`Asset with name "${updates.name}" already exists`);
                    }
                    
                    // Create new document with updated data
                    const newDocRef = doc(db, collectionPath, newDocumentId);
                    await setDoc(newDocRef, {
                        ...currentAsset,
                        ...updates,
                        updatedAt: serverTimestamp()
                    });
                    
                    // Delete old document
                    const oldDocRef = doc(db, collectionPath, assetId);
                    await deleteDoc(oldDocRef);
                    
                    return;
                }
            }
            
            // Normal update (name not changed or same sanitized ID)
            const docRef = doc(db, collectionPath, assetId);
            await updateDoc(docRef, {
                ...updates,
                updatedAt: serverTimestamp()
            });
        } catch (error) {
            console.error('Error updating asset:', error);
            throw error;
        }
    }

    // Delete an asset
    async deleteAsset(
        projectId: string,
        assetType: string,
        assetId: string
    ): Promise<void> {
        try {
            const collectionPath = this.getCollectionPath(projectId, assetType);
            const docRef = doc(db, collectionPath, assetId);
            await deleteDoc(docRef);
        } catch (error) {
            console.error('Error deleting asset:', error);
            throw error;
        }
    }

    // Get next system index for auto-generation
    async getNextSystemIndex(projectId: string): Promise<number> {
        try {
            const systems = await this.getAssets<SystemAsset>(projectId, 'systems');
            if (systems.length === 0) return 1;
            
            const maxIndex = Math.max(...systems.map(system => system.systemIndex || 0));
            return maxIndex + 1;
        } catch (error) {
            console.error('Error getting next system index:', error);
            return 1;
        }
    }
}

export const dataAssetsService = new DataAssetsService(); 
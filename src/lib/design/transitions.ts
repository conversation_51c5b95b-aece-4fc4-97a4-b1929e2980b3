/**
 * Transition Presets
 * 
 * Common transition configurations for animations
 */

// CSS transitions as strings
export const CSS_TRANSITIONS = {
    fast: 'all 0.15s ease',
    default: 'all 0.2s ease',
    slow: 'all 0.3s ease',
    veryFast: 'all 0.1s ease',
    verySlow: 'all 0.5s ease',
    // Component-specific transitions from button.tsx
    button: 'transition-all duration-200',
    buttonHover: 'transition-all duration-200 hover:shadow-md',
    buttonWithScale: 'transition-all duration-200 hover:scale-105 active:scale-95',
    // Custom transitions
    backgroundOnly: 'background-color 0.2s ease',
    transformOnly: 'transform 0.2s ease',
    opacityOnly: 'opacity 0.2s ease',
};

// Framer Motion transition objects
export const FRAMER_TRANSITIONS = {
    default: {
        type: 'tween',
        duration: 0.2,
        ease: 'easeOut',
    },
    easeIn: {
        type: 'tween',
        duration: 0.2,
        ease: 'easeIn',
    },
    easeOut: {
        type: 'tween',
        duration: 0.2,
        ease: 'easeOut',
    },
    easeInOut: {
        type: 'tween',
        duration: 0.3,
        ease: 'easeInOut',
    },
    spring: {
        type: 'spring',
        stiffness: 400,
        damping: 30,
    },
    springSoft: {
        type: 'spring',
        stiffness: 200,
        damping: 25,
    },
    springGentle: {
        type: 'spring',
        stiffness: 100,
        damping: 20,
    },
    stagger: {
        staggerChildren: 0.07,
        delayChildren: 0.2,
    },
    staggerFast: {
        staggerChildren: 0.05,
        delayChildren: 0.1,
    },
    staggerSlow: {
        staggerChildren: 0.1,
        delayChildren: 0.3,
    },
}; 
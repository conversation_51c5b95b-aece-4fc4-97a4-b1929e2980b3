/**
 * Animation Keyframes
 * 
 * This file contains keyframes definitions used in the design system
 * Includes CSS keyframes for Tailwind and Framer Motion keyframe objects
 */

// CSS keyframes for use with Tailwind animations
export const CSS_KEYFRAMES = {
    // Accordion keyframes from tailwind.config.ts
    accordionDown: {
        from: {
            height: '0',
        },
        to: {
            height: 'var(--radix-accordion-content-height)',
        },
    },
    accordionUp: {
        from: {
            height: 'var(--radix-accordion-content-height)',
        },
        to: {
            height: '0',
        },
    },
    // Common animations
    fadeIn: {
        from: {
            opacity: '0',
        },
        to: {
            opacity: '1',
        },
    },
    fadeOut: {
        from: {
            opacity: '1',
        },
        to: {
            opacity: '0',
        },
    },
    slideInFromTop: {
        from: {
            opacity: '0',
            transform: 'translateY(-10px)',
        },
        to: {
            opacity: '1',
            transform: 'translateY(0)',
        },
    },
    slideInFromBottom: {
        from: {
            opacity: '0',
            transform: 'translateY(10px)',
        },
        to: {
            opacity: '1',
            transform: 'translateY(0)',
        },
    },
    slideInFromLeft: {
        from: {
            opacity: '0',
            transform: 'translateX(-10px)',
        },
        to: {
            opacity: '1',
            transform: 'translateX(0)',
        },
    },
    slideInFromRight: {
        from: {
            opacity: '0',
            transform: 'translateX(10px)',
        },
        to: {
            opacity: '1',
            transform: 'translateX(0)',
        },
    },
    pulse: {
        '0%, 100%': {
            opacity: '1',
        },
        '50%': {
            opacity: '0.5',
        },
    },
    spin: {
        to: {
            transform: 'rotate(360deg)',
        },
    },
    ping: {
        '75%, 100%': {
            transform: 'scale(2)',
            opacity: '0',
        },
    },
    bounce: {
        '0%, 100%': {
            transform: 'translateY(-25%)',
            animationTimingFunction: 'cubic-bezier(0.8, 0, 1, 1)',
        },
        '50%': {
            transform: 'translateY(0)',
            animationTimingFunction: 'cubic-bezier(0, 0, 0.2, 1)',
        },
    },
    scale: {
        '0%, 100%': {
            transform: 'scale(1)',
        },
        '50%': {
            transform: 'scale(1.05)',
        },
    },
}; 
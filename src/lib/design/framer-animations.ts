/**
 * Framer Motion Animations
 * 
 * This file contains animation configurations for Framer Motion
 */

// Framer Motion keyframes for direct use with motion components
export const FRAMER_KEYFRAMES = {
    float: {
        y: [0, -10, 0],
        transition: {
            duration: 3,
            repeat: Infinity,
            repeatType: "reverse",
            ease: "easeInOut",
        },
    },
    pulse: {
        scale: [1, 1.05, 1],
        opacity: [0.7, 1, 0.7],
        transition: {
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut",
        },
    },
    orb: {
        scale: [1, 1.2, 1],
        x: [0, 100, 0],
        y: [0, 50, 0],
        transition: {
            duration: 20,
            repeat: Infinity,
            repeatType: "reverse",
        },
    },
    rotate: {
        rotate: 360,
        transition: {
            duration: 8,
            repeat: Infinity,
            ease: "linear",
        },
    },
    // Button animations
    buttonHover: {
        scale: 1.05,
    },
    buttonTap: {
        scale: 0.95,
    },
};

/**
 * Animation Variants for Framer Motion
 * 
 * These variants can be used directly with Framer Motion components
 */

export const FADE_IN = {
    hidden: { opacity: 0 },
    visible: {
        opacity: 1,
        transition: {
            duration: 0.5
        }
    },
    exit: {
        opacity: 0,
        transition: {
            duration: 0.3
        }
    }
};

export const SLIDE_UP = {
    hidden: { opacity: 0, y: 20 },
    visible: {
        opacity: 1,
        y: 0,
        transition: {
            duration: 0.5,
            ease: "easeOut"
        }
    },
    exit: {
        opacity: 0,
        y: -20,
        transition: {
            duration: 0.3,
            ease: "easeIn"
        }
    }
};

export const SLIDE_DOWN = {
    hidden: { opacity: 0, y: -20 },
    visible: {
        opacity: 1,
        y: 0,
        transition: {
            duration: 0.5,
            ease: "easeOut"
        }
    },
    exit: {
        opacity: 0,
        y: 20,
        transition: {
            duration: 0.3,
            ease: "easeIn"
        }
    }
};

export const SLIDE_LEFT = {
    hidden: { opacity: 0, x: 20 },
    visible: {
        opacity: 1,
        x: 0,
        transition: {
            duration: 0.5,
            ease: "easeOut"
        }
    },
    exit: {
        opacity: 0,
        x: -20,
        transition: {
            duration: 0.3,
            ease: "easeIn"
        }
    }
};

export const SLIDE_RIGHT = {
    hidden: { opacity: 0, x: -20 },
    visible: {
        opacity: 1,
        x: 0,
        transition: {
            duration: 0.5,
            ease: "easeOut"
        }
    },
    exit: {
        opacity: 0,
        x: 20,
        transition: {
            duration: 0.3,
            ease: "easeIn"
        }
    }
};

export const SCALE_UP = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: {
        opacity: 1,
        scale: 1,
        transition: {
            duration: 0.5,
            ease: "easeOut"
        }
    },
    exit: {
        opacity: 0,
        scale: 0.8,
        transition: {
            duration: 0.3,
            ease: "easeIn"
        }
    }
};

// For container with multiple children that stagger their animation
export const STAGGER_CONTAINER = {
    hidden: { opacity: 0 },
    visible: {
        opacity: 1,
        transition: {
            staggerChildren: 0.1,
            delayChildren: 0.2
        }
    },
    exit: {
        opacity: 0,
        transition: {
            staggerChildren: 0.05,
            staggerDirection: -1
        }
    }
};

// For card elements in a grid
export const CARD_VARIANTS = {
    hidden: { opacity: 0, y: 20 },
    visible: {
        opacity: 1,
        y: 0,
        transition: {
            duration: 0.5
        }
    },
    hover: {
        scale: 1.02,
        transition: {
            duration: 0.2
        }
    }
};

// Hero section decorative elements
export const HERO_ORB_VARIANTS = {
    topRight: {
        initial: { x: 100, y: -100 },
        animate: {
            x: 0,
            y: 0,
            scale: [1, 1.2, 1],
            rotate: [0, 45, 0],
            transition: {
                duration: 20,
                repeat: Infinity,
                repeatType: "reverse"
            }
        }
    },
    bottomLeft: {
        initial: { y: 50 },
        animate: {
            y: [0, 20, 0],
            scale: [1, 1.1, 1],
            transition: {
                duration: 8,
                repeat: Infinity,
                repeatType: "reverse"
            }
        }
    },
    middle: {
        initial: { opacity: 0.7 },
        animate: {
            scale: [1, 1.1, 1],
            opacity: [0.7, 0.9, 0.7],
            transition: {
                duration: 12,
                repeat: Infinity,
                repeatType: "reverse"
            }
        }
    }
}; 
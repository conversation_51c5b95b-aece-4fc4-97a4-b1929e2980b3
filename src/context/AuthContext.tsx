'use client';

import { createContext, useContext, ReactNode, useState, useEffect, useCallback } from 'react';
import { onAuthStateChanged, signOut } from 'firebase/auth';
import { useRouter, usePathname } from 'next/navigation';
import { useLocale } from 'next-intl';
import { auth } from '@/lib/firebaseClient';
import { signIn, SignInResult } from '@/lib/auth/signIn';
import { getUserSession, UserSession } from '@/lib/auth/getUserSession';

interface AuthContextType {
    user: UserSession | null;
    login: (email: string, password: string) => Promise<SignInResult>;
    logout: () => Promise<void>;
    error: string | null;
    isLoading: boolean;
    isInitialized: boolean;
}

// Create the context with a default value
const AuthContext = createContext<AuthContextType>({
    user: null,
    login: async () => ({ success: false }),
    logout: async () => { },
    error: null,
    isLoading: false,
    isInitialized: false
});

// Provider component
export function AuthProvider({ children }: { children: ReactNode }) {
    const [user, setUser] = useState<UserSession | null>(null);
    const [error, setError] = useState<string | null>(null);
    const [isLoading, setIsLoading] = useState(false);
    const [isInitialized, setIsInitialized] = useState(false);
    const router = useRouter();
    const pathname = usePathname();
    const locale = useLocale();

    // Function to redirect based on role - wrapped in useCallback
    const redirectBasedOnRole = useCallback((userSession: UserSession | null) => {
        if (!userSession) return;

        try {
            // Check if this is just a locale change in the URL (we don't want to redirect in that case)
            const pathSegments = pathname.split('/');
            // Skip redirection if only the locale segment changed
            const isLocaleChangeOnly = ['en', 'ar'].includes(pathSegments[1]) &&
                (pathSegments.includes('dashboard') || pathSegments.includes('project-selection'));

            if (isLocaleChangeOnly) return;

            // Get the current path components
            const isOnLoginPage = pathname.includes('/login');
            const isOnDashboardPath = pathname.includes('/dashboard');
            const isOnProjectSelectionPath = pathname.includes('/project-selection');
            const isOnUnauthorized = pathname.includes('/unauthorized');
            const isOnRoot = pathname === '/' || pathname === `/${locale}`;
            const isOnClientPath = pathname.includes('/client');
            const isOnConsultantPath = pathname.includes('/consultant');

            // If user is on login page or root, redirect to project selection
            if (isOnLoginPage || isOnRoot) {
                // Instead of using router.push, use window.location to avoid navigation issues
                window.location.href = `/${locale}/project-selection`;
                return;
            }
            // If user is in the old client/consultant paths, redirect to project selection
            else if ((isOnClientPath || isOnConsultantPath) && !isOnDashboardPath && !isOnUnauthorized && !isOnProjectSelectionPath) {
                window.location.href = `/${locale}/project-selection`;
                return;
            }
        } catch (err) {
            console.error('Redirect error:', err);
            // Fall back to simpler navigation if there's an error
            if (pathname.includes('/login') || pathname === '/' || pathname === `/${locale}`) {
                window.location.href = `/${locale}/project-selection`;
            }
        }
    }, [pathname, locale]);

    // Initialize auth state listener
    useEffect(() => {
        let authStateInitialized = false;
        let retryCount = 0;
        const maxRetries = 3;

        const initializeAuthState = async () => {
            const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
                if (!authStateInitialized) {
                    authStateInitialized = true;
                    setIsLoading(true);
                }

                try {
                    if (firebaseUser) {
                        // Get user session from Firestore
                        const userSession = await getUserSession(firebaseUser);

                        if (userSession) {
                            // If user has valid session (active and with correct role)
                            setUser(userSession);
                            redirectBasedOnRole(userSession);
                        } else {
                            // Invalid session/role/status, sign out and redirect to unauthorized
                            await signOut(auth);
                            setUser(null);

                            // Only redirect to unauthorized if not already there and not on login
                            if (!pathname.includes('/unauthorized') && !pathname.includes('/login')) {
                                router.push(`/${locale}/unauthorized`);
                            } else if (pathname.includes('/login')) {
                                // Stay on login page with no redirect
                            }
                        }
                    } else {
                        // No user is signed in
                        setUser(null);

                        // Redirect to login if trying to access protected pages
                        if (!pathname.includes('/login') &&
                            !pathname.includes('/unauthorized') &&
                            (pathname.includes('/client') ||
                                pathname.includes('/consultant') ||
                                pathname.includes('/dashboard') ||
                                pathname === '/' ||
                                pathname === `/${locale}`)) {
                            router.push(`/${locale}/login`);
                        }
                    }
                } catch (err) {
                    console.error('Auth state change error:', err);
                    setUser(null);

                    // If we're still under max retries, try again after a delay
                    if (retryCount < maxRetries) {
                        retryCount++;
                        console.log(`Retrying auth state initialization, attempt ${retryCount} of ${maxRetries}`);

                        // Unsubscribe from current listener before retrying
                        unsubscribe();

                        // Wait before retrying
                        setTimeout(() => {
                            initializeAuthState();
                        }, 1000); // 1 second delay between retries
                    }
                } finally {
                    if (authStateInitialized) {
                        setIsLoading(false);
                        setIsInitialized(true);
                    }
                }
            });

            return unsubscribe;
        };

        const unsubscribePromise = initializeAuthState();

        return () => {
            // Clean up the auth state listener
            unsubscribePromise.then(unsubscribe => unsubscribe());
        };
    }, [locale, pathname, router, redirectBasedOnRole]);

    // Login function
    const login = async (email: string, password: string): Promise<SignInResult> => {
        setIsLoading(true);
        setError(null);

        try {
            const result = await signIn(email, password);

            if (!result.success) {
                setError(result.errorMessage || 'Unknown error');
                return result;
            }

            // After successful login, use direct navigation to avoid Firestore connection issues
            if (pathname.includes('/login')) {
                // Mark login as successful immediately to prevent UI errors
                // Short delay to allow Firebase auth to complete
                setTimeout(() => {
                    window.location.href = `/${locale}/project-selection`;
                }, 500);
            }

            return result;
        } catch (err) {
            const error = err as Error;
            console.error('Login function error:', error);

            // Check if it's likely a network or Firestore connection issue
            const isNetworkError = error.message.includes('network') ||
                error.message.includes('connection') ||
                error.message.includes('timeout');

            if (isNetworkError) {
                // For network errors, we'll still try to proceed if authentication was successful
                // The UI will handle showing appropriate temporary error messages
                return {
                    success: false,
                    error: 'auth/network-request-failed',
                    errorMessage: 'Network connection issue'
                };
            }

            setError(error.message);
            return {
                success: false,
                error: 'auth/unknown-error',
                errorMessage: error.message
            };
        } finally {
            setIsLoading(false);
        }
    };

    // Logout function
    const logout = async () => {
        setIsLoading(true);
        try {
            await signOut(auth);
            setUser(null);
            router.push(`/${locale}/login`);
        } catch (err) {
            console.error('Logout error:', err);
        } finally {
            setIsLoading(false);
        }
    };

    const value = {
        user,
        login,
        logout,
        error,
        isLoading,
        isInitialized
    };

    return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

// Custom hook to use the auth context
export const useAuthContext = () => {
    const context = useContext(AuthContext);
    if (context === undefined) {
        throw new Error('useAuthContext must be used within an AuthProvider');
    }
    return context;
}; 
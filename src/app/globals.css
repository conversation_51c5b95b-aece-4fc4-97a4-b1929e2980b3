@import "tailwindcss";

@plugin "tailwindcss-animate";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

/* Font definitions */
.font-rubik {
  font-family: var(--font-rubik), system-ui, sans-serif;
}

.font-cairo {
  font-family: var(--font-cairo), system-ui, sans-serif;
}

:root {
  --radius: 0.5rem;
  
  /* Light theme variables using our color system */
  --background: 0 0% 100%;
  --foreground: 240 10% 3.9%;
  --card: 0 0% 100%;
  --card-foreground: 240 10% 3.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 240 10% 3.9%;
  --primary: 240 30% 31%;
  --primary-foreground: 0 0% 100%;
  --secondary: 242 33% 29%;
  --secondary-foreground: 0 0% 100%;
  --muted: 240 4.8% 95.9%;
  --muted-foreground: 240 3.8% 46.1%;
  --accent: 240 4.8% 95.9%;
  --accent-foreground: 240 30% 31%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 0 0% 100%;
  --border: 240 5.9% 90%;
  --input: 240 5.9% 90%;
  --ring: 240 30% 31%;
  
  /* Chart colors */
  --chart-1: 210 100% 23%;
  --chart-2: 162 61% 55%;
  --chart-3: 172 100% 19%;
  --chart-4: 101 63% 55%;
  --chart-5: 153 62% 43%;
  
  /* Sidebar colors */
  --sidebar: 0 0% 100%;
  --sidebar-foreground: 240 10% 3.9%;
  --sidebar-primary: 240 30% 31%;
  --sidebar-primary-foreground: 0 0% 100%;
  --sidebar-accent: 240 4.8% 95.9%;
  --sidebar-accent-foreground: 240 30% 31%;
  --sidebar-border: 240 5.9% 90%;
  --sidebar-ring: 240 30% 31%;
}

.dark {
  --background: 240 10% 3.9%;
  --foreground: 0 0% 98%;
  --card: 240 10% 3.9%;
  --card-foreground: 0 0% 98%;
  --popover: 240 10% 3.9%;
  --popover-foreground: 0 0% 98%;
  --primary: 242 33% 29%;
  --primary-foreground: 0 0% 98%;
  --secondary: 240 3.7% 15.9%;
  --secondary-foreground: 0 0% 98%;
  --muted: 240 3.7% 15.9%;
  --muted-foreground: 240 5% 64.9%;
  --accent: 240 3.7% 15.9%;
  --accent-foreground: 0 0% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 0% 98%;
  --border: 240 3.7% 15.9%;
  --input: 240 3.7% 15.9%;
  --ring: 240 30% 31%;
  
  /* Chart colors for dark mode */
  --chart-1: 210 100% 33%;
  --chart-2: 162 61% 65%;
  --chart-3: 172 100% 29%;
  --chart-4: 101 63% 65%;
  --chart-5: 153 62% 53%;
  
  /* Sidebar colors for dark mode */
  --sidebar: 240 10% 3.9%;
  --sidebar-foreground: 0 0% 98%;
  --sidebar-primary: 240 30% 31%;
  --sidebar-primary-foreground: 0 0% 98%;
  --sidebar-accent: 240 3.7% 15.9%;
  --sidebar-accent-foreground: 0 0% 98%;
  --sidebar-border: 240 3.7% 15.9%;
  --sidebar-ring: 240 30% 31%;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground font-sans;
  }
}

.font-arabic {
  font-family: 'Your Arabic Font', sans-serif;
}

[dir='rtl'] {
  direction: rtl;
  unicode-bidi: embed;
}

[dir='rtl'] .rtl-override {
  text-align: right;
}

/* Enhanced Assessment Card Styles */
.perspective-1000 {
  perspective: 1000px;
}

.transform-gpu {
  transform: translateZ(0);
}

.shadow-3xl {
  box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25);
}

.shadow-4xl {
  box-shadow: 0 45px 80px -15px rgba(0, 0, 0, 0.3);
}

/* Custom gradient border animation */
@keyframes gradient-border {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.animate-gradient-border {
  background: linear-gradient(-45deg, #003874, #2D8DC6, #48D3A5, #003874);
  background-size: 400% 400%;
  animation: gradient-border 3s ease infinite;
}

/* Enhanced hover effects */
.card-hover-glow {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover-glow:hover {
  filter: drop-shadow(0 20px 40px rgba(0, 56, 116, 0.15));
}

/* Floating animation for particles */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* Pulse glow effect */
@keyframes pulse-glow {
  0%, 100% {
    opacity: 0.4;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Enhanced card shimmer effect */
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.animate-shimmer {
  animation: shimmer 2s ease-in-out infinite;
}

/* Breathing glow effect */
@keyframes breathe {
  0%, 100% {
    box-shadow: 0 0 20px rgba(0, 56, 116, 0.1);
  }
  50% {
    box-shadow: 0 0 40px rgba(0, 56, 116, 0.2), 0 0 60px rgba(45, 141, 198, 0.1);
  }
}

.animate-breathe {
  animation: breathe 3s ease-in-out infinite;
}

/* Gradient text animation */
@keyframes gradient-text {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.animate-gradient-text {
  background: linear-gradient(-45deg, #003874, #2D8DC6, #48D3A5, #003874);
  background-size: 400% 400%;
  animation: gradient-text 3s ease infinite;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
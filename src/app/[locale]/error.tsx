"use client";

import { ServerCrash } from "lucide-react";
import { useEffect } from "react";
import { Button } from "@/components/ui/button";
import { NextIntlClientProvider } from "next-intl";
import { useRouter } from "next/navigation";

export default function LocaleError({
    error,
    reset,
    params = { locale: "en" }
}: {
    error: Error & { digest?: string };
    reset: () => void;
    params?: { locale: string };
}) {
    const locale = params.locale || "en";
    const router = useRouter();

    useEffect(() => {
        // Log the error to an error reporting service
        console.error(error);
    }, [error]);

    // Simple translations object
    const messages = {
        en: {
            error: {
                title: "Something went wrong!",
                sorry: "Sorry, an unexpected error occurred.",
                tryAgain: "Try again",
                returnHome: "Return home"
            }
        },
        ar: {
            error: {
                title: "حدث خطأ ما!",
                sorry: "عذرًا، حدث خطأ غير متوقع.",
                tryAgain: "حاول مرة أخرى",
                returnHome: "العودة للرئيسية"
            }
        }
    };

    // Use the messages for the current locale or fallback to English
    const localeMessages = messages[locale as keyof typeof messages] || messages.en;

    return (
        <NextIntlClientProvider locale={locale} messages={localeMessages}>
            <div className="flex flex-col items-center justify-center min-h-[70vh] px-4 text-center">
                <div className="flex items-center justify-center w-20 h-20 mb-6 rounded-full bg-red-100">
                    <ServerCrash className="w-10 h-10 text-red-600" />
                </div>
                <h1 className="mb-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
                    {localeMessages.error.title}
                </h1>
                <p className="mb-8 text-lg text-gray-600">{localeMessages.error.sorry}</p>
                <div className="flex flex-col gap-4 sm:flex-row">
                    <Button onClick={() => reset()} variant="default">
                        {localeMessages.error.tryAgain}
                    </Button>
                    <Button
                        variant="outline"
                        onClick={() => router.push(`/${locale}`)}
                    >
                        {localeMessages.error.returnHome}
                    </Button>
                </div>
            </div>
        </NextIntlClientProvider>
    );
} 
'use client';

import { useState, useEffect, useRef } from 'react';
import { useTranslations, useLocale } from 'next-intl';
import { motion, AnimatePresence } from 'framer-motion';
import { Loader2, Mail, Lock, AlertCircle } from 'lucide-react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';

import { useAuthContext } from '@/context/AuthContext';
import { LanguageSwitcher } from '@/components/shared/language-switcher';
import { toast } from '@/components/ui/use-toast';

export default function LoginPage() {
    const t = useTranslations('Auth');
    const locale = useLocale();
    const router = useRouter();
    const isRtl = locale === 'ar';
    const fontFamily = isRtl ? 'var(--font-cairo)' : 'var(--font-rubik)';
    const { login, user, isInitialized } = useAuthContext();

    // State for login form
    const [buttonLoading, setButtonLoading] = useState(false);
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [formError, setFormError] = useState<string | null>(null);
    const [formState, setFormState] = useState<"idle" | "submitting" | "success" | "error">("idle");

    // Ref to track if login is successful to prevent showing temporary errors after success
    const loginSuccessRef = useRef(false);
    // Timer for error display
    const errorTimerRef = useRef<NodeJS.Timeout | null>(null);

    // Redirect if already logged in
    useEffect(() => {
        if (isInitialized && user) {
            router.push(`/${locale}/project-selection`);
        }
    }, [user, isInitialized, router, locale]);

    // Clear error timer on unmount
    useEffect(() => {
        return () => {
            if (errorTimerRef.current) {
                clearTimeout(errorTimerRef.current);
            }
        };
    }, []);

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        // Validate form fields
        if (!email) {
            setFormError(t('validation.emailRequired'));
            return;
        }

        if (!password) {
            setFormError(t('validation.passwordRequired'));
            return;
        }

        // Reset success reference
        loginSuccessRef.current = false;

        // Clear any existing error timers
        if (errorTimerRef.current) {
            clearTimeout(errorTimerRef.current);
            errorTimerRef.current = null;
        }

        // Show loading state
        setButtonLoading(true);
        setFormState("submitting");
        setFormError(null);

        try {
            // Attempt login
            const result = await login(email, password);

            if (!result.success) {
                // Check if the error is a temporary issue
                const isTemporaryError = result.isTemporaryError || result.error === 'auth/network-request-failed' || result.error === 'auth/firestore-error';

                if (isTemporaryError) {
                    // For temporary errors, set a timer before showing the error message
                    // This prevents brief flashes of error messages for temporary issues
                    errorTimerRef.current = setTimeout(() => {
                        // Only show the error if login hasn't succeeded in the meantime
                        if (!loginSuccessRef.current) {
                            // For network or Firestore errors, check if we should still redirect
                            // If user was authenticated but we just couldn't get their data, we should redirect
                            if (result.error === 'auth/firestore-error') {
                                loginSuccessRef.current = true;
                                setFormState("success");

                                // Redirect despite the temporary error
                                setTimeout(() => {
                                    window.location.href = `/${locale}/project-selection`;
                                }, 500);
                            } else {
                                setFormError(t('networkError'));
                                setFormState("error");
                            }
                        }
                    }, 1500); // Wait 1.5 seconds before showing temporary errors
                } else {
                    // Display appropriate error message for permanent errors immediately
                    let errorMessage = t('serverError');

                    switch (result.error) {
                        case 'auth/invalid-credential':
                        case 'auth/wrong-password':
                        case 'auth/user-not-found':
                            errorMessage = t('invalidCredentials');
                            break;
                        case 'auth/user-inactive':
                        case 'auth/user-disabled':
                            errorMessage = t('accountInactive');
                            break;
                        case 'auth/too-many-requests':
                            errorMessage = t('tooManyAttempts');
                            break;
                        case 'auth/user-not-found':
                            errorMessage = t('userNotInDatabase');
                            break;
                        case 'auth/role-invalid':
                            errorMessage = t('invalidRole');
                            toast({
                                variant: "destructive",
                                title: t('accessDenied'),
                                description: t('invalidRoleForPortal')
                            });
                            break;
                    }

                    setFormError(errorMessage);
                    setFormState("error");
                }
            } else {
                // Mark login as successful to prevent temporary errors from showing
                loginSuccessRef.current = true;
                setFormState("success");

                // Clear any error timer that might be pending
                if (errorTimerRef.current) {
                    clearTimeout(errorTimerRef.current);
                    errorTimerRef.current = null;
                }

                // For successful login, use direct browser navigation
                setTimeout(() => {
                    window.location.href = `/${locale}/project-selection`;
                }, 500);
            }
        } catch (error) {
            console.error('Login error:', error);

            // Only show server error if login hasn't been successful
            if (!loginSuccessRef.current) {
                // Wait a moment before showing server error to avoid flashing for temporary issues
                errorTimerRef.current = setTimeout(() => {
                    if (!loginSuccessRef.current) {
                        setFormError(t('serverError'));
                        setFormState("error");
                    }
                }, 1500);
            }
        } finally {
            setButtonLoading(false);
        }
    };

    // Animation variants
    const _fadeIn = {
        hidden: { opacity: 0, y: 20 },
        visible: {
            opacity: 1,
            y: 0,
            transition: {
                duration: 0.6,
                ease: "easeOut"
            }
        },
        exit: {
            opacity: 0,
            y: -20,
            transition: {
                duration: 0.3
            }
        }
    };

    const slideIn = {
        hidden: { x: -60, opacity: 0 },
        visible: {
            x: 0,
            opacity: 1,
            transition: {
                duration: 0.8,
                ease: "easeOut"
            }
        }
    };

    const formControls = {
        hidden: { opacity: 0 },
        visible: (custom: number) => ({
            opacity: 1,
            transition: {
                delay: custom * 0.1 + 0.3,
                duration: 0.6
            }
        })
    };

    const _buttonVariants = {
        idle: {},
        hover: { boxShadow: "0 5px 15px rgba(0, 0, 0, 0.1)" },
        tap: { boxShadow: "0 2px 5px rgba(0, 0, 0, 0.1)" },
        loading: {
            boxShadow: "0 0 0 rgba(0, 0, 0, 0)"
        }
    };

    const _loadingSpinner = {
        animate: {}
    };

    return (
        <div
            className="flex min-h-screen bg-white"
            dir={isRtl ? 'rtl' : 'ltr'}
            style={{ fontFamily }}
        >
            {/* Language switcher in top-right corner */}
            <div className={`absolute top-4 ${isRtl ? 'left-4' : 'right-4'} z-20`}>
                <LanguageSwitcher className="text-brand-primary hover:bg-white/20" />
            </div>

            {/* Left side image (70%) */}
            <motion.div
                className="hidden lg:block w-[70%] relative bg-[#45cab0] overflow-hidden"
                initial="hidden"
                animate="visible"
                variants={slideIn}
            >
                <Image
                    src="/assets/SideIMage.png"
                    alt="DevFlow AI Planning"
                    fill
                    style={{ objectFit: "cover" }}
                    priority
                    className={`${isRtl ? 'rounded-l-xl' : 'rounded-r-xl'}`}
                />
                <div className={`absolute inset-0 bg-[#45cab0]/30 ${isRtl ? 'rounded-l-xl' : 'rounded-r-xl'} mix-blend-multiply`} />
                <motion.div
                    className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-[#45cab0]/70"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.5, duration: 1 }}
                />
                <div className={`absolute bottom-10 ${isRtl ? 'right-10' : 'left-10'} text-white`}>
                    <motion.div
                        className="text-3xl font-bold mb-4"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.5, duration: 0.8 }}
                    >
                        {isRtl ? 'تدفق المطور الذكي' : 'Smart Developer Flow'}
                    </motion.div>
                    <motion.div
                        className="text-xl max-w-md"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.8, duration: 0.8 }}
                    >
                        {isRtl
                            ? 'منصة متكاملة تستخدم الذكاء الاصطناعي لتبسيط وتسريع عمليات التطوير وإدارة المشاريع.'
                            : 'An integrated platform using AI to streamline and accelerate development processes and project management.'}
                    </motion.div>
                </div>
            </motion.div>

            {/* Right side form (30%) */}
            <motion.div
                className="w-full lg:w-[35%] flex items-center justify-center px-6 md:px-10"
                initial={{ opacity: 1 }}
                animate={{ opacity: 1 }}
            >
                <div className="w-full max-w-md">
                    <motion.div
                        className="flex justify-center"
                        variants={formControls}
                        custom={0}
                        initial="hidden"
                        animate="visible"
                    >
                        <Image
                            src="/assets/DevFlowLogo.png"
                            alt="DevFlow Logo"
                            width={180}
                            height={64}
                            priority
                            className="h-40 w-auto object-contain"
                        />
                    </motion.div>

                    <motion.h2
                        className="text-3xl font-bold text-gray-800 mb-8 text-center"
                        variants={formControls}
                        custom={1}
                        initial="hidden"
                        animate="visible"
                    >
                        {t('loginTitle')}
                    </motion.h2>

                    <AnimatePresence>
                        {formError && formState !== "success" && (
                            <motion.div
                                className="mb-6 p-4 bg-red-100 text-red-700 rounded-lg text-sm flex items-center gap-2"
                                initial={{ opacity: 0, y: -10, height: 0 }}
                                animate={{ opacity: 1, y: 0, height: 'auto' }}
                                exit={{ opacity: 0, y: -10, height: 0 }}
                                transition={{ duration: 0.3 }}
                            >
                                <AlertCircle className="h-5 w-5 flex-shrink-0" />
                                <span>{formError}</span>
                            </motion.div>
                        )}
                    </AnimatePresence>

                    <motion.form
                        onSubmit={handleSubmit}
                        className="space-y-6"
                    >
                        <motion.div
                            variants={formControls}
                            custom={2}
                            initial="hidden"
                            animate="visible"
                            className="overflow-hidden"
                        >
                            <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                                {t('email')}
                            </label>
                            <motion.div
                                whileHover={{ scale: 1.01 }}
                                whileTap={{ scale: 0.99 }}
                                className="relative"
                            >
                                <Mail className={`absolute ${isRtl ? 'right-3' : 'left-3'} top-2.5 h-5 w-5 text-gray-400`} />
                                <input
                                    id="email"
                                    name="email"
                                    type="email"
                                    autoComplete="email"
                                    value={email}
                                    onChange={(e) => setEmail(e.target.value)}
                                    className={`block w-full rounded-lg border-gray-300 bg-gray-50 py-2 ${isRtl ? 'pr-10' : 'pl-10'} shadow-sm focus:border-primary-500 focus:ring-primary-500 text-gray-900`}
                                    placeholder={isRtl ? "البريد الإلكتروني" : "Email address"}
                                    required
                                />
                            </motion.div>
                        </motion.div>

                        <motion.div
                            variants={formControls}
                            custom={3}
                            initial="hidden"
                            animate="visible"
                            className="overflow-hidden"
                        >
                            <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                                {t('password')}
                            </label>
                            <motion.div
                                whileHover={{ scale: 1.01 }}
                                whileTap={{ scale: 0.99 }}
                                className="relative"
                            >
                                <Lock className={`absolute ${isRtl ? 'right-3' : 'left-3'} top-2.5 h-5 w-5 text-gray-400`} />
                                <input
                                    id="password"
                                    name="password"
                                    type="password"
                                    autoComplete="current-password"
                                    value={password}
                                    onChange={(e) => setPassword(e.target.value)}
                                    className={`block w-full rounded-lg border-gray-300 bg-gray-50 py-2 ${isRtl ? 'pr-10' : 'pl-10'} shadow-sm focus:border-primary-500 focus:ring-primary-500 text-gray-900`}
                                    placeholder={isRtl ? "كلمة المرور" : "Password"}
                                    required
                                />
                            </motion.div>
                        </motion.div>

                        <motion.div
                            variants={formControls}
                            custom={4}
                            initial="hidden"
                            animate="visible"
                        >
                            <motion.button
                                type="submit"
                                disabled={buttonLoading || formState === "success"}
                                className={`relative flex w-full justify-center rounded-lg bg-gradient-to-r from-[#45cab0] to-[#2dd3c5] px-3 py-2.5 text-sm font-semibold text-white shadow-sm ${(buttonLoading || formState === "success") ? 'opacity-80' : 'hover:from-[#3bb69d] hover:to-[#28c1b4]'} focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary-600`}
                            >
                                {buttonLoading ? (
                                    <>
                                        <span className="flex items-center justify-center">
                                            <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                                            <span>{t('loading')}</span>
                                        </span>
                                    </>
                                ) : formState === "success" ? (
                                    <>
                                        <span className="flex items-center justify-center">
                                            <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                                            <span>{t('redirecting')}</span>
                                        </span>
                                    </>
                                ) : (
                                    t('login')
                                )}
                            </motion.button>
                        </motion.div>
                    </motion.form>

                    <motion.p
                        className="mt-10 text-center text-sm text-gray-500"
                        variants={formControls}
                        custom={5}
                        initial="hidden"
                        animate="visible"
                    >
                        {t('dontHaveAccount')}
                        <br />
                        <span className="font-semibold text-[#45cab0]">
                            {t('contactAdmin')}
                        </span>
                    </motion.p>
                </div>
            </motion.div>
        </div>
    );
} 
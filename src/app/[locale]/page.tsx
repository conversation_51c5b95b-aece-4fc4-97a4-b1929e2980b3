"use client";

import { useEffect } from "react";
import { useRouter } from "@/i18n/navigation";
import { useRoleGuard } from "@/hooks/useRoleGuard";
import { Loader } from "@/components/shared/Loader";
import { useLocale } from 'next-intl';

export default function Home() {
  const router = useRouter();
  const locale = useLocale();
  const { isAuthorized, isLoading } = useRoleGuard('Both');

  useEffect(() => {
    // If user is authorized (logged in), redirect to project selection
    if (!isLoading && isAuthorized) {
      router.push(`/${locale}/project-selection`);
    }
  }, [isLoading, isAuthorized, router, locale]);

  // Show a loader while checking authorization
  if (isLoading) {
    return (
      <div className="h-screen flex items-center justify-center ">
        <Loader size="lg" variant="primary" />
      </div>
    );
  }

  
}

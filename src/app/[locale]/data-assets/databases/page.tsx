"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useTranslations } from "next-intl";
import { useParams, useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Database, Eye, Package, AlertCircle, Loader2, ArrowLeft } from "lucide-react";
import { AssetTable, TableColumn } from "@/components/ui/data-assets/AssetTable";
import { DatabaseAnalytics } from "@/components/ui/data-assets/DatabaseCharts";
import { dataAssetsService, DatabaseAsset, SystemAsset } from "@/lib/services/dataAssetsService";
import { toast } from "sonner";

// Animation variants
const pageVariants = {
    hidden: { 
        opacity: 0
    },
    visible: {
        opacity: 1,
        transition: {
            duration: 0.6,
            staggerChildren: 0.1
        }
    }
};

const heroVariants = {
    hidden: { 
        opacity: 0,
        y: -30
    },
    visible: {
        opacity: 1,
        y: 0,
        transition: {
            type: "spring",
            stiffness: 260,
            damping: 20,
            duration: 0.8
        }
    }
};

const tabsVariants = {
    hidden: { 
        opacity: 0,
        y: 20
    },
    visible: {
        opacity: 1,
        y: 0,
        transition: {
            duration: 0.5,
            delay: 0.3
        }
    }
};

const contentVariants = {
    hidden: { 
        opacity: 0,
        x: 20
    },
    visible: {
        opacity: 1,
        x: 0,
        transition: {
            duration: 0.4
        }
    },
    exit: {
        opacity: 0,
        x: -20,
        transition: {
            duration: 0.3
        }
    }
};

type TabType = 'overview' | 'inventory';

interface TabData {
    id: TabType;
    labelKey: string;
    icon: React.ElementType;
}

const tabs: TabData[] = [
    { id: 'overview', labelKey: 'overview', icon: Eye },
    { id: 'inventory', labelKey: 'inventory', icon: Package }
];

export default function DatabasesPage() {
    const [activeTab, setActiveTab] = useState<TabType>('overview');
    const [currentProjectId, setCurrentProjectId] = useState<string | null>(null);
    const [isProjectLoading, setIsProjectLoading] = useState(true);
    
    const databasesT = useTranslations('Databases');
    const params = useParams();
    const router = useRouter();
    const lang = params.locale as 'ar' | 'en';
    const isRtl = lang === 'ar';
    
    const [databases, setDatabases] = useState<DatabaseAsset[]>([]);
    const [systems, setSystems] = useState<SystemAsset[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    // Get current project ID from localStorage
    useEffect(() => {
        const projectId = localStorage.getItem('currentProjectId');
        setCurrentProjectId(projectId);
        setIsProjectLoading(false);
        
        if (!projectId) {
            console.warn('No project selected. User should select a project first.');
        }
    }, []);

    // Fetch databases and systems data
    useEffect(() => {
        const fetchData = async () => {
            if (!currentProjectId) {
                setLoading(false);
                return;
            }

            try {
                setLoading(true);
                const [databasesData, systemsData] = await Promise.all([
                    dataAssetsService.getAssets<DatabaseAsset>(currentProjectId, 'databases'),
                    dataAssetsService.getAssets<SystemAsset>(currentProjectId, 'systems')
                ]);
                setDatabases(databasesData);
                setSystems(systemsData);
            } catch (error) {
                console.error('Error fetching data:', error);
                setError('Failed to load data');
                toast.error('Failed to load databases');
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, [currentProjectId]);

    // Define table columns for databases
    const databaseColumns: TableColumn<DatabaseAsset>[] = [
        {
            key: 'name',
            label: databasesT('databaseName'),
            sortable: true,
            editable: true,
            type: 'text',
            required: true,
            placeholder: databasesT('databaseNamePlaceholder'),
            width: 'w-[200px]',
            priority: 'high' // Always visible
        },
        {
            key: 'description',
            label: databasesT('description'),
            sortable: false,
            editable: true,
            type: 'text',
            required: true,
            placeholder: databasesT('descriptionPlaceholder'),
            width: 'w-[300px]',
            priority: 'high' // Always visible
        },
        {
            key: 'dbms',
            label: databasesT('dbms'),
            sortable: true,
            editable: true,
            type: 'select',
            required: true,
            options: ['Oracle', 'MySQL', 'SQL Server', 'PostgreSQL'],
            width: 'w-[140px]',
            priority: 'high', // Important for databases
            render: (value: string) => {
                const dbmsColors = {
                    'Oracle': 'bg-red-100 text-red-800',
                    'MySQL': 'bg-orange-100 text-orange-800',
                    'SQL Server': 'bg-blue-100 text-blue-800',
                    'PostgreSQL': 'bg-indigo-100 text-indigo-800'
                };
                return (
                    <Badge className={dbmsColors[value as keyof typeof dbmsColors] || 'bg-gray-100 text-gray-800'}>
                        {value}
                    </Badge>
                );
            }
        },
        {
            key: 'status',
            label: databasesT('status'),
            sortable: true,
            editable: true,
            type: 'select',
            required: true,
            options: ['Online', 'Offline', 'Maintenance', 'Archived'],
            width: 'w-[120px]',
            priority: 'high', // Status is important
            render: (value: string) => {
                const statusColors = {
                    'Online': 'bg-green-100 text-green-800',
                    'Offline': 'bg-red-100 text-red-800',
                    'Maintenance': 'bg-yellow-100 text-yellow-800',
                    'Archived': 'bg-gray-100 text-gray-800'
                };
                return (
                    <Badge className={statusColors[value as keyof typeof statusColors] || 'bg-gray-100 text-gray-800'}>
                        {value}
                    </Badge>
                );
            }
        },
        {
            key: 'databaseSystem',
            label: databasesT('databaseSystem'),
            sortable: true,
            editable: true,
            type: 'select',
            options: [databasesT('noLinkedSystem'), ...systems.map(system => system.name)],
            placeholder: 'Select linked system',
            width: 'w-[180px]',
            priority: 'medium', // Hide on smaller screens
            render: (value) => {
                if (!value || value === databasesT('noLinkedSystem')) {
                    return (
                        <Badge variant="outline" className="text-gray-600">
                            {databasesT('noLinkedSystem')}
                        </Badge>
                    );
                }
                const linkedSystem = systems.find(sys => sys.name === value);
                return linkedSystem ? (
                    <Badge className="bg-blue-100 text-blue-800">
                        {linkedSystem.name}
                    </Badge>
                ) : value;
            }
        },
        {
            key: 'technology',
            label: databasesT('technology'),
            sortable: true,
            editable: true,
            type: 'text',
            required: true,
            placeholder: databasesT('technologyPlaceholder'),
            width: 'w-[140px]',
            priority: 'medium' // Hide on smaller screens
        },
        {
            key: 'storageDevice',
            label: databasesT('storageDevice'),
            sortable: true,
            editable: true,
            type: 'text',
            required: true,
            placeholder: databasesT('storageDevicePlaceholder'),
            width: 'w-[140px]',
            priority: 'low' // Hide on mobile
        },
        {
            key: 'databaseLocation',
            label: databasesT('databaseLocation'),
            sortable: true,
            editable: true,
            type: 'text',
            required: true,
            placeholder: databasesT('databaseLocationPlaceholder'),
            width: 'w-[160px]',
            priority: 'low' // Hide on mobile
        }
    ];

    const handleAddDatabase = async (newDatabaseData: Partial<DatabaseAsset>): Promise<void> => {
        if (!currentProjectId) {
            toast.error('No project selected. Please select a project first.');
            throw new Error('No project selected');
        }

        try {
            const loadingToast = toast.loading('Creating database...');
            
            // Convert linked system name to system ID if needed
            const processedData = { ...newDatabaseData } as Omit<DatabaseAsset, 'id' | 'createdAt' | 'updatedAt'>;
            if (processedData.databaseSystem && processedData.databaseSystem !== databasesT('noLinkedSystem')) {
                const linkedSystem = systems.find(sys => sys.name === processedData.databaseSystem);
                if (linkedSystem) {
                    processedData.databaseSystem = linkedSystem.id;
                }
            } else {
                processedData.databaseSystem = undefined;
            }

            processedData.projectId = currentProjectId;
            processedData.name = processedData.name || '';
            processedData.description = processedData.description || '';
            processedData.dbms = processedData.dbms || 'MySQL';
            processedData.technology = processedData.technology || '';
            processedData.storageDevice = processedData.storageDevice || '';
            processedData.databaseLocation = processedData.databaseLocation || '';
            processedData.status = processedData.status || 'Online';

            await dataAssetsService.createAsset<DatabaseAsset>(currentProjectId, 'databases', processedData);
            
            // Refresh data
            const updatedDatabases = await dataAssetsService.getAssets<DatabaseAsset>(currentProjectId, 'databases');
            setDatabases(updatedDatabases);
            
            toast.dismiss(loadingToast);
            toast.success(`Database "${processedData.name}" created successfully!`);
        } catch (error) {
            console.error('Error adding database:', error);
            toast.error('Failed to create database');
            throw error;
        }
    };

    const handleBulkImport = async (databasesData: Partial<DatabaseAsset>[]): Promise<void> => {
        if (!currentProjectId) {
            toast.error('No project selected. Please select a project first.');
            throw new Error('No project selected');
        }

        try {
            const loadingToast = toast.loading(`Importing ${databasesData.length} databases...`);
            
            for (const databaseData of databasesData) {
                const processedData = { ...databaseData } as Omit<DatabaseAsset, 'id' | 'createdAt' | 'updatedAt'>;
                if (processedData.databaseSystem && processedData.databaseSystem !== databasesT('noLinkedSystem')) {
                    const linkedSystem = systems.find(sys => sys.name === processedData.databaseSystem);
                    if (linkedSystem) {
                        processedData.databaseSystem = linkedSystem.id;
                    }
                } else {
                    processedData.databaseSystem = undefined;
                }

                processedData.projectId = currentProjectId;
                processedData.name = processedData.name || '';
                processedData.description = processedData.description || '';
                processedData.dbms = processedData.dbms || 'MySQL';
                processedData.technology = processedData.technology || '';
                processedData.storageDevice = processedData.storageDevice || '';
                processedData.databaseLocation = processedData.databaseLocation || '';
                processedData.status = processedData.status || 'Online';

                await dataAssetsService.createAsset<DatabaseAsset>(currentProjectId, 'databases', processedData);
            }
            
            const updatedDatabases = await dataAssetsService.getAssets<DatabaseAsset>(currentProjectId, 'databases');
            setDatabases(updatedDatabases);
            
            toast.dismiss(loadingToast);
            toast.success(`Successfully imported ${databasesData.length} databases!`);
        } catch (error) {
            console.error('Error bulk importing databases:', error);
            toast.error('Failed to import databases');
            throw error;
        }
    };

    const handleEditDatabase = async (database: DatabaseAsset, updates: Partial<DatabaseAsset>): Promise<void> => {
        try {
            if (database.id) {
                const loadingToast = toast.loading('Updating database...');
                
                const processedUpdates = { ...updates };
                if (processedUpdates.databaseSystem && processedUpdates.databaseSystem !== databasesT('noLinkedSystem')) {
                    const linkedSystem = systems.find(sys => sys.name === processedUpdates.databaseSystem);
                    if (linkedSystem) {
                        processedUpdates.databaseSystem = linkedSystem.id;
                    }
                } else if (processedUpdates.databaseSystem === databasesT('noLinkedSystem')) {
                    processedUpdates.databaseSystem = undefined;
                }

                await dataAssetsService.updateAsset<DatabaseAsset>(currentProjectId!, 'databases', database.id, processedUpdates);
                
                const updatedDatabases = await dataAssetsService.getAssets<DatabaseAsset>(currentProjectId!, 'databases');
                setDatabases(updatedDatabases);
                
                toast.dismiss(loadingToast);
                toast.success(`Database "${updates.name || database.name}" updated successfully!`);
            }
        } catch (error) {
            console.error('Error editing database:', error);
            toast.error('Failed to update database');
            throw error;
        }
    };

    const handleDeleteDatabase = async (database: DatabaseAsset) => {
        try {
            if (database.id) {
                const loadingToast = toast.loading('Deleting database...');
                await dataAssetsService.deleteAsset(currentProjectId!, 'databases', database.id);
                
                const updatedDatabases = await dataAssetsService.getAssets<DatabaseAsset>(currentProjectId!, 'databases');
                setDatabases(updatedDatabases);
                
                toast.dismiss(loadingToast);
                toast.success(`Database "${database.name}" deleted successfully!`);
            }
        } catch (error) {
            console.error('Error deleting database:', error);
            toast.error('Failed to delete database');
        }
    };

    const handleViewDatabase = (database: DatabaseAsset) => {
        toast.info(`Viewing database: ${database.name}`, {
            description: 'Database details view is coming soon!'
        });
    };

    const handleBackToDataAssets = () => {
        router.push(`/${lang}/data-assets`);
    };

    const renderContent = () => {
        // Show project loading state
        if (isProjectLoading) {
            return (
                <motion.div
                    key="loading-project"
                    variants={contentVariants}
                    initial="hidden"
                    animate="visible"
                    className="flex items-center justify-center py-20"
                >
                    <div className="text-center">
                        <Loader2 className="h-12 w-12 animate-spin text-[#003874] mx-auto mb-4" />
                        <p className="text-gray-600 text-lg">Loading project information...</p>
                    </div>
                </motion.div>
            );
        }

        // Show project selection required
        if (!currentProjectId) {
            return (
                <motion.div
                    key="no-project"
                    variants={contentVariants}
                    initial="hidden"
                    animate="visible"
                    className="text-center py-20"
                >
                    <div className="max-w-md mx-auto">
                        <AlertCircle className="h-16 w-16 text-amber-500 mx-auto mb-6" />
                        <h3 className="text-2xl font-bold text-gray-900 mb-4">
                            Project Required
                        </h3>
                        <p className="text-gray-600 text-lg mb-8">
                            Please select a project from the project selection page to manage your databases inventory.
                        </p>
                        <Button
                            onClick={() => router.push(`/${lang}/project-selection`)}
                            className="bg-gradient-to-r from-[#003874] to-[#2D8DC6] hover:from-[#001f4d] hover:to-[#1e6fa8]"
                        >
                            Select Project
                        </Button>
                    </div>
                </motion.div>
            );
        }

        switch (activeTab) {
            case 'overview':
                return (
                    <motion.div
                        key="overview"
                        variants={contentVariants}
                        initial="hidden"
                        animate="visible"
                        exit="exit"
                        className="space-y-8"
                    >
                        {databases.length === 0 ? (
                            <div className="text-center py-20">
                                <Database className="mx-auto h-16 w-16 text-[#003874] mb-6" />
                                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                                    {databasesT('overviewTitle')}
                                </h3>
                                <p className="text-gray-600 text-lg max-w-2xl mx-auto mb-8">
                                    {databasesT('overviewDescription')}
                                </p>
                                
                                <div className="mt-8 p-6 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl border border-blue-200 max-w-2xl mx-auto">
                                    <div className="text-center">
                                        <h4 className="text-lg font-semibold text-gray-800 mb-2">Ready to get started?</h4>
                                        <p className="text-gray-600 mb-4 text-sm">Add your first database to begin building your organization's data assets inventory.</p>
                                        <Button
                                            onClick={() => setActiveTab('inventory')}
                                            className="bg-gradient-to-r from-[#003874] to-[#2D8DC6] hover:from-[#001f4d] hover:to-[#1e6fa8] shadow-lg hover:shadow-xl transition-all duration-200"
                                        >
                                            <Package className="h-4 w-4 mr-2" />
                                            Start Adding Databases
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        ) : (
                            <DatabaseAnalytics 
                                databases={databases} 
                                isRTL={isRtl} 
                                loading={loading} 
                            />
                        )}
                    </motion.div>
                );
            case 'inventory':
                return (
                    <motion.div
                        key="inventory"
                        variants={contentVariants}
                        initial="hidden"
                        animate="visible"
                        exit="exit"
                        className="space-y-8"
                    >
                        <div className="space-y-6">
                            {/* Info banner about analytics */}
                            {databases.length > 0 && (
                                <div className="bg-gradient-to-r from-blue-50 via-indigo-50 to-purple-50 border border-blue-200 rounded-xl p-4">
                                    <div className={`flex items-center gap-3 ${isRtl ? 'flex-row-reverse' : ''}`}>
                                        <div className="p-2 bg-gradient-to-r from-[#003874] to-[#2D8DC6] rounded-lg">
                                            <Database className="h-5 w-5 text-white" />
                                        </div>
                                        <div className={`${isRtl ? 'text-right' : 'text-left'}`}>
                                            <p className={`text-sm font-medium text-gray-800 ${isRtl ? 'font-arabic' : ''}`}>
                                                💡 Your database data is automatically analyzed in the Overview tab with real-time charts and KPIs.
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            )}
                            
                            <AssetTable<DatabaseAsset>
                                assets={databases}
                                columns={databaseColumns}
                                loading={loading}
                                error={error}
                                onAdd={handleAddDatabase}
                                onEdit={handleEditDatabase}
                                onDelete={handleDeleteDatabase}
                                onView={handleViewDatabase}
                                onBulkImport={handleBulkImport}
                                searchPlaceholder={databasesT('searchDatabases')}
                                addButtonLabel={databasesT('addDatabase')}
                                emptyMessage={databasesT('noDatabases')}
                                emptyDescription={databasesT('noDatabasesDescription')}
                                isRTL={isRtl}
                                assetType="databases"
                                templateFileName="databases"
                            />
                        </div>
                    </motion.div>
                );
            default:
                return null;
        }
    };

    return (
        <motion.div
            variants={pageVariants}
            initial="hidden"
            animate="visible"
            className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100"
        >
            {/* Hero Section with Integrated Tabs */}
            <motion.div variants={heroVariants} className="relative">
                <div className="relative overflow-hidden bg-gradient-to-r from-[#003874] via-[#2D8DC6] to-[#48D3A5] pb-24">
                    {/* Back button */}
                    <div className="absolute top-6 left-6 z-30">
                        <Button
                            onClick={handleBackToDataAssets}
                            variant="ghost"
                            className="text-white hover:bg-white/10 hover:text-white"
                        >
                            <ArrowLeft className={`h-4 w-4 ${isRtl ? 'ml-2 rotate-180' : 'mr-2'}`} />
                            Back to Data Assets
                        </Button>
                    </div>

                    {/* Decorative Elements */}
                    <motion.div
                        className={`absolute top-0 w-64 h-64 rounded-full opacity-30 bg-white ${isRtl ? 'left-0' : 'right-0'}`}
                        initial={{ x: isRtl ? -100 : 100, y: -100 }}
                        animate={{
                            x: 0,
                            y: 0,
                            scale: [1, 1.2, 1],
                            rotate: [0, 45, 0],
                        }}
                        transition={{ duration: 20, repeat: Infinity, repeatType: "reverse" }}
                    />
                    <motion.div
                        className={`absolute bottom-0 w-32 h-32 rounded-full opacity-30 bg-white ${isRtl ? 'right-1/4' : 'left-1/4'}`}
                        initial={{ y: 50 }}
                        animate={{
                            y: [0, 20, 0],
                            scale: [1, 1.1, 1],
                        }}
                        transition={{ duration: 8, repeat: Infinity, repeatType: "reverse" }}
                    />
                    <motion.div
                        className="absolute top-1/3 right-1/4 w-48 h-48 rounded-full opacity-20 bg-white"
                        initial={{ y: -20 }}
                        animate={{
                            y: [0, -30, 0],
                            scale: [1, 1.2, 1],
                        }}
                        transition={{ duration: 12, repeat: Infinity, repeatType: "reverse" }}
                    />

                    {/* Hero Content */}
                    <div className="relative z-10 container mx-auto px-10 py-16 flex flex-col justify-center min-h-[500px]">
                        <motion.h1
                            className="text-4xl md:text-5xl font-bold text-white mb-4"
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.5 }}
                        >
                            {databasesT('title')}
                        </motion.h1>

                        <motion.p
                            className="text-xl text-white/80 mb-8 max-w-2xl"
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.5, delay: 0.2 }}
                        >
                            {databasesT('subtitle')}
                        </motion.p>
                    </div>
                    
                    {/* Tabs positioned on the hero background */}
                    <motion.div 
                        variants={tabsVariants}
                        className="absolute bottom-8 left-0 right-0 z-20"
                    >
                        <div className="container mx-auto px-6">
                            <div className="flex justify-center">
                                <div className={`flex bg-white/20 backdrop-blur-md border border-white/30 rounded-2xl p-2 shadow-2xl ${isRtl ? 'flex-row-reverse' : ''}`}>
                                    {tabs.map((tab) => {
                                        const Icon = tab.icon;
                                        const isActive = activeTab === tab.id;
                                        
                                        return (
                                            <Button
                                                key={tab.id}
                                                onClick={() => setActiveTab(tab.id)}
                                                variant="ghost"
                                                className={`
                                                    relative px-8 py-4 rounded-xl font-semibold text-base transition-all duration-300 
                                                    ${isActive 
                                                        ? 'bg-white text-[#003874] shadow-lg scale-105' 
                                                        : 'text-white hover:bg-white/10 hover:text-white'
                                                    }
                                                    ${isRtl ? 'flex-row-reverse' : ''}
                                                `}
                                            >
                                                <Icon className={`h-5 w-5 ${isRtl ? 'ml-3' : 'mr-3'}`} />
                                                {databasesT(tab.labelKey)}
                                                
                                                {/* Active indicator */}
                                                {isActive && (
                                                    <motion.div
                                                        layoutId="activeTab"
                                                        className="absolute inset-0 bg-white rounded-xl -z-10"
                                                        initial={false}
                                                        transition={{
                                                            type: "spring",
                                                            stiffness: 500,
                                                            damping: 30
                                                        }}
                                                    />
                                                )}
                                            </Button>
                                        );
                                    })}
                                </div>
                            </div>
                        </div>
                    </motion.div>
                </div>
            </motion.div>

            {/* Content Section */}
            <div className="container mx-auto px-6 py-12">
                <AnimatePresence mode="wait">
                    {renderContent()}
                </AnimatePresence>
            </div>

            {/* Background decoration */}
            <div className="fixed inset-0 pointer-events-none overflow-hidden -z-10">
                <div className="absolute top-20 left-10 w-64 h-64 bg-blue-500/5 rounded-full blur-3xl"></div>
                <div className="absolute bottom-20 right-10 w-96 h-96 bg-indigo-500/5 rounded-full blur-3xl"></div>
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-128 h-128 bg-emerald-500/3 rounded-full blur-3xl"></div>
            </div>
        </motion.div>
    );
} 
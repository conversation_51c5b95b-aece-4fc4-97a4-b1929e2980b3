"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useTranslations } from "next-intl";
import { useParams, useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { FileText, Eye, Package, AlertCircle, Loader2, ArrowLeft } from "lucide-react";
import { LongDataAssets } from "@/components/ui/data-assets/LongDataAssets";
import { dataAssetsService, DatasetAsset, DatabaseAsset, SystemAsset } from "@/lib/services/dataAssetsService";
import { toast } from "sonner";

// Animation variants
const pageVariants = {
    hidden: { 
        opacity: 0
    },
    visible: {
        opacity: 1,
        transition: {
            duration: 0.6,
            staggerChildren: 0.1
        }
    }
};

const heroVariants = {
    hidden: { 
        opacity: 0,
        y: -30
    },
    visible: {
        opacity: 1,
        y: 0,
        transition: {
            type: "spring",
            stiffness: 260,
            damping: 20,
            duration: 0.8
        }
    }
};

const tabsVariants = {
    hidden: { 
        opacity: 0,
        y: 20
    },
    visible: {
        opacity: 1,
        y: 0,
        transition: {
            duration: 0.5,
            delay: 0.3
        }
    }
};

const contentVariants = {
    hidden: { 
        opacity: 0,
        x: 20
    },
    visible: {
        opacity: 1,
        x: 0,
        transition: {
            duration: 0.4
        }
    },
    exit: {
        opacity: 0,
        x: -20,
        transition: {
            duration: 0.3
        }
    }
};

type TabType = 'overview' | 'inventory';

interface TabData {
    id: TabType;
    labelKey: string;
    icon: React.ElementType;
}

const tabs: TabData[] = [
    { id: 'overview', labelKey: 'overview', icon: Eye },
    { id: 'inventory', labelKey: 'inventory', icon: Package }
];

export default function DatasetsPage() {
    const [activeTab, setActiveTab] = useState<TabType>('overview');
    const [currentProjectId, setCurrentProjectId] = useState<string | null>(null);
    const [isProjectLoading, setIsProjectLoading] = useState(true);
    
    const datasetsT = useTranslations('Datasets');
    const params = useParams();
    const router = useRouter();
    const lang = params.locale as 'ar' | 'en';
    const isRtl = lang === 'ar';
    
    const [datasets, setDatasets] = useState<DatasetAsset[]>([]);
    const [databases, setDatabases] = useState<DatabaseAsset[]>([]);
    const [systems, setSystems] = useState<SystemAsset[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    // Get current project ID from localStorage
    useEffect(() => {
        const projectId = localStorage.getItem('currentProjectId');
        setCurrentProjectId(projectId);
        setIsProjectLoading(false);
        
        if (!projectId) {
            console.warn('No project selected. User should select a project first.');
        }
    }, []);

    // Fetch datasets, databases, and systems data
    useEffect(() => {
        const fetchData = async () => {
            if (!currentProjectId) {
                setLoading(false);
                return;
            }

            try {
                setLoading(true);
                const [datasetsData, databasesData, systemsData] = await Promise.all([
                    dataAssetsService.getAssets<DatasetAsset>(currentProjectId, 'datasets'),
                    dataAssetsService.getAssets<DatabaseAsset>(currentProjectId, 'databases'),
                    dataAssetsService.getAssets<SystemAsset>(currentProjectId, 'systems')
                ]);
                setDatasets(datasetsData);
                setDatabases(databasesData);
                setSystems(systemsData);
            } catch (error) {
                console.error('Error fetching data:', error);
                setError('Failed to load data');
                toast.error('Failed to load datasets');
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, [currentProjectId]);

    const handleAddDataset = async (newDatasetData: Partial<DatasetAsset>): Promise<void> => {
        if (!currentProjectId) {
            toast.error('No project selected. Please select a project first.');
            throw new Error('No project selected');
        }

        try {
            const loadingToast = toast.loading('Creating dataset...');
            
            const processedData = { ...newDatasetData } as Omit<DatasetAsset, 'id' | 'createdAt' | 'updatedAt'>;
            processedData.projectId = currentProjectId;
            processedData.name = processedData.name || '';
            processedData.description = processedData.description || '';
            processedData.databasesUsed = processedData.databasesUsed || [];
            processedData.systemsThatUseIt = processedData.systemsThatUseIt || [];
            processedData.owner = processedData.owner || '';
            processedData.size = processedData.size || '';
            processedData.updateFrequency = processedData.updateFrequency || 'Monthly';
            processedData.sensitivity = processedData.sensitivity || 'Internal';
            processedData.format = processedData.format || '';

            await dataAssetsService.createAsset<DatasetAsset>(currentProjectId, 'datasets', processedData);
            
            // Refresh data
            const updatedDatasets = await dataAssetsService.getAssets<DatasetAsset>(currentProjectId, 'datasets');
            setDatasets(updatedDatasets);
            
            toast.dismiss(loadingToast);
            toast.success(`Dataset "${processedData.name}" created successfully!`);
        } catch (error) {
            console.error('Error adding dataset:', error);
            toast.error('Failed to create dataset');
            throw error;
        }
    };

    const handleBulkImport = async (datasetsData: Partial<DatasetAsset>[]): Promise<void> => {
        if (!currentProjectId) {
            toast.error('No project selected. Please select a project first.');
            throw new Error('No project selected');
        }

        try {
            const loadingToast = toast.loading(`Importing ${datasetsData.length} datasets...`);
            
            for (const datasetData of datasetsData) {
                const processedData = { ...datasetData } as Omit<DatasetAsset, 'id' | 'createdAt' | 'updatedAt'>;
                processedData.projectId = currentProjectId;
                processedData.name = processedData.name || '';
                processedData.description = processedData.description || '';
                processedData.databasesUsed = processedData.databasesUsed || [];
                processedData.systemsThatUseIt = processedData.systemsThatUseIt || [];
                processedData.owner = processedData.owner || '';
                processedData.size = processedData.size || '';
                processedData.updateFrequency = processedData.updateFrequency || 'Monthly';
                processedData.sensitivity = processedData.sensitivity || 'Internal';
                processedData.format = processedData.format || '';

                await dataAssetsService.createAsset<DatasetAsset>(currentProjectId, 'datasets', processedData);
            }
            
            const updatedDatasets = await dataAssetsService.getAssets<DatasetAsset>(currentProjectId, 'datasets');
            setDatasets(updatedDatasets);
            
            toast.dismiss(loadingToast);
            toast.success(`Successfully imported ${datasetsData.length} datasets!`);
        } catch (error) {
            console.error('Error bulk importing datasets:', error);
            toast.error('Failed to import datasets');
            throw error;
        }
    };

    const handleEditDataset = async (dataset: DatasetAsset, updates: Partial<DatasetAsset>): Promise<void> => {
        try {
            if (dataset.id) {
                const loadingToast = toast.loading('Updating dataset...');
                
                await dataAssetsService.updateAsset<DatasetAsset>(currentProjectId!, 'datasets', dataset.id, updates);
                
                const updatedDatasets = await dataAssetsService.getAssets<DatasetAsset>(currentProjectId!, 'datasets');
                setDatasets(updatedDatasets);
                
                toast.dismiss(loadingToast);
                toast.success(`Dataset "${updates.name || dataset.name}" updated successfully!`);
            }
        } catch (error) {
            console.error('Error editing dataset:', error);
            toast.error('Failed to update dataset');
            throw error;
        }
    };

    const handleDeleteDataset = async (dataset: DatasetAsset) => {
        try {
            if (dataset.id) {
                const loadingToast = toast.loading('Deleting dataset...');
                await dataAssetsService.deleteAsset(currentProjectId!, 'datasets', dataset.id);
                
                const updatedDatasets = await dataAssetsService.getAssets<DatasetAsset>(currentProjectId!, 'datasets');
                setDatasets(updatedDatasets);
                
                toast.dismiss(loadingToast);
                toast.success(`Dataset "${dataset.name}" deleted successfully!`);
            }
        } catch (error) {
            console.error('Error deleting dataset:', error);
            toast.error('Failed to delete dataset');
        }
    };

    const handleViewDataset = (dataset: DatasetAsset) => {
        toast.info(`Viewing dataset: ${dataset.name}`, {
            description: 'Dataset details view is coming soon!'
        });
    };

    const handleBackToDataAssets = () => {
        router.push(`/${lang}/data-assets`);
    };

    const renderContent = () => {
        // Show project loading state
        if (isProjectLoading) {
            return (
                <motion.div
                    key="loading-project"
                    variants={contentVariants}
                    initial="hidden"
                    animate="visible"
                    className="flex items-center justify-center py-20"
                >
                    <div className="text-center">
                        <Loader2 className="h-12 w-12 animate-spin text-[#003874] mx-auto mb-4" />
                        <p className="text-gray-600 text-lg">Loading project information...</p>
                    </div>
                </motion.div>
            );
        }

        // Show project selection required
        if (!currentProjectId) {
            return (
                <motion.div
                    key="no-project"
                    variants={contentVariants}
                    initial="hidden"
                    animate="visible"
                    className="text-center py-20"
                >
                    <div className="max-w-md mx-auto">
                        <AlertCircle className="h-16 w-16 text-amber-500 mx-auto mb-6" />
                        <h3 className="text-2xl font-bold text-gray-900 mb-4">
                            Project Required
                        </h3>
                        <p className="text-gray-600 text-lg mb-8">
                            Please select a project from the project selection page to manage your datasets inventory.
                        </p>
                        <Button
                            onClick={() => router.push(`/${lang}/project-selection`)}
                            className="bg-gradient-to-r from-[#003874] to-[#2D8DC6] hover:from-[#001f4d] hover:to-[#1e6fa8]"
                        >
                            Select Project
                        </Button>
                    </div>
                </motion.div>
            );
        }

        switch (activeTab) {
            case 'overview':
                return (
                    <motion.div
                        key="overview"
                        variants={contentVariants}
                        initial="hidden"
                        animate="visible"
                        exit="exit"
                        className="space-y-8"
                    >
                        {/* Empty overview section as requested */}
                        <div className="text-center py-20">
                            <FileText className="mx-auto h-16 w-16 text-[#003874] mb-6" />
                            <h3 className="text-2xl font-bold text-gray-900 mb-4">
                                {datasetsT('overviewTitle')}
                            </h3>
                            <p className="text-gray-600 text-lg max-w-2xl mx-auto mb-8">
                                {datasetsT('overviewDescription')}
                            </p>
                            
                            <div className="mt-8 p-6 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl border border-blue-200 max-w-2xl mx-auto">
                                <div className="text-center">
                                    <h4 className="text-lg font-semibold text-gray-800 mb-2">Ready to get started?</h4>
                                    <p className="text-gray-600 mb-4 text-sm">Add your first dataset to begin building your organization's data assets inventory.</p>
                                    <Button
                                        onClick={() => setActiveTab('inventory')}
                                        className="bg-gradient-to-r from-[#003874] to-[#2D8DC6] hover:from-[#001f4d] hover:to-[#1e6fa8] shadow-lg hover:shadow-xl transition-all duration-200"
                                    >
                                        <Package className="h-4 w-4 mr-2" />
                                        Start Adding Datasets
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </motion.div>
                );
            case 'inventory':
                return (
                    <motion.div
                        key="inventory"
                        variants={contentVariants}
                        initial="hidden"
                        animate="visible"
                        exit="exit"
                        className="space-y-8"
                    >
                        <div className="space-y-6">
                            <LongDataAssets
                                datasets={datasets}
                                databases={databases}
                                systems={systems}
                                loading={loading}
                                error={error}
                                onAdd={handleAddDataset}
                                onEdit={handleEditDataset}
                                onDelete={handleDeleteDataset}
                                onView={handleViewDataset}
                                onBulkImport={handleBulkImport}
                                searchPlaceholder={datasetsT('searchDatasets')}
                                addButtonLabel={datasetsT('addDataset')}
                                emptyMessage={datasetsT('noDatasets')}
                                emptyDescription={datasetsT('noDatasetsDescription')}
                                isRTL={isRtl}
                                assetType="datasets"
                                templateFileName="datasets"
                            />
                        </div>
                    </motion.div>
                );
            default:
                return null;
        }
    };

    return (
        <motion.div
            variants={pageVariants}
            initial="hidden"
            animate="visible"
            className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100"
        >
            {/* Hero Section with Integrated Tabs */}
            <motion.div variants={heroVariants} className="relative">
                <div className="relative overflow-hidden bg-gradient-to-r from-[#003874] via-[#2D8DC6] to-[#48D3A5] pb-24">
                    {/* Back button */}
                    <div className="absolute top-6 left-6 z-30">
                        <Button
                            onClick={handleBackToDataAssets}
                            variant="ghost"
                            className="text-white hover:bg-white/10 hover:text-white"
                        >
                            <ArrowLeft className={`h-4 w-4 ${isRtl ? 'ml-2 rotate-180' : 'mr-2'}`} />
                            Back to Data Assets
                        </Button>
                    </div>

                    {/* Decorative Elements */}
                    <motion.div
                        className={`absolute top-0 w-64 h-64 rounded-full opacity-30 bg-white ${isRtl ? 'left-0' : 'right-0'}`}
                        initial={{ x: isRtl ? -100 : 100, y: -100 }}
                        animate={{
                            x: 0,
                            y: 0,
                            scale: [1, 1.2, 1],
                            rotate: [0, 45, 0],
                        }}
                        transition={{ duration: 20, repeat: Infinity, repeatType: "reverse" }}
                    />
                    <motion.div
                        className={`absolute bottom-0 w-32 h-32 rounded-full opacity-30 bg-white ${isRtl ? 'right-1/4' : 'left-1/4'}`}
                        initial={{ y: 50 }}
                        animate={{
                            y: [0, 20, 0],
                            scale: [1, 1.1, 1],
                        }}
                        transition={{ duration: 8, repeat: Infinity, repeatType: "reverse" }}
                    />
                    <motion.div
                        className="absolute top-1/3 right-1/4 w-48 h-48 rounded-full opacity-20 bg-white"
                        initial={{ y: -20 }}
                        animate={{
                            y: [0, -30, 0],
                            scale: [1, 1.2, 1],
                        }}
                        transition={{ duration: 12, repeat: Infinity, repeatType: "reverse" }}
                    />

                    {/* Hero Content */}
                    <div className="relative z-10 container mx-auto px-10 py-16 flex flex-col justify-center min-h-[500px]">
                        <motion.h1
                            className="text-4xl md:text-5xl font-bold text-white mb-4"
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.5 }}
                        >
                            {datasetsT('title')}
                        </motion.h1>

                        <motion.p
                            className="text-xl text-white/80 mb-8 max-w-2xl"
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.5, delay: 0.2 }}
                        >
                            {datasetsT('subtitle')}
                        </motion.p>
                    </div>
                    
                    {/* Tabs positioned on the hero background */}
                    <motion.div 
                        variants={tabsVariants}
                        className="absolute bottom-8 left-0 right-0 z-20"
                    >
                        <div className="container mx-auto px-6">
                            <div className="flex justify-center">
                                <div className={`flex bg-white/20 backdrop-blur-md border border-white/30 rounded-2xl p-2 shadow-2xl ${isRtl ? 'flex-row-reverse' : ''}`}>
                                    {tabs.map((tab) => {
                                        const Icon = tab.icon;
                                        const isActive = activeTab === tab.id;
                                        
                                        return (
                                            <Button
                                                key={tab.id}
                                                onClick={() => setActiveTab(tab.id)}
                                                variant="ghost"
                                                className={`
                                                    relative px-8 py-4 rounded-xl font-semibold text-base transition-all duration-300 
                                                    ${isActive 
                                                        ? 'bg-white text-[#003874] shadow-lg scale-105' 
                                                        : 'text-white hover:bg-white/10 hover:text-white'
                                                    }
                                                    ${isRtl ? 'flex-row-reverse' : ''}
                                                `}
                                            >
                                                <Icon className={`h-5 w-5 ${isRtl ? 'ml-3' : 'mr-3'}`} />
                                                {datasetsT(tab.labelKey)}
                                                
                                                {/* Active indicator */}
                                                {isActive && (
                                                    <motion.div
                                                        layoutId="activeTab"
                                                        className="absolute inset-0 bg-white rounded-xl -z-10"
                                                        initial={false}
                                                        transition={{
                                                            type: "spring",
                                                            stiffness: 500,
                                                            damping: 30
                                                        }}
                                                    />
                                                )}
                                            </Button>
                                        );
                                    })}
                                </div>
                            </div>
                        </div>
                    </motion.div>
                </div>
            </motion.div>

            {/* Content Section */}
            <div className="container mx-auto px-6 py-12">
                <AnimatePresence mode="wait">
                    {renderContent()}
                </AnimatePresence>
            </div>

            {/* Background decoration */}
            <div className="fixed inset-0 pointer-events-none overflow-hidden -z-10">
                <div className="absolute top-20 left-10 w-64 h-64 bg-blue-500/5 rounded-full blur-3xl"></div>
                <div className="absolute bottom-20 right-10 w-96 h-96 bg-indigo-500/5 rounded-full blur-3xl"></div>
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-128 h-128 bg-emerald-500/3 rounded-full blur-3xl"></div>
            </div>
        </motion.div>
    );
} 
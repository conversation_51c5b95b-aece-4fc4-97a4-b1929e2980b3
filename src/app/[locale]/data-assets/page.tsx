"use client";

import { motion } from "framer-motion";
import { useTranslations } from "next-intl";
import { useParams, useRouter } from "next/navigation";
import { HeroHeader } from "@/components/shared/HeroHeader";
import { AssetCard, AssetType } from "@/components/ui/data-assets/AssetCard";

// Professional animation variants
const pageVariants = {
    hidden: { 
        opacity: 0
    },
    visible: {
        opacity: 1,
        transition: {
            duration: 0.6,
            staggerChildren: 0.1
        }
    }
};

const heroVariants = {
    hidden: { 
        opacity: 0,
        y: -30
    },
    visible: {
        opacity: 1,
        y: 0,
        transition: {
            type: "spring",
            stiffness: 260,
            damping: 20,
            duration: 0.8
        }
    }
};

const cardsContainerVariants = {
    hidden: { 
        opacity: 0
    },
    visible: {
        opacity: 1,
        transition: {
            duration: 0.6,
            delay: 0.3,
            staggerChildren: 0.2
        }
    }
};

// Asset types in the specified order
const assetTypes: AssetType[] = [
    'systems',
    'databases', 
    'datasets',
    'structuredFiles',
    'apis',
    'dashboards'
];

export default function DataAssetsPage() {
    const heroT = useTranslations('heroHeader');
    const params = useParams();
    const router = useRouter();
    const locale = params.locale as string;
    const isRtl = locale === 'ar';

    const handleAssetClick = (assetType: AssetType) => {
        // Navigate to specific asset management page using standard Next.js routing
        const targetUrl = `/${locale}/data-assets/${assetType}`;
        console.log('Navigating to:', targetUrl);
        router.push(targetUrl);
    };

    return (
        <motion.div
            variants={pageVariants}
            initial="hidden"
            animate="visible"
            className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100"
        >
            {/* Enhanced Hero Section */}
            <motion.div variants={heroVariants}>
                <HeroHeader
                    title={heroT('dataTitle')}
                    description={heroT('dataSubtitle')}
                    isRTL={isRtl}
                    height="auto"
                />
            </motion.div>

            {/* Asset Cards Section */}
            <motion.div 
                variants={cardsContainerVariants}
                className="container mx-auto px-6 py-16 relative z-10"
            >
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
                    {assetTypes.map((assetType, index) => (
                        <motion.div
                            key={assetType}
                            variants={{
                                hidden: { opacity: 0, y: 50, scale: 0.9 },
                                visible: {
                                    opacity: 1,
                                    y: 0,
                                    scale: 1,
                                    transition: {
                                        type: "spring",
                                        stiffness: 300,
                                        damping: 30,
                                        delay: index * 0.1
                                    }
                                }
                            }}
                        >
                            <AssetCard 
                                type={assetType}
                                locale={locale}
                                onClick={() => handleAssetClick(assetType)}
                                isRTL={isRtl}
                            />
                        </motion.div>
                    ))}
                </div>
            </motion.div>

            {/* Background decoration */}
            <div className="fixed inset-0 pointer-events-none overflow-hidden -z-10">
                <div className="absolute top-20 left-10 w-64 h-64 bg-blue-500/5 rounded-full blur-3xl"></div>
                <div className="absolute bottom-20 right-10 w-96 h-96 bg-indigo-500/5 rounded-full blur-3xl"></div>
                <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-128 h-128 bg-emerald-500/3 rounded-full blur-3xl"></div>
            </div>
        </motion.div>
    );
} 
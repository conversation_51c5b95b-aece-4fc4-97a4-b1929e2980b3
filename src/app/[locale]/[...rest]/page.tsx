'use client';

import { useEffect } from 'react';
import { useLocale } from 'next-intl';
import { Loader } from '@/components/shared/Loader';
import { useParams } from 'next/navigation';

export default function CatchAllPage() {
  const locale = useLocale();
  const params = useParams<{ rest: string[] }>();

  useEffect(() => {
    // Ensure params and params.rest are available
    if (params && params.rest) {
      // Check for project-selection/dashboard pattern and redirect
      if (params.rest.length >= 2 && params.rest[0] === 'project-selection' && params.rest[1] === 'dashboard') {
        window.location.href = `/${locale}/dashboard`;
        return;
      }
    }


  }, [params, locale]);

  return (
    <div className="h-screen flex items-center justify-center ">
      <Loader size="lg" variant="primary" />
    </div>
  );
}

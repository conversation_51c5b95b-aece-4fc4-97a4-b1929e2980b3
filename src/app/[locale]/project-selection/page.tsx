"use client";

import { useState, useEffect, useRef } from "react";
import { motion } from "framer-motion";
import { useTranslations } from "next-intl";
import { useRoleGuard } from "@/hooks/useRoleGuard";
import { collection, query, where, getDocs } from "firebase/firestore";
import { db } from "@/lib/firebaseClient";
import { Project } from "@/types";
import { usePathname, useRouter } from "@/i18n/navigation";
import { ChevronLeft, ChevronRight, Lightbulb, Lock, LayoutDashboard, Sparkles, PenTool, Megaphone, BarChart3, Braces } from "lucide-react";
import Image from "next/image";
import { Loader } from "@/components/shared/Loader";
import { LanguageSwitcher } from "@/components/shared/language-switcher";

// Animation variants
const fadeIn = {
    hidden: { opacity: 0, y: 20 },
    visible: {
        opacity: 1,
        y: 0,
        transition: { duration: 0.5 }
    }
};

const staggerContainer = {
    hidden: { opacity: 0 },
    visible: {
        opacity: 1,
        transition: {
            staggerChildren: 0.1
        }
    }
};

const moduleCardVariant = {
    hidden: { opacity: 0, y: 50 },
    visible: {
        opacity: 1,
        y: 0,
        transition: {
            type: "spring",
            stiffness: 100,
            damping: 15
        }
    },
    hover: {
        y: -15,
        boxShadow: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
        transition: {
            type: "spring",
            stiffness: 400,
            damping: 10
        }
    },
    tap: {
        scale: 0.98,
        transition: {
            type: "spring",
            stiffness: 400,
            damping: 17
        }
    }
};

const floatingAnimation = {
    initial: { y: 0 },
    animate: {
        y: [0, -12, 0],
        transition: {
            duration: 5,
            repeat: Infinity,
            repeatType: "reverse" as const,
            ease: "easeInOut"
        }
    }
}

export default function ProjectSelectionPage() {
    const [projects, setProjects] = useState<Project[]>([]);
    const [loading, setLoading] = useState(true);
    const [contentReady, setContentReady] = useState(false);
    const sliderRef = useRef<HTMLDivElement>(null);

    const t = useTranslations('ProjectSelection');
    const { isAuthorized, isLoading, user } = useRoleGuard('Both');
    const router = useRouter();
    const pathname = usePathname();

    // Get current language from URL
    const lang = pathname.split("/")[1] as 'ar' | 'en';
    const isConsultant = user?.role === 'Consultant';

    // Fetch assigned projects from Firestore
    useEffect(() => {
        const fetchProjects = async () => {
            if (!isLoading && isAuthorized && user?.assignedProjectIds) {
                try {
                    const assignedProjectIds = user.assignedProjectIds || [];
                    if (assignedProjectIds.length === 0) {
                        setLoading(false);
                        setContentReady(true);
                        return;
                    }

                    // Try to fetch projects with retry logic
                    let fetchedProjects: Project[] = [];
                    let attempts = 0;
                    const maxAttempts = 3;

                    while (attempts < maxAttempts) {
                        try {
                            const projectsRef = collection(db, 'projects');
                            const projectsQuery = query(
                                projectsRef,
                                where('__name__', 'in', assignedProjectIds)
                            );

                            const querySnapshot = await getDocs(projectsQuery);
                            fetchedProjects = [];

                            querySnapshot.forEach((doc) => {
                                fetchedProjects.push({
                                    id: doc.id,
                                    ...doc.data() as Omit<Project, 'id'>
                                });
                            });

                            // Break out of retry loop if successful
                            break;
                        } catch (retryError) {
                            console.error(`Error fetching projects (attempt ${attempts + 1}/${maxAttempts}):`, retryError);
                            attempts++;

                            if (attempts >= maxAttempts) {
                                throw retryError; // Re-throw if all attempts failed
                            }

                            // Wait before retrying
                            await new Promise(resolve => setTimeout(resolve, 500));
                        }
                    }

                    setProjects(fetchedProjects);
                } catch (error) {
                    console.error('Error fetching projects:', error);
                    // Continue anyway to show the UI with fallback content
                } finally {
                    setLoading(false);
                    // Short delay for smoother animations
                    setTimeout(() => {
                        setContentReady(true);
                    }, 100);
                }
            }
        };

        fetchProjects();
    }, [isLoading, isAuthorized, user]);

    // Scroll slider functions
    const scrollLeft = () => {
        if (sliderRef.current) {
            sliderRef.current.scrollBy({ left: -450, behavior: 'smooth' });
        }
    };

    const scrollRight = () => {
        if (sliderRef.current) {
            sliderRef.current.scrollBy({ left: 450, behavior: 'smooth' });
        }
    };

    // Loading state
    if (isLoading || loading || !contentReady) {
        return (
            <div className="h-screen flex items-center justify-center from-slate-800 via-slate-900 to-slate-950">
                <Loader size="lg" variant="primary" />
            </div>
        );
    }

    // Get status colors and icon for project
    const getStatusGradient = (status: string) => {
        switch (status) {
            case 'completed':
                return { from: "from-[#48D3A5]", to: "to-[#302C64]" };
            case 'in-progress':
                return { from: "from-[#003874]", to: "to-[#2D8DC6]" };
            default: // on-holding or other
                return { from: "from-[#31326A]", to: "to-[#003874]" };
        }
    };

    // Format project status for display
    const getStatusTranslation = (status: string) => {
        switch (status) {
            case 'on-holding':
                return t('onHolding');
            case 'in-progress':
                return t('inProgress');
            case 'completed':
                return t('completed');
            default:
                return status;
        }
    };

    return (
        <motion.div
            initial="hidden"
            animate="visible"
            variants={fadeIn}
            className="min-h-screen bg-gradient-to-br from-slate-800 via-slate-900 to-slate-950 pt-12 pb-20"
        >
            {/* Language switcher in top-right corner */}
            <div className={`absolute top-4 text-white text-2xl ${lang === 'ar' ? 'left-4' : 'right-4'} z-20`}>
                <LanguageSwitcher />
            </div>

            {/* Header */}
            <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="text-center mb-16"
            >
                <div className="flex justify-center mb-8">
                    <Image
                        src="/assets/DevFlowLogo.png"
                        alt="DevFlow Logo"
                        width={220}
                        height={80}
                        priority
                        className="drop-shadow-lg"
                    />
                </div>
                <h1 className={`text-5xl md:text-6xl font-bold text-white mb-6 ${lang === 'ar' ? 'font-arabic' : ''}`}>
                    {t('welcome')} <span className="text-transparent bg-clip-text bg-gradient-to-r from-indigo-400 to-purple-500">DevFlow</span>
                </h1>
                <p className={`text-xl text-gray-300 max-w-3xl mx-auto ${lang === 'ar' ? 'font-arabic' : ''}`}>
                    {t('subtitle')}
                </p>
            </motion.div>

            {/* Projects Section */}
            {projects.length > 0 && (
                <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 mb-16">
                    <h2 className={`text-3xl font-bold text-white mb-6 ${lang === 'ar' ? 'font-arabic text-right' : ''}`}>
                        {isConsultant ? t('consultantWelcome') : t('clientWelcome')}
                    </h2>
                    <p className={`text-xl mb-8 text-gray-300 ${lang === 'ar' ? 'font-arabic text-right' : ''}`}>
                        {isConsultant ? t('consultantDescription') : t('clientDescription')}
                    </p>

                    <div className="flex flex-wrap gap-6 mb-12">
                        {projects.map(project => {
                            const { from, to } = getStatusGradient(project.status);
                            return (
                                <motion.div
                                    key={project.id}
                                    variants={moduleCardVariant}
                                    initial="hidden"
                                    animate="visible"
                                    whileHover="hover"
                                    whileTap="tap"
                                    className="relative overflow-hidden rounded-2xl cursor-pointer w-full md:w-[400px] lg:w-[450px]"
                                    onClick={() => {
                                        // Store the selected project ID in localStorage
                                        localStorage.setItem('currentProjectId', project.id);
                                        router.push(`/${lang}/dashboard`);
                                    }}
                                >
                                    <div className={`bg-gradient-to-br ${from} ${to} p-8 h-full min-h-[320px] relative overflow-hidden`}>
                                        {/* Decorative elements */}
                                        <div className="absolute top-0 right-0 w-64 h-64 rounded-full opacity-30 bg-white transform translate-x-1/3 -translate-y-1/3"></div>
                                        <div className="absolute bottom-0 left-0 w-32 h-32 rounded-full opacity-20 bg-white transform -translate-x-1/3 translate-y-1/3"></div>

                                        <div className="relative z-10 flex flex-col h-full">
                                            <h2 className={`text-3xl font-bold text-white mb-4 ${lang === 'ar' ? 'font-arabic' : ''}`}>
                                                {lang === 'ar' ? project.name.ar : project.name.en}
                                            </h2>
                                            <p className={`text-white/90 mb-6 ${lang === 'ar' ? 'font-arabic' : ''}`}>
                                                {getStatusTranslation(project.status)}
                                            </p>

                                            <div className="space-y-2 mb-auto">
                                                <div className={`text-white/80 ${lang === 'ar' ? 'font-arabic' : ''}`}>
                                                    {t('startDate')}: {project.startDate}
                                                </div>
                                                <div className={`text-white/80 ${lang === 'ar' ? 'font-arabic' : ''}`}>
                                                    {t('deadline')}: {project.projectDeadline}
                                                </div>
                                                {project.frameworkId && (
                                                    <div className={`text-white/90 mt-2 font-medium ${lang === 'ar' ? 'font-arabic' : ''}`}>
                                                        {t('framework')}: {project.frameworkId}
                                                    </div>
                                                )}
                                            </div>

                                            {/* Action button */}
                                            <button className={`mt-8 w-full rounded-lg bg-white/20 backdrop-blur-sm text-white hover:bg-white/30 py-3 px-6 transition-all shadow-md flex items-center justify-center gap-2 font-medium ${lang === 'ar' ? 'font-arabic' : ''}`}>
                                                <span>{t('viewProject')}</span>
                                                <ChevronRight size={18} className={lang === 'ar' ? 'transform rotate-180' : ''} />
                                            </button>
                                        </div>
                                    </div>
                                </motion.div>
                            );
                        })}
                    </div>
                </div>
            )}

            {/* No Projects Case */}
            {projects.length === 0 && (
                <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 mb-16">
                    <div className="bg-white/10 backdrop-blur-sm p-8 rounded-lg text-center">
                        <p className={`text-xl text-white ${lang === 'ar' ? 'font-arabic' : ''}`}>
                            {t('noProjects')}
                        </p>
                    </div>
                </div>
            )}

            {/* Coming Soon Section */}
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-8">
                <h2 className={`text-3xl font-bold text-white mb-4 ${lang === 'ar' ? 'font-arabic text-right' : ''}`}>
                    {t('comingSoon')}
                </h2>
                <p className={`text-xl text-gray-300 mb-8 ${lang === 'ar' ? 'font-arabic text-right' : ''}`}>
                    {t('comingSoonModules')}
                </p>
            </div>

            {/* Coming Soon Modules Section */}
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 mb-12 relative">
                {/* Slider Arrows */}
                <div className="hidden md:block">
                    <button
                        onClick={scrollLeft}
                        className="absolute left-0 top-1/2 -translate-y-1/2 -ml-5 z-10 bg-white/10 backdrop-blur-md hover:bg-white/20 rounded-full p-3 text-white shadow-lg transition-all"
                        aria-label="Scroll left"
                    >
                        <ChevronLeft size={24} className={lang === 'ar' ? 'transform rotate-180' : ''} />
                    </button>
                    <button
                        onClick={scrollRight}
                        className="absolute right-0 top-1/2 -translate-y-1/2 -mr-5 z-10 bg-white/10 backdrop-blur-md hover:bg-white/20 rounded-full p-3 text-white shadow-lg transition-all"
                        aria-label="Scroll right"
                    >
                        <ChevronRight size={24} className={lang === 'ar' ? 'transform rotate-180' : ''} />
                    </button>
                </div>

                <motion.div
                    ref={sliderRef}
                    variants={staggerContainer}
                    className="flex flex-nowrap gap-6 overflow-x-auto pb-8 snap-x snap-mandatory"
                    style={{ scrollbarWidth: 'none' }}
                >
                    {/* Application Planning Module Card */}
                    <motion.div
                        variants={moduleCardVariant}
                        initial="hidden"
                        animate="visible"
                        className="relative overflow-hidden rounded-2xl flex-shrink-0 snap-center w-full md:w-[400px] lg:w-[450px] opacity-70"
                    >
                        <div className="bg-gradient-to-br from-indigo-600 via-indigo-700 to-purple-700 p-8 h-full min-h-[480px] relative overflow-hidden">
                            {/* Work in Progress Badge */}
                            <div className="absolute top-4 right-4 bg-black/50 backdrop-blur-sm px-4 py-2 rounded-full z-20 text-white font-semibold flex items-center">
                                <Lock className="h-4 w-4 mr-2" />
                                {t('workInProgress')}
                            </div>

                            {/* Dark overlay */}
                            <div className="absolute inset-0 bg-black/30 backdrop-blur-[2px] z-10"></div>

                            {/* Decorative elements */}
                            <div className="absolute top-0 right-0 w-64 h-64 rounded-full opacity-30 bg-white transform translate-x-1/3 -translate-y-1/3"></div>
                            <div className="absolute bottom-0 left-0 w-32 h-32 rounded-full opacity-20 bg-white transform -translate-x-1/3 translate-y-1/3"></div>

                            <div className="relative z-9 flex flex-col h-full">
                                <div className="mb-8">
                                    <div className="inline-flex items-center justify-center p-3 rounded-xl bg-white/20 backdrop-blur-sm mb-6">
                                        <LayoutDashboard className="h-10 w-10 text-white" />
                                    </div>
                                    <h2 className={`text-3xl font-bold text-white mb-4 ${lang === 'ar' ? 'font-arabic' : ''}`}>
                                        {t('applicationPlanning')}
                                    </h2>
                                    <p className={`text-white/90 text-lg mb-6 ${lang === 'ar' ? 'font-arabic' : ''}`}>
                                        {t('applicationPlanningDesc')}
                                    </p>
                                </div>

                                {/* Features list */}
                                <div className="space-y-4 mb-auto">
                                    <div className="flex items-start text-white/90">
                                        <div className="flex-shrink-0 mt-1">
                                            <Sparkles className="h-5 w-5 text-yellow-300" />
                                        </div>
                                        <p className={`ml-3 ${lang === 'ar' ? 'font-arabic' : ''}`}>AI-generated user stories and epics</p>
                                    </div>
                                    <div className="flex items-start text-white/90">
                                        <div className="flex-shrink-0 mt-1">
                                            <Sparkles className="h-5 w-5 text-yellow-300" />
                                        </div>
                                        <p className={`ml-3 ${lang === 'ar' ? 'font-arabic' : ''}`}>Visual user flow diagrams</p>
                                    </div>
                                    <div className="flex items-start text-white/90">
                                        <div className="flex-shrink-0 mt-1">
                                            <Sparkles className="h-5 w-5 text-yellow-300" />
                                        </div>
                                        <p className={`ml-3 ${lang === 'ar' ? 'font-arabic' : ''}`}>Automated wireframe generation</p>
                                    </div>
                                    <div className="flex items-start text-white/90">
                                        <div className="flex-shrink-0 mt-1">
                                            <Sparkles className="h-5 w-5 text-yellow-300" />
                                        </div>
                                        <p className={`ml-3 ${lang === 'ar' ? 'font-arabic' : ''}`}>Data model & ERD visualization</p>
                                    </div>
                                </div>

                                {/* Floating element */}
                                <motion.div
                                    className="absolute bottom-8 right-8"
                                    variants={floatingAnimation}
                                    initial="initial"
                                    animate="animate"
                                >
                                    <Braces className="h-28 w-28 text-white/10" />
                                </motion.div>

                                {/* Action button */}
                                <button className={`mt-8 w-full rounded-lg bg-white/50 text-indigo-700 py-3 px-6 transition-all shadow-md flex items-center justify-center gap-2 font-medium cursor-not-allowed ${lang === 'ar' ? 'font-arabic' : ''}`}>
                                    <span>{t('comingSoon')}</span>
                                    <Lock size={16} />
                                </button>
                            </div>
                        </div>
                    </motion.div>

                    {/* Strategy Drafting Module Card */}
                    <motion.div
                        variants={moduleCardVariant}
                        initial="hidden"
                        animate="visible"
                        className="relative overflow-hidden rounded-2xl flex-shrink-0 snap-center w-full md:w-[400px] lg:w-[450px] opacity-70"
                    >
                        <div className="bg-gradient-to-br from-emerald-600 via-teal-700 to-cyan-700 p-8 h-full min-h-[480px] relative overflow-hidden">
                            {/* Work in Progress Badge */}
                            <div className="absolute top-4 right-4 bg-black/50 backdrop-blur-sm px-4 py-2 rounded-full z-20 text-white font-semibold flex items-center">
                                <Lock className="h-4 w-4 mr-2" />
                                {t('workInProgress')}
                            </div>

                            {/* Dark overlay */}
                            <div className="absolute inset-0 bg-black/30 backdrop-blur-[2px] z-10"></div>

                            {/* Decorative elements */}
                            <div className="absolute top-0 right-0 w-64 h-64 rounded-full opacity-30 bg-white transform translate-x-1/3 -translate-y-1/3"></div>
                            <div className="absolute bottom-0 left-0 w-32 h-32 rounded-full opacity-20 bg-white transform -translate-x-1/3 translate-y-1/3"></div>

                            <div className="relative z-9 flex flex-col h-full">
                                <div className="mb-8">
                                    <div className="inline-flex items-center justify-center p-3 rounded-xl bg-white/20 backdrop-blur-sm mb-6">
                                        <PenTool className="h-10 w-10 text-white" />
                                    </div>
                                    <h2 className={`text-3xl font-bold text-white mb-4 ${lang === 'ar' ? 'font-arabic' : ''}`}>
                                        {t('strategyModule')}
                                    </h2>
                                    <p className={`text-white/90 text-lg mb-6 ${lang === 'ar' ? 'font-arabic' : ''}`}>
                                        {t('strategyModuleDesc')}
                                    </p>
                                </div>

                                {/* Features list */}
                                <div className="space-y-4 mb-auto">
                                    <div className="flex items-start text-white/90">
                                        <div className="flex-shrink-0 mt-1">
                                            <Sparkles className="h-5 w-5 text-yellow-300" />
                                        </div>
                                        <p className={`ml-3 ${lang === 'ar' ? 'font-arabic' : ''}`}>Business model canvas generation</p>
                                    </div>
                                    <div className="flex items-start text-white/90">
                                        <div className="flex-shrink-0 mt-1">
                                            <Sparkles className="h-5 w-5 text-yellow-300" />
                                        </div>
                                        <p className={`ml-3 ${lang === 'ar' ? 'font-arabic' : ''}`}>Competitive analysis frameworks</p>
                                    </div>
                                    <div className="flex items-start text-white/90">
                                        <div className="flex-shrink-0 mt-1">
                                            <Sparkles className="h-5 w-5 text-yellow-300" />
                                        </div>
                                        <p className={`ml-3 ${lang === 'ar' ? 'font-arabic' : ''}`}>Go-to-market strategy planning</p>
                                    </div>
                                    <div className="flex items-start text-white/90">
                                        <div className="flex-shrink-0 mt-1">
                                            <Sparkles className="h-5 w-5 text-yellow-300" />
                                        </div>
                                        <p className={`ml-3 ${lang === 'ar' ? 'font-arabic' : ''}`}>Product roadmap visualization</p>
                                    </div>
                                </div>

                                {/* Floating element */}
                                <motion.div
                                    className="absolute bottom-8 right-8"
                                    variants={floatingAnimation}
                                    initial="initial"
                                    animate="animate"
                                >
                                    <Lightbulb className="h-28 w-28 text-white/10" />
                                </motion.div>

                                {/* Action button */}
                                <button className={`mt-8 w-full rounded-lg bg-white/50 text-teal-700 py-3 px-6 transition-all shadow-md flex items-center justify-center gap-2 font-medium cursor-not-allowed ${lang === 'ar' ? 'font-arabic' : ''}`}>
                                    <span>{t('comingSoon')}</span>
                                    <Lock size={16} />
                                </button>
                            </div>
                        </div>
                    </motion.div>

                    {/* Marketing Strategy Module Card */}
                    <motion.div
                        variants={moduleCardVariant}
                        initial="hidden"
                        animate="visible"
                        className="relative overflow-hidden rounded-2xl flex-shrink-0 snap-center w-full md:w-[400px] lg:w-[450px] opacity-70"
                    >
                        <div className="bg-gradient-to-br from-blue-400 via-blue-600 to-slate-900 p-8 h-full min-h-[480px] relative overflow-hidden">
                            {/* Work in Progress Badge */}
                            <div className="absolute top-4 right-4 bg-black/50 backdrop-blur-sm px-4 py-2 rounded-full z-20 text-white font-semibold flex items-center">
                                <Lock className="h-4 w-4 mr-2" />
                                {t('workInProgress')}
                            </div>

                            {/* Dark overlay */}
                            <div className="absolute inset-0 bg-black/30 backdrop-blur-[2px] z-10"></div>

                            {/* Decorative elements - different pattern for marketing */}
                            <div className="absolute top-0 right-0 w-64 h-64 rounded-full opacity-30 bg-white transform translate-x-1/3 -translate-y-1/3"></div>
                            <div className="absolute top-1/2 left-1/4 w-48 h-48 rounded-full opacity-10 bg-white"></div>
                            <div className="absolute bottom-0 left-0 w-32 h-32 rounded-full opacity-20 bg-white transform -translate-x-1/3 translate-y-1/3"></div>

                            <div className="relative z-9 flex flex-col h-full">
                                <div className="mb-8">
                                    <div className="inline-flex items-center justify-center p-3 rounded-xl bg-white/20 backdrop-blur-sm mb-6">
                                        <Megaphone className="h-10 w-10 text-white" />
                                    </div>
                                    <h2 className={`text-3xl font-bold text-white mb-4 ${lang === 'ar' ? 'font-arabic' : ''}`}>
                                        {t('marketingStrategy')}
                                    </h2>
                                    <p className={`text-white/90 text-lg mb-6 ${lang === 'ar' ? 'font-arabic' : ''}`}>
                                        {t('marketingStrategyDesc')}
                                    </p>
                                </div>

                                {/* Features list */}
                                <div className="space-y-4 mb-auto">
                                    <div className="flex items-start text-white/90">
                                        <div className="flex-shrink-0 mt-1">
                                            <Sparkles className="h-5 w-5 text-yellow-300" />
                                        </div>
                                        <p className={`ml-3 ${lang === 'ar' ? 'font-arabic' : ''}`}>Campaign planning and execution</p>
                                    </div>
                                    <div className="flex items-start text-white/90">
                                        <div className="flex-shrink-0 mt-1">
                                            <Sparkles className="h-5 w-5 text-yellow-300" />
                                        </div>
                                        <p className={`ml-3 ${lang === 'ar' ? 'font-arabic' : ''}`}>Performance analytics dashboard</p>
                                    </div>
                                    <div className="flex items-start text-white/90">
                                        <div className="flex-shrink-0 mt-1">
                                            <Sparkles className="h-5 w-5 text-yellow-300" />
                                        </div>
                                        <p className={`ml-3 ${lang === 'ar' ? 'font-arabic' : ''}`}>Audience targeting and segmentation</p>
                                    </div>
                                    <div className="flex items-start text-white/90">
                                        <div className="flex-shrink-0 mt-1">
                                            <Sparkles className="h-5 w-5 text-yellow-300" />
                                        </div>
                                        <p className={`ml-3 ${lang === 'ar' ? 'font-arabic' : ''}`}>Content calendar and scheduling</p>
                                    </div>
                                </div>

                                {/* Floating element - different for marketing */}
                                <motion.div
                                    className="absolute bottom-8 right-8"
                                    variants={floatingAnimation}
                                    initial="initial"
                                    animate="animate"
                                >
                                    <BarChart3 className="h-28 w-28 text-white/10" />
                                </motion.div>

                                {/* Action button */}
                                <button className={`mt-8 w-full rounded-lg bg-white/50 text-blue-700 py-3 px-6 transition-all shadow-md flex items-center justify-center gap-2 font-medium cursor-not-allowed ${lang === 'ar' ? 'font-arabic' : ''}`}>
                                    <span>{t('comingSoon')}</span>
                                    <Lock size={16} />
                                </button>
                            </div>
                        </div>
                    </motion.div>
                </motion.div>

                {/* Slider Indicator */}
                <div className="flex justify-center gap-2 mt-6">
                    <div className="w-8 h-1 rounded-full bg-white"></div>
                    <div className="w-8 h-1 rounded-full bg-white/30"></div>
                    <div className="w-8 h-1 rounded-full bg-white/30"></div>
                </div>
            </div>
        </motion.div>
    );
} 
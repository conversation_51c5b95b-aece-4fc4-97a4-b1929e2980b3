'use client';

import { useTranslations, useLocale } from 'next-intl';
import { motion } from 'framer-motion';
import { AlertCircle, ArrowLeft } from 'lucide-react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { useAuthContext } from '@/context/AuthContext';

export default function UnauthorizedPage() {
    const t = useTranslations('Auth');
    const locale = useLocale();
    const router = useRouter();
    const isRtl = locale === 'ar';
    const fontFamily = isRtl ? 'var(--font-cairo)' : 'var(--font-rubik)';
    const { logout } = useAuthContext();

    const handleReturnToLogin = async () => {
        await logout();
        router.push(`/${locale}/login`);
    };

    return (
        <div
            className="flex min-h-screen bg-white flex-col items-center justify-center px-6 py-12"
            dir={isRtl ? 'rtl' : 'ltr'}
            style={{ fontFamily }}
        >
            <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="w-full max-w-md text-center"
            >
                <div className="mb-6 flex justify-center">
                    <Image
                        src="/assets/DevFlowLogo.png"
                        alt="DevFlow Logo"
                        width={150}
                        height={53}
                        priority
                        className="h-20 w-auto object-contain"
                    />
                </div>

                <div className="flex justify-center mb-6">
                    <div className="rounded-full bg-red-100 p-3">
                        <AlertCircle className="h-8 w-8 text-red-600" />
                    </div>
                </div>

                <h1 className="text-2xl font-bold text-gray-900 mb-4">
                    {t('accessDenied')}
                </h1>

                <p className="text-gray-600 mb-8">
                    {t('unauthorizedAccess')}
                </p>

                <motion.button
                    onClick={handleReturnToLogin}
                    whileHover={{ scale: 1.03 }}
                    whileTap={{ scale: 0.98 }}
                    className="flex items-center justify-center gap-2 mx-auto bg-gradient-to-r from-[#45cab0] to-[#2dd3c5] text-white px-6 py-2 rounded-lg shadow-sm hover:shadow-md transition-all duration-200"
                >
                    <ArrowLeft className={`h-4 w-4 ${isRtl ? 'ml-1 rotate-180' : 'mr-1'}`} />
                    {t('returnToLogin')}
                </motion.button>
            </motion.div>
        </div>
    );
} 
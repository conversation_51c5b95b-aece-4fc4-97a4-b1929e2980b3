"use client";

import { useState } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { useTranslations } from 'next-intl';
import { ArrowLeft, Shield, Target, Calendar, Building2, TrendingUp, CheckCircle2 } from 'lucide-react';
import { HeroHeader } from '@/components/shared/HeroHeader';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ProfessionalTabs } from '@/components/ui/assessment-cards/ProfessionalTabs';
import { AssessmentOverview } from '@/components/ui/assessment-cards/AssessmentOverview';
import { DomainDetails } from '@/components/ui/assessment-cards/DomainDetails';
import { useAssessmentDetails } from '@/hooks/useAssessmentDetails';
import { useDomainAverages } from '@/hooks/useDomainAverages';
import { useAssessmentCriteria } from '@/hooks/useAssessmentCriteria';

// Animation variants
const pageVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      staggerChildren: 0.1
    }
  }
};

const contentVariants = {
  hidden: { opacity: 0, y: 30 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5
    }
  }
};

const heroStatsVariants = {
  hidden: { opacity: 0, scale: 0.8 },
  visible: {
    opacity: 1,
    scale: 1,
    transition: {
      duration: 0.6,
      delay: 0.4
    }
  }
};

export default function AssessmentDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const locale = params.locale as string;
  const projectId = params.projectId as string;
  const assessmentId = params.assessmentId as string;
  const isRtl = locale === 'ar';

  const t = useTranslations('AssessmentDetails');
  const [_activeTab, setActiveTab] = useState('overview');

  const {
    assessment,
    framework,
    domains,
    loading,
    error,
    domainsLoaded,
    loadDomains
  } = useAssessmentDetails(projectId, assessmentId);

  // Fetch domain averages and assessment criteria for real KPIs
  const { 
    domainAverages, 
    loading: averagesLoading 
  } = useDomainAverages(projectId, assessmentId);

  const { 
    assessmentCriteria, 
    loading: criteriaLoading 
  } = useAssessmentCriteria(framework?.id || '');

  // Calculate weighted compliance KPIs for hero section
  const calculateHeroKPIs = () => {
    const domains = Object.entries(domainAverages);
    
    if (domains.length === 0) {
      return {
        weightedCompliance: 0,
        totalSpecifications: 0,
        ratedSpecifications: 0,
        completionRate: 0,
        totalWeight: 0,
        domainsWithWeights: 0
      };
    }

    const totalSpecifications = domains.reduce((sum, [_, domain]) => sum + domain.totalSpecifications, 0);
    const ratedSpecifications = domains.reduce((sum, [_, domain]) => sum + domain.ratedSpecifications, 0);
    const completionRate = totalSpecifications > 0 ? (ratedSpecifications / totalSpecifications) * 100 : 0;
    
    // Calculate weighted compliance using domain weights
    let weightedCompliance = 0;
    let totalWeight = 0;
    let domainsWithWeights = 0;
    
    domains.forEach(([domainId, domain]) => {
      // Use weight from domain averages if available, otherwise look up in assessment criteria
      const weight = domain.weight || assessmentCriteria?.domainWeights?.find(dw => dw.domainId === domain.domainId)?.weight || 0;
      if (weight > 0) {
        weightedCompliance += domain.average * (weight / 100);
        totalWeight += weight;
        domainsWithWeights++;
      }
    });
    
    // If not all domains have weights, fall back to equal weighting
    if (totalWeight === 0) {
      weightedCompliance = domains.reduce((sum, [_, domain]) => sum + domain.average, 0) / domains.length;
      totalWeight = 100; // Assume 100% for display purposes
      domainsWithWeights = domains.length;
    }

    return {
      weightedCompliance,
      totalSpecifications,
      ratedSpecifications,
      completionRate,
      totalWeight,
      domainsWithWeights
    };
  };

  const heroKPIs = calculateHeroKPIs();

  // Get compliance level for hero display
  const getComplianceLevel = (percentage: number) => {
    if (!assessmentCriteria?.levels) {
      if (percentage >= 80) return { label: isRtl ? 'ملتزم' : 'Compliant', color: 'bg-emerald-100 text-emerald-800 border-emerald-200' };
      if (percentage >= 60) return { label: isRtl ? 'ملتزم جزئياً' : 'Partially Compliant', color: 'bg-blue-100 text-blue-800 border-blue-200' };
      if (percentage >= 40) return { label: isRtl ? 'ملتزم محدود' : 'Limited Compliance', color: 'bg-amber-100 text-amber-800 border-amber-200' };
      return { label: isRtl ? 'غير ملتزم' : 'Non-Compliant', color: 'bg-red-100 text-red-800 border-red-200' };
    }

    const levels = Object.values(assessmentCriteria.levels).sort((a, b) => b.value - a.value);
    const matchingLevel = levels.find(level => percentage >= level.value);
    
    if (matchingLevel) {
      const label = typeof matchingLevel.label === 'object' 
        ? (matchingLevel.label[locale] || matchingLevel.label.en)
        : matchingLevel.label;
      
      // Determine color based on percentage
      let color = 'bg-gray-100 text-gray-800 border-gray-200';
      if (percentage >= 80) color = 'bg-emerald-100 text-emerald-800 border-emerald-200';
      else if (percentage >= 60) color = 'bg-blue-100 text-blue-800 border-blue-200';
      else if (percentage >= 40) color = 'bg-amber-100 text-amber-800 border-amber-200';
      else color = 'bg-red-100 text-red-800 border-red-200';
      
      return { label, color };
    }
    
    return { label: isRtl ? 'غير محدد' : 'Not Determined', color: 'bg-gray-100 text-gray-800 border-gray-200' };
  };

  // Format date
  const formatDate = (timestamp: { toDate?: () => Date } | string | Date | null | undefined) => {
    if (!timestamp) return '-';
    try {
      const date = (timestamp as { toDate?: () => Date }).toDate ? (timestamp as { toDate: () => Date }).toDate() : new Date(timestamp as string | Date);
      return date.toLocaleDateString(locale === 'ar' ? 'ar-SA' : 'en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    } catch {
      return '-';
    }
  };

  // Get status configuration
  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'active':
        return {
          color: 'bg-emerald-100 text-emerald-800 border-emerald-200',
          label: 'Active'
        };
      case 'completed':
        return {
          color: 'bg-blue-100 text-blue-800 border-blue-200',
          label: 'Completed'
        };
      case 'draft':
        return {
          color: 'bg-amber-100 text-amber-800 border-amber-200',
          label: 'Draft'
        };
      default:
        return {
          color: 'bg-gray-100 text-gray-800 border-gray-200',
          label: status
        };
    }
  };

  const handleGoBack = () => {
    router.push(`/${locale}/maturity-assessment`);
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#003874] mx-auto mb-4"></div>
          <p className="text-gray-600">Loading assessment...</p>
        </div>
      </div>
    );
  }

  if (error || !assessment) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error || 'Assessment not found'}</p>
          <Button onClick={handleGoBack}>Go Back</Button>
        </div>
      </div>
    );
  }

  const statusConfig = getStatusConfig(assessment.status);
  const complianceLevel = getComplianceLevel(heroKPIs.weightedCompliance);

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={pageVariants}
      className="min-h-screen bg-gray-50/30"
    >
      {/* Enhanced Hero Header with Real KPIs */}
      <HeroHeader
        isRTL={isRtl}
        backgroundClassName="bg-gradient-to-r from-[#003874] via-[#2D8DC6] to-[#48D3A5]"
      >
        <div className={`space-y-6 ${isRtl ? 'text-right' : ''}`}>
          {/* Back Button */}
          <motion.div
            initial={{ opacity: 0, x: isRtl ? 20 : -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Button
              onClick={handleGoBack}
              variant="ghost"
              className={`text-white hover:bg-white/20 ${isRtl ? 'flex-row-reverse' : ''}`}
            >
              <ArrowLeft className={`h-5 w-5 ${isRtl ? 'ml-2 rotate-180' : 'mr-2'}`} />
              {isRtl ? 'العودة إلى التقييمات' : 'Back to Assessments'}
            </Button>
          </motion.div>

          {/* Assessment Header */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className={`flex items-start gap-6 ${isRtl ? 'flex-row-reverse' : ''}`}
          >
            <div className="p-4 bg-white/20 rounded-2xl backdrop-blur-sm">
              <Shield className="h-12 w-12 text-white" />
            </div>
            
            <div className={`flex-1 ${isRtl ? 'text-right' : ''}`}>
              <div className={`flex items-center gap-3 mb-3 ${isRtl ? 'flex-row-reverse' : ''}`}>
                <h1 className={`text-4xl font-bold text-white ${isRtl ? 'font-arabic' : ''}`}>
                  {assessment.name?.[locale as keyof typeof assessment.name] || assessment.name?.en}
                </h1>
                <Badge className={`${statusConfig.color} px-3 py-1.5 text-sm font-semibold border`}>
                  {statusConfig.label}
                </Badge>
              </div>
              
              <p className={`text-xl text-white/90 mb-6 leading-relaxed ${isRtl ? 'font-arabic' : ''}`}>
                {assessment.description}
              </p>
              
              {/* Real KPIs in Hero */}
              {!averagesLoading && !criteriaLoading && Object.keys(domainAverages).length > 0 && (
                <motion.div
                  variants={heroStatsVariants}
                  className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6"
                >
                  {/* Weighted Compliance */}
                  <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
                    <div className={`flex items-center gap-2 mb-2 ${isRtl ? 'flex-row-reverse' : ''}`}>
                      <TrendingUp className="h-5 w-5 text-white/80" />
                      <span className="text-white/70 text-sm font-medium">
                        {isRtl ? 'الامتثال المرجح' : 'Weighted Compliance'}
                      </span>
                    </div>
                    <div className={`flex items-center gap-2 ${isRtl ? 'flex-row-reverse' : ''}`}>
                      <span className="text-2xl font-bold text-white">
                        {heroKPIs.weightedCompliance.toFixed(1)}%
                      </span>
                      <Badge className={`${complianceLevel.color} text-xs px-2 py-1`}>
                        {complianceLevel.label}
                      </Badge>
                    </div>
                  </div>

                  {/* Completion Rate */}
                  <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
                    <div className={`flex items-center gap-2 mb-2 ${isRtl ? 'flex-row-reverse' : ''}`}>
                      <CheckCircle2 className="h-5 w-5 text-white/80" />
                      <span className="text-white/70 text-sm font-medium">
                        {isRtl ? 'معدل الإنجاز' : 'Completion'}
                      </span>
                    </div>
                    <span className="text-2xl font-bold text-white">
                      {heroKPIs.completionRate.toFixed(1)}%
                    </span>
                  </div>

                  {/* Total Domains */}
                  <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
                    <div className={`flex items-center gap-2 mb-2 ${isRtl ? 'flex-row-reverse' : ''}`}>
                      <Building2 className="h-5 w-5 text-white/80" />
                      <span className="text-white/70 text-sm font-medium">
                        {isRtl ? 'المجالات' : 'Domains'}
                      </span>
                    </div>
                    <span className="text-2xl font-bold text-white">
                      {Object.keys(domainAverages).length}
                    </span>
                  </div>

                  {/* Total Specifications */}
                  <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4 border border-white/20">
                    <div className={`flex items-center gap-2 mb-2 ${isRtl ? 'flex-row-reverse' : ''}`}>
                      <Target className="h-5 w-5 text-white/80" />
                      <span className="text-white/70 text-sm font-medium">
                        {isRtl ? 'المواصفات' : 'Specifications'}
                      </span>
                    </div>
                    <span className="text-2xl font-bold text-white">
                      {heroKPIs.totalSpecifications}
                    </span>
                  </div>
                </motion.div>
              )}
              
              {/* Assessment Metadata */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {framework && (
                  <div className={`flex items-center gap-3 ${isRtl ? 'flex-row-reverse' : ''}`}>
                    <Target className="h-5 w-5 text-white/80" />
                    <div>
                      <p className="text-white/70 text-sm">{isRtl ? 'الإطار' : 'Framework'}</p>
                      <p className={`text-white font-semibold ${isRtl ? 'font-arabic' : ''}`}>
                        {framework.name?.[locale as keyof typeof framework.name] || framework.name?.en}
                      </p>
                    </div>
                  </div>
                )}
                
                <div className={`flex items-center gap-3 ${isRtl ? 'flex-row-reverse' : ''}`}>
                  <Calendar className="h-5 w-5 text-white/80" />
                  <div>
                    <p className="text-white/70 text-sm">{isRtl ? 'تاريخ الإنشاء' : 'Created'}</p>
                    <p className="text-white font-semibold">
                      {formatDate(assessment.createdAt)}
                    </p>
                  </div>
                </div>
                
                <div className={`flex items-center gap-3 ${isRtl ? 'flex-row-reverse' : ''}`}>
                  <Building2 className="h-5 w-5 text-white/80" />
                  <div>
                    <p className="text-white/70 text-sm">{isRtl ? 'النوع' : 'Type'}</p>
                    <p className="text-white font-semibold">
                      {isRtl ? 'تقييم الامتثال' : 'Compliance Assessment'}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </HeroHeader>

      {/* Main Content */}
      <motion.div
        variants={contentVariants}
        className="container mx-auto px-6 py-12"
      >
        <ProfessionalTabs
          tabs={[
            {
              id: 'overview',
              label: t('overview'),
              content: (
                <AssessmentOverview 
                  assessment={{
                    ...assessment,
                    domainAverages
                  }}
                  framework={framework}
                  domains={domains}
                  locale={locale}
                />
              )
            },
            {
              id: 'domains',
              label: t('domainDetails'),
              content: (
                <DomainDetails 
                  domains={domains}
                  _framework={framework}
                  _assessment={assessment}
                  locale={locale}
                  domainsLoaded={domainsLoaded}
                  loadDomains={loadDomains}
                  projectId={projectId}
                  assessmentId={assessmentId}
                />
              )
            }
          ]}
          defaultTab="overview"
          onTabChange={(tabId: string) => {
            setActiveTab(tabId);
            // Load domains when switching to domains tab
            if (tabId === 'domains' && !domainsLoaded) {
              loadDomains();
            }
          }}
          isRtl={isRtl}
          className="w-full"
        />
      </motion.div>
    </motion.div>
  );
} 
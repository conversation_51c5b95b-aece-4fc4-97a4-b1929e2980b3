"use client";

import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { 
  ArrowLeft, 
  FileText, 
  Building2, 
  Settings,
  Loader2,
  AlertCircle,
  Shield,
  Target,
  Clock,
  List,
  BookOpen
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { useDomainSpecifications } from '@/hooks/useDomainSpecifications';
import { useAssessmentCriteria } from '@/hooks/useAssessmentCriteria';
import { 
  doc, 
  getDoc,
  setDoc, 
  collection,
  getDocs,
  updateDoc,
  serverTimestamp
} from 'firebase/firestore';
import { db } from '@/lib/firebaseClient';
import { toast } from 'sonner';
import { useAuthContext } from '@/context/AuthContext';

// Animation variants
const pageVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      staggerChildren: 0.1
    }
  }
};

const contentVariants = {
  hidden: { opacity: 0, y: 30 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5
    }
  }
};

// Define the Specification interface for this component
interface SpecificationWithControl {
  id: string;
  name?: {
    en: string;
    ar: string;
  };
  description?: {
    en: string;
    ar: string;
  };
  domainId?: string;
  controlId?: string;
  control?: {
    id: string;
    name?: {
      en: string;
      ar: string;
    };
    description?: {
      en: string;
      ar: string;
    };
  };
  maturityLevel?: number;
  percentageValue?: number;
  complianceStatus?: string;
  currentRating?: number;
  dataRating?: string | null;
  swotCompleted?: boolean;
  comments?: string;
  assessmentDate?: Date | string | null;
  subSpecifications?: SubSpecification[];
  [key: string]: unknown;
}

// Define the SubSpecification interface
interface SubSpecification {
  id?: string;
  name?: {
    en: string;
    ar: string;
  };
  description?: {
    en: string;
    ar: string;
  };
  versionHistory?: Array<{
    version: string;
    date: string;
  }>;
  updatedAt?: string;
  [key: string]: unknown;
}

export default function SpecificationDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const locale = params.locale as string;
  const projectId = params.projectId as string;
  const assessmentId = params.assessmentId as string;
  const domainName = decodeURIComponent(params.domainName as string);
  const specificationId = params.specificationId as string;
  const isRtl = locale === 'ar';

  const { user } = useAuthContext();

  const [selectedRating, setSelectedRating] = useState<string>('');
  const [comments, setComments] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [frameworkId, setFrameworkId] = useState<string>('');

  const {
    domain,
    specifications,
    loading: specsLoading,
    error: specsError
  } = useDomainSpecifications(projectId, assessmentId, domainName);

  const {
    assessmentCriteria,
    loading: criteriaLoading,
    error: criteriaError
  } = useAssessmentCriteria(frameworkId);

  // Find the specific specification
  const specification = specifications.find((spec: SpecificationWithControl) => spec.id === specificationId);

  // Fetch framework ID from assessment
  useEffect(() => {
    const fetchAssessmentData = async () => {
      try {
        const assessmentRef = doc(db, `projects/${projectId}/ComplianceAssessment/${assessmentId}`);
        const assessmentSnap = await getDoc(assessmentRef);
        if (assessmentSnap.exists()) {
          const assessmentData = assessmentSnap.data();
          setFrameworkId(assessmentData.frameworkId || '');
        }
      } catch (error) {
        console.error('Error fetching assessment data:', error);
      }
    };

    if (projectId && assessmentId) {
      fetchAssessmentData();
    }
  }, [projectId, assessmentId]);

  // Load existing rating
  useEffect(() => {
    const loadExistingRating = async () => {
      if (!specification) return;

      try {
        const ratingRef = doc(db, `projects/${projectId}/ComplianceAssessment/${assessmentId}/ratings/${specificationId}`);
        const ratingSnap = await getDoc(ratingRef);
        
        if (ratingSnap.exists()) {
          const ratingData = ratingSnap.data();
          setSelectedRating(ratingData.complianceStatus || '');
          setComments(ratingData.comments || '');
        }
      } catch (error) {
        console.error('Error loading existing rating:', error);
      }
    };

    loadExistingRating();
  }, [specification, projectId, assessmentId, specificationId]);

  const handleGoBack = () => {
    router.push(`/${locale}/maturity-assessment/${projectId}/${assessmentId}/${domainName}`);
  };

  // Save rating function
  const saveRating = async () => {
    if (!selectedRating || !user || !specification) {
      toast.error(isRtl ? 'يرجى اختيار تقييم' : 'Please select a rating');
      return;
    }

    setIsSubmitting(true);
    try {
      // Get the selected level details
      const selectedLevel = assessmentCriteria?.levels ? 
        Object.values(assessmentCriteria.levels).find(level => {
          const label = typeof level.label === 'object' 
            ? (level.label[locale] || level.label.en)
            : level.label;
          return label === selectedRating;
        }) : null;

      // Save to projects/ComplianceAssessment/{assessmentId}/ratings/{specificationId}
      const ratingData = {
        ratingType: 'compliance',
        specificationId: specificationId,
        complianceStatus: selectedRating,
        targetStatus: 'Compliant',
        currentRating: selectedLevel?.value || 0,
        targetRating: 100,
        comments: comments,
        domainName: {
          en: domain?.name?.en || domainName,
          ar: domain?.name?.ar || domainName
        },
        controlName: {
          en: specification.control?.name?.en || specification.controlId || 'Unknown Control',
          ar: specification.control?.name?.ar || specification.controlId || 'تحكم غير معروف'
        },
        updatedAt: serverTimestamp(),
        updatedBy: user.uid,
        createdAt: serverTimestamp(),
        createdBy: user.uid
      };

      const ratingRef = doc(db, `projects/${projectId}/ComplianceAssessment/${assessmentId}/ratings/${specificationId}`);
      await setDoc(ratingRef, ratingData, { merge: true });

      // Update domain averages
      await updateDomainAverages();

      toast.success(isRtl ? 'تم حفظ التقييم بنجاح' : 'Rating saved successfully');
    } catch (error) {
      console.error('Error saving rating:', error);
      toast.error(isRtl ? 'خطأ في حفظ التقييم' : 'Error saving rating');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Update domain averages function
  const updateDomainAverages = async () => {
    try {
      // Get all ratings for this assessment
      const ratingsCollection = collection(db, `projects/${projectId}/ComplianceAssessment/${assessmentId}/ratings`);
      const ratingsSnapshot = await getDocs(ratingsCollection);
      
      // Get assessment to find framework and get all specifications
      const assessmentRef = doc(db, `projects/${projectId}/ComplianceAssessment/${assessmentId}`);
      const assessmentSnap = await getDoc(assessmentRef);
      
      if (!assessmentSnap.exists()) return;
      
      const assessmentData = assessmentSnap.data();
      const frameworkId = assessmentData.frameworkId;
      
      // Get all specifications from framework to calculate proper averages including unrated ones
      const domainsRef = collection(db, `frameworks/${frameworkId}/domains`);
      const domainsSnapshot = await getDocs(domainsRef);
      
      // Build a map of all specifications by domain
      const domainSpecifications: { [domainKey: string]: { 
        total: number; 
        count: number; 
        name: { en: string; ar: string }; 
        allSpecs: string[];
        domainId: string; // Add domain ID for weight lookup
      } } = {};
      
              for (const domainDoc of domainsSnapshot.docs) {
          const domainData = domainDoc.data();
          const domainKey = domainData.name?.en || domainDoc.id;
          
          domainSpecifications[domainKey] = {
            total: 0,
            count: 0,
            name: domainData.name || { en: domainDoc.id, ar: domainDoc.id },
            allSpecs: [],
            domainId: domainDoc.id // Add the actual domain ID
          };
        
        // Get all controls for this domain
        const controlsRef = collection(db, `frameworks/${frameworkId}/domains/${domainDoc.id}/controls`);
        const controlsSnapshot = await getDocs(controlsRef);
        
        for (const controlDoc of controlsSnapshot.docs) {
          // Get all specifications for this control
          const specsRef = collection(db, `frameworks/${frameworkId}/domains/${domainDoc.id}/controls/${controlDoc.id}/specifications`);
          const specsSnapshot = await getDocs(specsRef);
          
          specsSnapshot.forEach(specDoc => {
            domainSpecifications[domainKey].allSpecs.push(specDoc.id);
          });
        }
      }
      
      // Create ratings map
      const ratingsMap = new Map();
      ratingsSnapshot.forEach(doc => {
        const ratingData = doc.data();
        ratingsMap.set(doc.id, ratingData);
      });

      // Calculate averages including unrated specifications as 0%
      Object.keys(domainSpecifications).forEach(domainKey => {
        const domainData = domainSpecifications[domainKey];
        let totalRating = 0;
        const totalSpecs = domainData.allSpecs.length;
        
        domainData.allSpecs.forEach(specId => {
          const rating = ratingsMap.get(specId);
          if (rating) {
            // Handle different compliance statuses
            let ratingValue = 0;
            if (rating.complianceStatus) {
              // Find the rating value from assessment criteria if available
              if (assessmentCriteria?.levels) {
                const matchingLevel = Object.values(assessmentCriteria.levels).find(level => {
                  const levelLabel = typeof level.label === 'object'
                    ? (level.label[locale] || level.label.en)
                    : level.label;
                  return levelLabel === rating.complianceStatus;
                });
                ratingValue = matchingLevel?.value || 0;
              } else {
                // Fallback mapping for compliance statuses
                switch (rating.complianceStatus.toLowerCase()) {
                  case 'compliant':
                  case 'ملتزم':
                    ratingValue = 100;
                    break;
                  case 'partially compliant':
                  case 'partiallycompliant':
                  case 'ملتزم جزئياً':
                    ratingValue = 50;
                    break;
                  case 'non-compliant':
                  case 'noncompliant':
                  case 'غير ملتزم':
                    ratingValue = 0;
                    break;
                  case 'not applicable':
                  case 'notapplicable':
                  case 'غير قابل للتطبيق':
                    ratingValue = 0; // Treat N/A as 0% for compliance percentage
                    break;
                  default:
                    ratingValue = rating.currentRating || 0;
                    break;
                }
              }
            } else {
              ratingValue = rating.currentRating || 0;
            }
            totalRating += ratingValue;
          } else {
            // Unrated specification = 0%
            totalRating += 0;
          }
        });
        
        domainSpecifications[domainKey].total = totalRating;
        domainSpecifications[domainKey].count = totalSpecs;
      });

      // Get assessment criteria to include domain weights
      let domainWeights: Array<{ domainId: string; weight: number }> = [];
      try {
        const criteriaRef = doc(db, `assessmentCriteria/${frameworkId}`);
        const criteriaSnap = await getDoc(criteriaRef);
        if (criteriaSnap.exists()) {
          const criteriaData = criteriaSnap.data();
          domainWeights = criteriaData.domainWeights || [];
        }
      } catch (error) {
        console.warn('Could not fetch assessment criteria for domain weights:', error);
      }

      // Update assessment document with domain averages
      const averages: { [key: string]: { 
        average: number;
        name: { en: string; ar: string };
        totalSpecifications: number;
        ratedSpecifications: number;
        domainId: string;
        weight?: number; // Add domain weight
      } } = {};
      Object.keys(domainSpecifications).forEach(domainKey => {
        const domainData = domainSpecifications[domainKey];
        const domainWeight = domainWeights.find(dw => dw.domainId === domainData.domainId);
        
        averages[domainKey] = {
          average: domainData.count > 0 ? domainData.total / domainData.count : 0,
          name: domainData.name,
          totalSpecifications: domainData.count,
          ratedSpecifications: domainData.allSpecs.filter(specId => ratingsMap.has(specId)).length,
          domainId: domainData.domainId,
          ...(domainWeight && { weight: domainWeight.weight }) // Include weight if available
        };
      });

      await updateDoc(assessmentRef, {
        domainAverages: averages,
        updatedAt: serverTimestamp()
      });

    } catch (error) {
      console.error('Error updating domain averages:', error);
    }
  };

  // Get compliance status configuration
  const getComplianceConfig = (status: string) => {
    switch (status) {
      case 'compliant':
        return {
          color: 'bg-emerald-50 text-emerald-700 border-emerald-200',
          label: isRtl ? 'ملتزم' : 'Compliant'
        };
      case 'partiallyCompliant':
        return {
          color: 'bg-blue-50 text-blue-700 border-blue-200',
          label: isRtl ? 'ملتزم جزئياً' : 'Partially Compliant'
        };
      case 'nonCompliant':
        return {
          color: 'bg-red-50 text-red-700 border-red-200',
          label: isRtl ? 'غير ملتزم' : 'Non-Compliant'
        };
      case 'notApplicable':
        return {
          color: 'bg-gray-50 text-gray-700 border-gray-200',
          label: isRtl ? 'غير قابل للتطبيق' : 'Not Applicable'
        };
      default:
        return {
          color: 'bg-amber-50 text-amber-700 border-amber-200',
          label: isRtl ? 'غير مكتمل' : 'Not Completed'
        };
    }
  };

  const loading = specsLoading || criteriaLoading;
  const error = specsError || criteriaError;

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-blue-50">
        <div className="text-center">
          <Loader2 className="h-16 w-16 animate-spin text-[#003874] mx-auto mb-6" />
          <p className={`text-gray-700 text-xl font-medium ${isRtl ? 'font-arabic' : ''}`}>
            {isRtl ? 'جاري تحميل تفاصيل المواصفة...' : 'Loading specification details...'}
          </p>
        </div>
      </div>
    );
  }

  if (error || !domain || !specification) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-red-50">
        <div className="text-center">
          <AlertCircle className="h-16 w-16 text-red-500 mx-auto mb-6" />
          <p className={`text-red-600 mb-6 text-xl ${isRtl ? 'font-arabic' : ''}`}>
            {error || (isRtl ? 'المواصفة غير موجودة' : 'Specification not found')}
          </p>
          <Button onClick={handleGoBack} className="bg-gradient-to-r from-[#003874] to-[#2D8DC6]">
            {isRtl ? 'العودة إلى المجال' : 'Back to Domain'}
          </Button>
        </div>
      </div>
    );
  }

  const statusConfig = getComplianceConfig(specification.complianceStatus || '');

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={pageVariants}
      className="min-h-screen bg-gradient-to-br from-gray-50/50 via-blue-50/30 to-indigo-50/50"
    >
      {/* Compact Hero Section with Side Rating */}
      <div className="relative overflow-hidden bg-gradient-to-r from-[#003874] via-[#2D8DC6] to-[#48D3A5]">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-white/10 rounded-full blur-3xl"></div>
        </div>
        
        <div className={`container mx-auto px-6 py-8 relative z-10 ${isRtl ? 'text-right' : ''}`}>
          {/* Back Button */}
          <motion.div
            initial={{ opacity: 0, x: isRtl ? 20 : -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
            className="mb-6"
          >
            <Button
              onClick={handleGoBack}
              variant="ghost"
              size="sm"
              className={`text-white hover:bg-white/20 backdrop-blur-sm border border-white/20 transition-all duration-300 ${isRtl ? 'flex-row-reverse' : ''}`}
            >
              <ArrowLeft className={`h-4 w-4 ${isRtl ? 'ml-2 rotate-180' : 'mr-2'}`} />
              <span className={isRtl ? 'font-arabic' : ''}>{isRtl ? 'العودة إلى المجال' : 'Back to Domain'}</span>
            </Button>
          </motion.div>

          {/* Main Hero Content with Rating Sidebar */}
          <div className={`grid grid-cols-1 lg:grid-cols-5 gap-8 ${isRtl ? 'rtl' : ''}`}>
            {/* Left Side - Specification Details */}
            <div className="lg:col-span-3">
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className={`flex items-start gap-4 ${isRtl ? 'flex-row-reverse' : ''}`}
              >
                <div className="p-3 bg-white/20 rounded-2xl backdrop-blur-sm border border-white/20 shadow-lg flex-shrink-0">
                  <FileText className="h-8 w-8 text-white" />
                </div>
                
                <div className={`flex-1 min-w-0 ${isRtl ? 'text-right' : ''}`}>
                  <motion.h1 
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.4 }}
                    className={`text-2xl lg:text-3xl font-bold text-white mb-3 leading-tight ${isRtl ? 'font-arabic' : ''}`}
                  >
                    {specification.name?.[locale as keyof typeof specification.name] || 
                     specification.name?.en || 
                     specification.id}
                  </motion.h1>
                  
                  <motion.p 
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.5 }}
                    className={`text-sm text-white/85 mb-4 leading-relaxed line-clamp-2 ${isRtl ? 'font-arabic' : ''}`}
                  >
                    {specification.description?.[locale as keyof typeof specification.description] || 
                     specification.description?.en || 
                     (isRtl ? 'لا يوجد وصف متاح' : 'No description available')}
                  </motion.p>
                  
                  {/* Compact Metadata */}
                  <motion.div 
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.6 }}
                    className="flex flex-wrap gap-2"
                  >
                    <div className={`inline-flex items-center gap-2 px-3 py-1.5 bg-white/15 rounded-xl backdrop-blur-sm border border-white/20 text-xs ${isRtl ? 'flex-row-reverse' : ''}`}>
                      <Building2 className="h-3 w-3 text-white" />
                      <span className={`text-white/90 font-medium ${isRtl ? 'font-arabic' : ''}`}>
                        {domain.name?.[locale as keyof typeof domain.name] || domain.name?.en || domain.id}
                      </span>
                    </div>
                    
                    <div className={`inline-flex items-center gap-2 px-3 py-1.5 bg-white/15 rounded-xl backdrop-blur-sm border border-white/20 text-xs ${isRtl ? 'flex-row-reverse' : ''}`}>
                      <Settings className="h-3 w-3 text-white" />
                      <span className={`text-white/90 font-medium truncate max-w-[120px] ${isRtl ? 'font-arabic' : ''}`}>
                        {specification.control?.name?.[locale as keyof typeof specification.control.name] || 
                         specification.control?.name?.en || 
                         specification.controlId || 
                         (isRtl ? 'غير محدد' : 'Not Specified')}
                      </span>
                    </div>
                    
                    <Badge className={`${statusConfig.color} px-3 py-1.5 text-xs font-bold border rounded-xl inline-flex items-center gap-1.5`}>
                      <Shield className="h-3 w-3" />
                      <span className={`${isRtl ? 'font-arabic' : ''}`}>{statusConfig.label}</span>
                    </Badge>
                  </motion.div>
                </div>
              </motion.div>
            </div>

            {/* Right Side - Compact Rating Panel */}
            {assessmentCriteria?.type === 'compliance' && assessmentCriteria.levels && (
              <motion.div
                initial={{ opacity: 0, x: isRtl ? -30 : 30 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                className="lg:col-span-2"
              >
                <Card className="bg-white/95 backdrop-blur-lg border-white/20 shadow-2xl rounded-2xl overflow-hidden sticky top-6">
                  <CardContent className="p-5">
                    {/* Compact Rating Header */}
                    <div className={`text-center mb-5 ${isRtl ? 'text-right' : ''}`}>
                      <div className="p-3 bg-gradient-to-r from-[#003874] to-[#2D8DC6] rounded-xl shadow-lg mx-auto w-fit mb-3">
                        <Target className="h-6 w-6 text-white" />
                      </div>
                      <h3 className={`text-lg font-bold text-gray-900 mb-1 ${isRtl ? 'font-arabic' : ''}`}>
                        {isRtl ? 'تقييم الامتثال' : 'Compliance Rating'}
                      </h3>
                      <p className={`text-gray-600 text-xs ${isRtl ? 'font-arabic' : ''}`}>
                        {isRtl ? 'اختر مستوى الامتثال' : 'Select compliance level'}
                      </p>
                      
                      {/* Current Rating Display */}
                      {selectedRating && (
                        <motion.div
                          initial={{ opacity: 0, scale: 0.8 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ type: "spring", stiffness: 300, damping: 30 }}
                          className="mt-3 p-2.5 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200"
                        >
                          <p className={`text-xs font-medium text-blue-700 mb-1 ${isRtl ? 'font-arabic' : ''}`}>
                            {isRtl ? 'التقييم المختار:' : 'Selected:'}
                          </p>
                          <div className={`flex items-center justify-center gap-2 ${isRtl ? 'flex-row-reverse' : ''}`}>
                            <span className={`font-bold text-blue-900 text-sm ${isRtl ? 'font-arabic' : ''}`}>
                              {selectedRating}
                            </span>
                            {(() => {
                              const selectedLevel = Object.values(assessmentCriteria.levels || {}).find(level => {
                                const levelLabel = typeof level.label === 'object'
                                  ? (level.label[locale] || level.label.en)
                                  : level.label;
                                return levelLabel === selectedRating;
                              });
                              return selectedLevel && (
                                <span className="px-2 py-0.5 bg-blue-100 text-blue-800 rounded text-xs font-bold">
                                  {selectedLevel.value}%
                                </span>
                              );
                            })()}
                          </div>
                        </motion.div>
                      )}
                    </div>

                    {/* Compact Rating Options */}
                    <div className="space-y-2 mb-5">
                      {Object.values(assessmentCriteria.levels || {})
                        .sort((a, b) => b.value - a.value)
                        .map((level, index) => {
                          const levelLabel = typeof level.label === 'object'
                            ? (level.label[locale] || level.label.en)
                            : level.label;
                          const levelDescription = typeof level.description === 'object'
                            ? (level.description?.[locale] || level.description?.en)
                            : level.description;
                          const isSelected = levelLabel === selectedRating;

                          // Color scheme based on compliance value
                          const getColorScheme = (value: number) => {
                            if (value >= 80) return 'emerald';
                            if (value >= 60) return 'blue';
                            if (value >= 40) return 'amber';
                            return 'red';
                          };

                          const colorScheme = getColorScheme(level.value);

                          return (
                            <motion.div
                              key={index}
                              initial={{ opacity: 0, y: 5 }}
                              animate={{ opacity: 1, y: 0 }}
                              transition={{ delay: 0.05 * index }}
                              className={`relative cursor-pointer group ${
                                isSelected ? 'scale-[1.02]' : 'hover:scale-[1.01]'
                              } transition-all duration-200`}
                              onClick={() => setSelectedRating(levelLabel)}
                            >
                              <div className={`p-3 rounded-xl border-2 transition-all duration-200 ${
                                isSelected
                                  ? `border-${colorScheme}-500 bg-gradient-to-r from-${colorScheme}-50 to-${colorScheme}-100 shadow-md`
                                  : `border-gray-200 hover:border-${colorScheme}-300 hover:bg-${colorScheme}-50/50`
                              }`}>
                                <div className={`flex items-center gap-2.5 ${isRtl ? 'flex-row-reverse' : ''}`}>
                                  {/* Custom Radio Button */}
                                  <div className={`relative w-4 h-4 rounded-full border-2 transition-all duration-200 flex-shrink-0 ${
                                    isSelected 
                                      ? `border-${colorScheme}-500 bg-${colorScheme}-500` 
                                      : `border-gray-300 group-hover:border-${colorScheme}-400`
                                  }`}>
                                    {isSelected && (
                                      <motion.div
                                        initial={{ scale: 0 }}
                                        animate={{ scale: 1 }}
                                        transition={{ type: "spring", stiffness: 400, damping: 25 }}
                                        className="absolute inset-0.5 bg-white rounded-full"
                                      />
                                    )}
                                  </div>
                                  
                                  <div className={`flex-1 min-w-0 ${isRtl ? 'text-right' : ''}`}>
                                    <div className={`flex items-center justify-between ${isRtl ? 'flex-row-reverse' : ''}`}>
                                      <h4 className={`font-bold text-gray-900 text-xs truncate ${isRtl ? 'font-arabic' : ''}`}>
                                        {levelLabel}
                                      </h4>
                                      <span className={`text-xs font-bold px-1.5 py-0.5 rounded bg-${colorScheme}-100 text-${colorScheme}-700 flex-shrink-0 ml-2`}>
                                        {level.value}%
                                      </span>
                                    </div>
                                    {levelDescription && (
                                      <p className={`text-gray-600 text-xs mt-0.5 line-clamp-1 ${isRtl ? 'font-arabic' : ''}`}>
                                        {levelDescription}
                                      </p>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </motion.div>
                          );
                        })}
                    </div>

                    {/* Quick Actions */}
                    <div className={`grid grid-cols-2 gap-2 mb-4 ${isRtl ? 'rtl' : ''}`}>
                      {/* Quick Compliant */}
                      <Button
                        onClick={() => {
                          const compliantLevel = Object.values(assessmentCriteria.levels || {}).find(level => level.value >= 80);
                          if (compliantLevel) {
                            const label = typeof compliantLevel.label === 'object'
                              ? (compliantLevel.label[locale] || compliantLevel.label.en)
                              : compliantLevel.label;
                            setSelectedRating(label);
                          }
                        }}
                        variant="outline"
                        size="sm"
                        className={`text-xs py-2 border-emerald-200 hover:bg-emerald-50 hover:border-emerald-300 transition-all duration-200 ${isRtl ? 'font-arabic' : ''}`}
                      >
                        ✓ {isRtl ? 'ملتزم' : 'Compliant'}
                      </Button>
                      
                      {/* Quick Non-Compliant */}
                      <Button
                        onClick={() => {
                          const nonCompliantLevel = Object.values(assessmentCriteria.levels || {}).find(level => level.value === 0);
                          if (nonCompliantLevel) {
                            const label = typeof nonCompliantLevel.label === 'object'
                              ? (nonCompliantLevel.label[locale] || nonCompliantLevel.label.en)
                              : nonCompliantLevel.label;
                            setSelectedRating(label);
                          }
                        }}
                        variant="outline"
                        size="sm"
                        className={`text-xs py-2 border-red-200 hover:bg-red-50 hover:border-red-300 transition-all duration-200 ${isRtl ? 'font-arabic' : ''}`}
                      >
                        ✗ {isRtl ? 'غير ملتزم' : 'Non-Compliant'}
                      </Button>
                    </div>

                    {/* Comments Section - More Compact */}
                    <div className="mb-4">
                      <label className={`block text-xs font-bold text-gray-900 mb-1.5 ${isRtl ? 'text-right font-arabic' : ''}`}>
                        {isRtl ? 'تعليقات' : 'Comments'}
                      </label>
                      <textarea
                        value={comments}
                        onChange={(e) => setComments(e.target.value)}
                        placeholder={isRtl ? 'أضف تعليقات...' : 'Add comments...'}
                        className={`w-full p-2.5 border border-gray-300 rounded-lg resize-none focus:border-[#003874] focus:ring-1 focus:ring-[#003874]/20 transition-colors text-xs ${
                          isRtl ? 'text-right font-arabic' : ''
                        }`}
                        rows={2}
                      />
                    </div>

                    {/* Action Buttons */}
                    <div className={`space-y-2 ${isRtl ? 'rtl' : ''}`}>
                      <Button
                        onClick={saveRating}
                        disabled={!selectedRating || isSubmitting}
                        className={`w-full bg-gradient-to-r from-[#003874] to-[#2D8DC6] hover:from-[#002a5c] hover:to-[#1e6b9e] text-white py-2.5 text-xs font-bold rounded-lg transition-all duration-200 disabled:opacity-50 ${
                          isRtl ? 'font-arabic' : ''
                        }`}
                      >
                        {isSubmitting ? (
                          <>
                            <Loader2 className={`h-3 w-3 animate-spin ${isRtl ? 'ml-1.5' : 'mr-1.5'}`} />
                            {isRtl ? 'جاري الحفظ...' : 'Saving...'}
                          </>
                        ) : (
                          <>
                            <Shield className={`h-3 w-3 ${isRtl ? 'ml-1.5' : 'mr-1.5'}`} />
                            {isRtl ? 'حفظ التقييم' : 'Save Rating'}
                          </>
                        )}
                      </Button>
                      
                      <Button
                        onClick={() => {
                          setSelectedRating('');
                          setComments('');
                        }}
                        variant="outline"
                        size="sm"
                        className={`w-full py-2 text-xs font-bold rounded-lg border hover:bg-gray-50 transition-all duration-200 ${isRtl ? 'font-arabic' : ''}`}
                      >
                        {isRtl ? 'إعادة تعيين' : 'Reset'}
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <motion.div
        variants={contentVariants}
        className="container mx-auto px-6 py-8 max-w-7xl space-y-8"
      >
        {/* Specification Details Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card className="border-0 shadow-xl bg-white/90 backdrop-blur-sm overflow-hidden rounded-2xl">
            <CardContent className="p-8">
              <div className={`text-center ${isRtl ? 'text-right' : ''}`}>
                <Target className="h-16 w-16 mx-auto mb-6 text-[#003874]" />
                <h2 className={`text-2xl font-bold text-gray-900 mb-3 ${isRtl ? 'font-arabic' : ''}`}>
                  {isRtl ? 'تفاصيل المواصفة' : 'Specification Details'}
                </h2>
                <p className={`text-gray-600 text-lg max-w-2xl mx-auto leading-relaxed ${isRtl ? 'font-arabic' : ''}`}>
                  {specification.description?.[locale as keyof typeof specification.description] || 
                   specification.description?.en || 
                   (isRtl ? 'لا يوجد وصف تفصيلي متاح لهذه المواصفة' : 'No detailed description available for this specification')}
                </p>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Sub-specifications Section - Always Visible */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
        >
          <Card className="border-0 shadow-xl bg-white/90 backdrop-blur-sm overflow-hidden rounded-2xl">
            <CardContent className="p-6">
              {/* Sub-specifications Header */}
              <div className={`text-center mb-8 ${isRtl ? 'text-right' : ''}`}>
                <div className={`flex items-center justify-center gap-3 mb-4 ${isRtl ? 'flex-row-reverse' : ''}`}>
                  <div className="p-3 bg-gradient-to-r from-[#003874] to-[#2D8DC6] rounded-xl shadow-lg">
                    <BookOpen className="h-8 w-8 text-white" />
                  </div>
                  <div>
                    <h2 className={`text-2xl font-bold text-gray-900 mb-1 ${isRtl ? 'font-arabic' : ''}`}>
                      {isRtl ? 'المواصفات الفرعية' : 'Sub-specifications'}
                    </h2>
                    <p className={`text-gray-600 text-sm ${isRtl ? 'font-arabic' : ''}`}>
                      {Array.isArray(specification.subSpecifications) ? specification.subSpecifications.length : 0} {isRtl ? 'مواصفة فرعية' : 'sub-specifications found'}
                    </p>
                  </div>
                </div>
              </div>

              {/* Sub-specifications Content */}
              <div className="space-y-4">
                {Array.isArray(specification.subSpecifications) && specification.subSpecifications.length > 0 ? (
                  specification.subSpecifications.map((subSpec: SubSpecification, index: number) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.05 * index }}
                      className="group"
                    >
                      <Card className="border border-gray-200/50 hover:border-[#2D8DC6]/30 hover:shadow-lg transition-all duration-200 bg-white/95 backdrop-blur-sm rounded-xl overflow-hidden">
                        <CardContent className="p-5">
                          <div className={`space-y-4 ${isRtl ? 'text-right' : ''}`}>
                            {/* Sub-spec header */}
                            <div className={`flex items-start gap-4 ${isRtl ? 'flex-row-reverse' : ''}`}>
                              <div className="p-2.5 bg-gradient-to-r from-[#003874] to-[#2D8DC6] rounded-lg shadow-md flex-shrink-0 group-hover:scale-105 transition-transform duration-200">
                                <FileText className="h-5 w-5 text-white" />
                              </div>
                              <div className="flex-1 min-w-0">
                                <h3 className={`font-bold text-gray-900 text-lg mb-2 group-hover:text-[#003874] transition-colors ${isRtl ? 'font-arabic' : ''}`}>
                                  {subSpec.name?.[locale as keyof typeof subSpec.name] || 
                                   subSpec.name?.en || 
                                   `${isRtl ? 'مواصفة فرعية' : 'Sub-specification'} ${index + 1}`}
                                </h3>
                                <p className={`text-gray-600 text-sm leading-relaxed ${isRtl ? 'font-arabic' : ''}`}>
                                  {subSpec.description?.[locale as keyof typeof subSpec.description] || 
                                   subSpec.description?.en || 
                                   (isRtl ? 'لا يوجد وصف متاح' : 'No description available')}
                                </p>
                              </div>
                            </div>

                            {/* Compact Metadata Row */}
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                              {/* Version info */}
                              {subSpec.versionHistory && subSpec.versionHistory.length > 0 && (
                                <div className={`flex items-center gap-3 p-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-100 ${isRtl ? 'flex-row-reverse' : ''}`}>
                                  <div className="p-2 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-lg shadow-md">
                                    <Clock className="h-4 w-4 text-white" />
                                  </div>
                                  <div className={`${isRtl ? 'text-right font-arabic' : ''}`}>
                                    <p className="text-blue-900 font-bold text-sm">
                                      {isRtl ? 'الإصدار' : 'Version'} {subSpec.versionHistory[0]?.version || 'N/A'}
                                    </p>
                                    {subSpec.versionHistory[0]?.date && (
                                      <p className={`text-blue-700 text-xs ${isRtl ? 'font-arabic' : ''}`}>
                                        {subSpec.versionHistory[0].date}
                                      </p>
                                    )}
                                  </div>
                                </div>
                              )}

                              {/* Updated date */}
                              {subSpec.updatedAt && (
                                <div className={`flex items-center gap-3 p-3 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg border border-green-100 ${isRtl ? 'flex-row-reverse' : ''}`}>
                                  <div className="p-2 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg shadow-md">
                                    <Clock className="h-4 w-4 text-white" />
                                  </div>
                                  <div className={`${isRtl ? 'text-right font-arabic' : ''}`}>
                                    <p className="text-green-900 font-bold text-sm">
                                      {isRtl ? 'آخر تحديث' : 'Last Updated'}
                                    </p>
                                    <p className={`text-green-700 text-xs ${isRtl ? 'font-arabic' : ''}`}>
                                      {new Date(subSpec.updatedAt).toLocaleDateString(isRtl ? 'ar' : 'en', {
                                        year: 'numeric',
                                        month: 'short',
                                        day: 'numeric'
                                      })}
                                    </p>
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  ))
                ) : (
                  <div className="text-center py-12">
                    <motion.div
                      initial={{ opacity: 0, scale: 0.9 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.3 }}
                    >
                      <List className="h-16 w-16 mx-auto mb-4 text-gray-300" />
                      <h3 className={`text-xl font-bold text-gray-900 mb-2 ${isRtl ? 'font-arabic' : ''}`}>
                        {isRtl ? 'لا توجد مواصفات فرعية' : 'No Sub-specifications'}
                      </h3>
                      <p className={`text-gray-600 text-sm max-w-md mx-auto ${isRtl ? 'font-arabic' : ''}`}>
                        {isRtl 
                          ? 'لا توجد مواصفات فرعية محددة لهذه المواصفة حتى الآن'
                          : 'No sub-specifications have been defined for this specification yet'
                        }
                      </p>
                    </motion.div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </motion.div>
    </motion.div>
  );
} 
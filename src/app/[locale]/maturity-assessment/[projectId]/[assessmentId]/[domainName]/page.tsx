"use client";

import { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { useTranslations } from 'next-intl';
import { 
  ArrowLeft, 
  Building2, 
  FileText, 
  Search, 
  Filter,
  Shield,
  Target,
  AlertCircle,
  CheckCircle2,
  Clock,
  Eye,
  Settings,
  Loader2,
  Layers,
  Star,
  Grid3X3,
  Weight
} from 'lucide-react';
import { HeroHeader } from '@/components/shared/HeroHeader';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useDomainSpecifications } from '@/hooks/useDomainSpecifications';
import { useAssessmentCriteria } from '@/hooks/useAssessmentCriteria';

import { AssessmentCriteria, LevelDefinition } from '@/types';

// Animation variants
const pageVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      staggerChildren: 0.1
    }
  }
};

const contentVariants = {
  hidden: { opacity: 0, y: 30 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5
    }
  }
};

// Helper function to get compliance percentage from status
const getCompliancePercentage = (status: string, assessmentCriteria?: AssessmentCriteria | null): number => {
  if (!assessmentCriteria?.levels) return 0;
  
  // Find the matching level
  const matchingLevel = Object.values(assessmentCriteria.levels).find((level: LevelDefinition) => {
    if (typeof level.label === 'string') {
      return level.label.toLowerCase() === status.toLowerCase();
    } else if (typeof level.label === 'object') {
      return level.label.en?.toLowerCase() === status.toLowerCase() || 
             level.label.ar?.toLowerCase() === status.toLowerCase();
    }
    return false;
  });
  
  return matchingLevel ? matchingLevel.value : 0;
};

// Define the enhanced specification type that includes all properties we use
interface SpecificationWithControl {
  id: string;
  name?: {
    en: string;
    ar: string;
  };
  description?: {
    en: string;
    ar: string;
  };
  domainId?: string;
  controlId?: string;
  control?: {
    id: string;
    name?: {
      en: string;
      ar: string;
    };
    description?: {
      en: string;
      ar: string;
    };
  };
  maturityLevel?: number;
  percentageValue?: number;
  complianceStatus?: string;
  currentRating?: number;
  dataRating?: string | null;
  swotCompleted?: boolean;
  comments?: string;
  assessmentDate?: Date | string | null;
  [key: string]: unknown;
}

// Define the Control interface for the controls filter
interface Control {
  id: string;
  name?: {
    en: string;
    ar: string;
  };
  description?: {
    en: string;
    ar: string;
  };
}

export default function DomainDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const locale = params.locale as string;
  const projectId = params.projectId as string;
  const assessmentId = params.assessmentId as string;
  const domainName = decodeURIComponent(params.domainName as string);
  const isRtl = locale === 'ar';

  const t = useTranslations('DomainDetails');
  
  const [searchTerm, setSearchTerm] = useState('');
  const [controlFilter, setControlFilter] = useState('all');
  const [sortBy, setSortBy] = useState('name');
  const [frameworkId, setFrameworkId] = useState<string>('');

  const {
    domain,
    specifications,
    controls,
    loading: specsLoading,
    error: specsError
  } = useDomainSpecifications(projectId, assessmentId, domainName);

  const {
    assessmentCriteria,
    loading: criteriaLoading,
    error: criteriaError,
    getDomainWeight,
    getDomainWeightConfig: getWeightConfig
  } = useAssessmentCriteria(frameworkId);

  const loading = specsLoading || criteriaLoading;
  const error = specsError || criteriaError;

  // Fetch framework ID from assessment
  useEffect(() => {
    const fetchAssessmentData = async () => {
      try {
        const { doc, getDoc } = await import('firebase/firestore');
        const { db } = await import('@/lib/firebaseClient');
        
        const assessmentRef = doc(db, `projects/${projectId}/ComplianceAssessment/${assessmentId}`);
        const assessmentSnap = await getDoc(assessmentRef);
        if (assessmentSnap.exists()) {
          const assessmentData = assessmentSnap.data();
          setFrameworkId(assessmentData.frameworkId || '');
        }
      } catch (error) {
        console.error('Error fetching assessment data:', error);
      }
    };

    if (projectId && assessmentId) {
      fetchAssessmentData();
    }
  }, [projectId, assessmentId]);

  const handleGoBack = () => {
    router.push(`/${locale}/maturity-assessment/${projectId}/${assessmentId}`);
  };

  // Filter and sort specifications
  const filteredSpecifications = specifications
    .filter((spec: SpecificationWithControl) => {
      const nameMatch = spec.name?.[locale as keyof typeof spec.name]?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                      spec.name?.en?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                      spec.id.toLowerCase().includes(searchTerm.toLowerCase());
      
      const controlMatch = controlFilter === 'all' || spec.controlId === controlFilter;
      
      return nameMatch && controlMatch;
    })
    .sort((a: SpecificationWithControl, b: SpecificationWithControl) => {
      switch (sortBy) {
        case 'name':
          const nameA = a.name?.[locale as keyof typeof a.name] || a.name?.en || a.id;
          const nameB = b.name?.[locale as keyof typeof b.name] || b.name?.en || b.id;
          return nameA.localeCompare(nameB);
        case 'level':
          return (a.maturityLevel || 0) - (b.maturityLevel || 0);
        case 'control':
          const controlA = a.control?.name?.[locale as keyof typeof a.control.name] || a.control?.name?.en || a.controlId || '';
          const controlB = b.control?.name?.[locale as keyof typeof b.control.name] || b.control?.name?.en || b.controlId || '';
          return controlA.localeCompare(controlB);
        case 'id':
          return a.id.localeCompare(b.id);
        default:
          return 0;
      }
    });

  // Calculate domain compliance metrics using the same system as root assessment
  const calculateDomainMetrics = () => {
    if (!domain?.specifications || specifications.length === 0) {
      return {
        overallRating: 0,
        totalSpecs: 0,
        ratedSpecs: 0,
        distributionByStatus: {},
        averagePercentage: 0,
        complianceStatus: 'notCompleted'
      };
    }

    const distributionByStatus: { [key: string]: number } = {};
    
    // Filter specs that are actually rated (not notCompleted)
    const ratedSpecs = specifications.filter(spec => {
      const status = spec.complianceStatus;
      return status && 
             status !== 'notCompleted' && 
             status !== 'Not Completed' && 
             status !== '' && 
             status !== null && 
             status !== undefined;
    }).length;
    
    // Build distribution including all statuses
    specifications.forEach(spec => {
      const status = spec.complianceStatus || 'notCompleted';
      distributionByStatus[status] = (distributionByStatus[status] || 0) + 1;
    });

    // Calculate average compliance percentage SAME AS ROOT ASSESSMENT:
    // Include ALL specifications in calculation, treating unrated ones as 0%
    let totalPercentage = 0;
    const totalSpecs = specifications.length; // ALL specifications
    
    specifications.forEach(spec => {
      const status = spec.complianceStatus;
      if (status && status !== 'notCompleted' && status !== 'Not Completed' && status !== '') {
        // Get percentage for rated specifications
        const percentage = getCompliancePercentage(status, assessmentCriteria);
        totalPercentage += percentage;
      } else {
        // Unrated specification = 0% (same as root assessment calculation)
        totalPercentage += 0;
      }
    });

    const averagePercentage = totalSpecs > 0 ? totalPercentage / totalSpecs : 0;

    // Map average percentage to compliance status
    let complianceStatus = 'notCompleted';
    if (assessmentCriteria?.levels && averagePercentage > 0) {
      const levels = Object.values(assessmentCriteria.levels).sort((a, b) => b.value - a.value);
      const matchingLevel = levels.find((level: LevelDefinition) => averagePercentage >= level.value);
      if (matchingLevel) {
        complianceStatus = typeof matchingLevel.label === 'object' 
          ? (matchingLevel.label[locale] || matchingLevel.label.en)
          : matchingLevel.label;
      }
    }

    return {
      overallRating: averagePercentage,
      totalSpecs: specifications.length,
      ratedSpecs,
      distributionByStatus,
      averagePercentage,
      complianceStatus
    };
  };

  const domainMetrics = calculateDomainMetrics();
  
  // Get domain weight from assessment criteria
  const domainWeight = getWeightConfig(
    getDomainWeight(domain?.id || ''),
    locale
  );

  // Get compliance status configuration
  const getComplianceConfig = (status: string) => {
    // Try to find the status in assessment criteria first
    if (assessmentCriteria?.levels) {
      const matchingLevel = Object.values(assessmentCriteria.levels).find(level => {
        const levelLabel = typeof level.label === 'object'
          ? (level.label[locale] || level.label.en)
          : level.label;
        return levelLabel === status;
      });

      if (matchingLevel) {
        // Use a gradient based on the compliance value
        const value = matchingLevel.value;
        if (value >= 80) {
          return {
            color: 'bg-emerald-50 text-emerald-700 border-emerald-200 shadow-emerald-100',
            icon: CheckCircle2,
            label: status,
            gradient: 'from-emerald-500 to-green-500',
            value
          };
        } else if (value >= 50) {
          return {
            color: 'bg-blue-50 text-blue-700 border-blue-200 shadow-blue-100',
            icon: Clock,
            label: status,
            gradient: 'from-blue-500 to-indigo-500',
            value
          };
        } else if (value >= 0) {
          return {
            color: 'bg-red-50 text-red-700 border-red-200 shadow-red-100',
            icon: AlertCircle,
            label: status,
            gradient: 'from-red-500 to-rose-500',
            value
          };
        }
      }
    }

    // Fallback to hardcoded values with dynamic percentage calculation
    const getPercentageForStatus = (stat: string) => {
      return assessmentCriteria ? getCompliancePercentage(stat, assessmentCriteria) : 0;
    };

    switch (status.toLowerCase()) {
      case 'compliant':
      case 'ملتزم':
        return {
          color: 'bg-emerald-50 text-emerald-700 border-emerald-200 shadow-emerald-100',
          icon: CheckCircle2,
          label: status,
          gradient: 'from-emerald-500 to-green-500',
          value: getPercentageForStatus(status)
        };
      case 'partiallycompliant':
      case 'partially compliant':
      case 'ملتزم جزئياً':
        return {
          color: 'bg-blue-50 text-blue-700 border-blue-200 shadow-blue-100',
          icon: Clock,
          label: status,
          gradient: 'from-blue-500 to-indigo-500',
          value: getPercentageForStatus(status)
        };
      case 'noncompliant':
      case 'non-compliant':
      case 'غير ملتزم':
        return {
          color: 'bg-red-50 text-red-700 border-red-200 shadow-red-100',
          icon: AlertCircle,
          label: status,
          gradient: 'from-red-500 to-rose-500',
          value: getPercentageForStatus(status)
        };
      case 'notapplicable':
      case 'not applicable':
      case 'غير قابل للتطبيق':
        return {
          color: 'bg-gray-50 text-gray-700 border-gray-200 shadow-gray-100',
          icon: Shield,
          label: status,
          gradient: 'from-gray-500 to-slate-500',
          value: getPercentageForStatus(status)
        };
      case 'notcompleted':
      case 'not completed':
      case 'غير مكتمل':
      case '':
        return {
          color: 'bg-amber-50 text-amber-700 border-amber-200 shadow-amber-100',
          icon: Eye,
          label: isRtl ? 'غير مكتمل' : 'Not Completed',
          gradient: 'from-amber-500 to-orange-500',
          value: 0
        };
      default:
        return {
          color: 'bg-amber-50 text-amber-700 border-amber-200 shadow-amber-100',
          icon: Eye,
          label: status || (isRtl ? 'غير مكتمل' : 'Not Completed'),
          gradient: 'from-amber-500 to-orange-500',
          value: 0
        };
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-blue-50">
        <div className="text-center">
          <Loader2 className="h-16 w-16 animate-spin text-[#003874] mx-auto mb-6" />
          <p className={`text-gray-700 text-xl font-medium ${isRtl ? 'font-arabic' : ''}`}>
            {isRtl ? 'جاري تحميل مواصفات المجال...' : 'Loading domain specifications...'}
          </p>
        </div>
      </div>
    );
  }

  if (error || !domain) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-red-50">
        <div className="text-center">
          <AlertCircle className="h-16 w-16 text-red-500 mx-auto mb-6" />
          <p className={`text-red-600 mb-6 text-xl ${isRtl ? 'font-arabic' : ''}`}>
            {error || (isRtl ? 'المجال غير موجود' : 'Domain not found')}
          </p>
          <Button onClick={handleGoBack} className="bg-gradient-to-r from-[#003874] to-[#2D8DC6]">
            {isRtl ? 'العودة' : 'Go Back'}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={pageVariants}
      className="min-h-screen bg-gradient-to-br from-gray-50/50 via-blue-50/30 to-indigo-50/50"
    >
      {/* Enhanced Hero Header */}
      <HeroHeader
        isRTL={isRtl}
        backgroundClassName="bg-gradient-to-r from-[#003874] via-[#2D8DC6] to-[#48D3A5] relative overflow-hidden"
      >
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-white/10 rounded-full blur-3xl"></div>
        </div>
        
        {/* Domain Weight - Floating Top Right */}
        {domainWeight.isSet && (
          <motion.div
            initial={{ opacity: 0, x: isRtl ? -20 : 20, y: -20 }}
            animate={{ opacity: 1, x: 0, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className={`absolute top-6 z-20 ${isRtl ? 'left-6' : 'right-6'}`}
          >
            <div className={`${domainWeight.bgColor} backdrop-blur-md rounded-2xl p-4 border border-white/30 shadow-2xl hover:scale-105 transition-all duration-300`}>
              <div className={`flex items-center gap-3 ${isRtl ? 'flex-row-reverse' : ''}`}>
                <div className="p-2 bg-white/25 rounded-xl shadow-lg">
                  <Weight className="h-5 w-5 text-white" />
                </div>
                <div className={isRtl ? 'text-right' : ''}>
                  <div className={`text-white/90 text-sm font-medium ${isRtl ? 'font-arabic' : ''}`}>
                    {isRtl ? 'وزن المجال' : 'Domain Weight'}
                  </div>
                  <div className={`text-white text-xl font-bold ${domainWeight.color}`}>
                    {domainWeight.display}
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        )}
        
        <div className={`space-y-8 relative z-10 ${isRtl ? 'text-right' : ''}`}>
          {/* Back Button */}
          <motion.div
            initial={{ opacity: 0, x: isRtl ? 20 : -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Button
              onClick={handleGoBack}
              variant="ghost"
              className={`text-white hover:bg-white/20 backdrop-blur-sm border border-white/20 transition-all duration-300 ${isRtl ? 'flex-row-reverse' : ''}`}
            >
              <ArrowLeft className={`h-5 w-5 ${isRtl ? 'ml-2 rotate-180' : 'mr-2'}`} />
              <span className={isRtl ? 'font-arabic' : ''}>{t('backToDomain')}</span>
            </Button>
          </motion.div>

          {/* Domain Header */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className={`flex items-start gap-8 ${isRtl ? 'flex-row-reverse' : ''}`}
          >
            <div className="p-6 bg-white/20 rounded-3xl backdrop-blur-sm border border-white/20 shadow-lg">
              <Building2 className="h-16 w-16 text-white" />
            </div>
            
            <div className={`flex-1 ${isRtl ? 'text-right' : ''}`}>
              <motion.h1 
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                className={`text-5xl font-bold text-white mb-4 ${isRtl ? 'font-arabic leading-relaxed' : ''}`}
              >
                {domain.name?.[locale as keyof typeof domain.name] || domain.name?.en}
              </motion.h1>
              
              <motion.p 
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
                className={`text-xl text-white/90 mb-6 leading-relaxed max-w-3xl ${isRtl ? 'font-arabic' : ''}`}
              >
                {domain.description?.[locale as keyof typeof domain.description] || 
                 domain.description?.en || 
                 (isRtl ? 'تفاصيل مواصفات المجال ومتطلبات الامتثال' : 'Domain specifications and compliance details')}
              </motion.p>
              
              {/* Enhanced KPI Dashboard */}
              <motion.div 
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6 }}
                className={`grid grid-cols-1 md:grid-cols-2 gap-6 ${domainWeight.isSet ? 'lg:grid-cols-5' : 'lg:grid-cols-4'}`}
              >
                {/* Overall Compliance Score */}
                <div className={`flex items-center gap-4 p-6 bg-white/15 rounded-3xl backdrop-blur-sm border border-white/20 shadow-2xl hover:bg-white/20 transition-all duration-300 ${isRtl ? 'flex-row-reverse' : ''}`}>
                  <div className="p-4 bg-gradient-to-r from-emerald-500 to-green-500 rounded-2xl shadow-xl">
                    <CheckCircle2 className="h-8 w-8 text-white" />
                  </div>
                  <div className={isRtl ? 'text-right' : ''}>
                    <p className={`text-white/80 text-sm font-medium ${isRtl ? 'font-arabic' : ''}`}>
                      {isRtl ? 'نسبة الامتثال الإجمالية' : 'Overall Compliance'}
                    </p>
                    <p className="text-white font-bold text-2xl">{domainMetrics.averagePercentage.toFixed(1)}%</p>
                    <p className={`text-white/60 text-xs ${isRtl ? 'font-arabic' : ''}`}>
                      {domainMetrics.complianceStatus}
                    </p>
                  </div>
                </div>

                {/* Total Specifications */}
                <div className={`flex items-center gap-4 p-6 bg-white/15 rounded-3xl backdrop-blur-sm border border-white/20 shadow-2xl hover:bg-white/20 transition-all duration-300 ${isRtl ? 'flex-row-reverse' : ''}`}>
                  <div className="p-4 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-2xl shadow-xl">
                    <FileText className="h-8 w-8 text-white" />
                  </div>
                  <div className={isRtl ? 'text-right' : ''}>
                    <p className={`text-white/80 text-sm font-medium ${isRtl ? 'font-arabic' : ''}`}>
                      {isRtl ? 'إجمالي المواصفات' : 'Total Specifications'}
                    </p>
                    <p className="text-white font-bold text-2xl">{domainMetrics.totalSpecs}</p>
                    <p className={`text-white/60 text-xs ${isRtl ? 'font-arabic' : ''}`}>
                      {isRtl ? 'مواصفة' : 'specifications'}
                    </p>
                  </div>
                </div>

                {/* Rated Specifications */}
                <div className={`flex items-center gap-4 p-6 bg-white/15 rounded-3xl backdrop-blur-sm border border-white/20 shadow-2xl hover:bg-white/20 transition-all duration-300 ${isRtl ? 'flex-row-reverse' : ''}`}>
                  <div className="p-4 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl shadow-xl">
                    <Star className="h-8 w-8 text-white" />
                  </div>
                  <div className={isRtl ? 'text-right' : ''}>
                    <p className={`text-white/80 text-sm font-medium ${isRtl ? 'font-arabic' : ''}`}>
                      {isRtl ? 'المواصفات المقيمة' : 'Rated Specifications'}
                    </p>
                    <p className="text-white font-bold text-2xl">{domainMetrics.ratedSpecs}</p>
                    <p className={`text-white/60 text-xs ${isRtl ? 'font-arabic' : ''}`}>
                      {domainMetrics.totalSpecs > 0 ? 
                        `${((domainMetrics.ratedSpecs / domainMetrics.totalSpecs) * 100).toFixed(0)}% ${isRtl ? 'مكتمل' : 'complete'}` :
                        (isRtl ? 'لا يوجد' : 'none')
                      }
                    </p>
                  </div>
                </div>

                {/* Assessment Progress */}
                <div className={`flex items-center gap-4 p-6 bg-white/15 rounded-3xl backdrop-blur-sm border border-white/20 shadow-2xl hover:bg-white/20 transition-all duration-300 ${isRtl ? 'flex-row-reverse' : ''}`}>
                  <div className="p-4 bg-gradient-to-r from-orange-500 to-red-500 rounded-2xl shadow-xl">
                    <Target className="h-8 w-8 text-white" />
                  </div>
                  <div className={isRtl ? 'text-right' : ''}>
                    <p className={`text-white/80 text-sm font-medium ${isRtl ? 'font-arabic' : ''}`}>
                      {isRtl ? 'تقدم التقييم' : 'Assessment Progress'}
                    </p>
                    <p className="text-white font-bold text-2xl">
                      {domainMetrics.totalSpecs > 0 ? 
                        `${((domainMetrics.ratedSpecs / domainMetrics.totalSpecs) * 100).toFixed(0)}%` : 
                        '0%'
                      }
                    </p>
                    <p className={`text-white/60 text-xs ${isRtl ? 'font-arabic' : ''}`}>
                      {domainMetrics.totalSpecs - domainMetrics.ratedSpecs} {isRtl ? 'متبقي' : 'remaining'}
                    </p>
                  </div>
                </div>

                {/* Domain Weight KPI */}
                {domainWeight.isSet && (
                  <div className={`flex items-center gap-4 p-6 bg-white/15 rounded-3xl backdrop-blur-sm border border-white/20 shadow-2xl hover:bg-white/20 transition-all duration-300 ${isRtl ? 'flex-row-reverse' : ''}`}>
                    <div className="p-4 bg-gradient-to-r from-teal-500 to-cyan-500 rounded-2xl shadow-xl">
                      <Weight className="h-8 w-8 text-white" />
                    </div>
                    <div className={isRtl ? 'text-right' : ''}>
                      <p className={`text-white/80 text-sm font-medium ${isRtl ? 'font-arabic' : ''}`}>
                        {isRtl ? 'وزن المجال' : 'Domain Weight'}
                      </p>
                      <p className="text-white font-bold text-2xl">{domainWeight.display}</p>
                      <p className={`text-white/60 text-xs ${isRtl ? 'font-arabic' : ''}`}>
                        {isRtl ? 'من إجمالي التقييم' : 'of total assessment'}
                      </p>
                    </div>
                  </div>
                )}
              </motion.div>

              {/* Compliance Distribution Chart */}
              {domainMetrics.totalSpecs > 0 && (
                <motion.div 
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.8 }}
                  className="mt-8 p-6 bg-white/10 rounded-3xl backdrop-blur-sm border border-white/20 shadow-2xl"
                >
                  <h4 className={`text-white font-bold text-xl mb-6 ${isRtl ? 'font-arabic text-right' : ''}`}>
                    {isRtl ? 'توزيع حالة الامتثال' : 'Compliance Status Distribution'}
                  </h4>
                  
                                     <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                     {Object.entries(domainMetrics.distributionByStatus)
                       .filter(([_status, count]) => count > 0) // Only show statuses that exist
                       .map(([status, count]) => {
                         const percentage = (count / domainMetrics.totalSpecs) * 100;
                         const config = getComplianceConfig(status);
                         const StatusIcon = config.icon;
                         
                         return (
                           <div key={status} className={`p-4 bg-white/10 rounded-2xl border border-white/20 ${isRtl ? 'text-right' : ''}`}>
                             <div className={`flex items-center gap-3 mb-3 ${isRtl ? 'flex-row-reverse' : ''}`}>
                               <div className={`p-2 bg-gradient-to-r ${config.gradient} rounded-xl`}>
                                 <StatusIcon className="h-4 w-4 text-white" />
                               </div>
                               <span className={`text-white/90 text-sm font-medium ${isRtl ? 'font-arabic' : ''}`}>
                                 {config.label}
                               </span>
                             </div>
                             <div className="space-y-2">
                               <div className={`flex justify-between ${isRtl ? 'flex-row-reverse' : ''}`}>
                                 <span className="text-white font-bold text-lg">{count}</span>
                                 <span className="text-white/60 text-sm">{percentage.toFixed(0)}%</span>
                               </div>
                               <div className="w-full h-2 bg-white/20 rounded-full overflow-hidden">
                                 <div 
                                   className={`h-full bg-gradient-to-r ${config.gradient} rounded-full transition-all duration-500`}
                                   style={{ width: `${percentage}%` }}
                                 />
                               </div>
                             </div>
                           </div>
                         );
                       })}
                  </div>
                </motion.div>
              )}
            </div>
          </motion.div>
        </div>
      </HeroHeader>

      {/* Main Content */}
      <div className="w-full">
        {/* Enhanced Filter Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          variants={contentVariants}
          className="mb-12 px-6 max-w-7xl mx-auto"
        >
          <Card className="border-0 shadow-2xl bg-gradient-to-br from-[#003874] via-[#2D8DC6] to-[#48D3A5] overflow-hidden">
            <CardHeader className="relative p-8">
              {/* Enhanced Decorative Elements */}
              <div className="absolute top-0 right-0 w-80 h-80 bg-white/10 rounded-full blur-3xl -translate-y-40 translate-x-40" />
              <div className="absolute bottom-0 left-0 w-60 h-60 bg-white/5 rounded-full blur-2xl translate-y-30 -translate-x-30" />
              <div className="absolute top-1/2 left-1/2 w-40 h-40 bg-white/5 rounded-full blur-xl -translate-x-1/2 -translate-y-1/2" />
              
              <div className="relative z-10">
                <div className={`flex items-center justify-between mb-8 ${isRtl ? 'flex-row-reverse' : ''}`}>
                  <div className={`flex items-center gap-6 ${isRtl ? 'flex-row-reverse' : ''}`}>
                    <div className="p-4 bg-white/20 rounded-3xl backdrop-blur-sm border border-white/20 shadow-lg">
                      <Grid3X3 className="h-10 w-10 text-white" />
                    </div>
                    <div className={isRtl ? 'text-right' : ''}>
                      <CardTitle className={`text-3xl font-bold text-white mb-2 ${isRtl ? 'font-arabic' : ''}`}>
                        {t('title')}
                      </CardTitle>
                      <p className={`text-white/90 text-lg ${isRtl ? 'font-arabic' : ''}`}>
                        {isRtl 
                          ? `تم العثور على ${filteredSpecifications.length} مواصفة`
                          : `${filteredSpecifications.length} specifications found`
                        }
                      </p>
                    </div>
                  </div>
                  
                  <div className={`flex items-center gap-4 ${isRtl ? 'flex-row-reverse' : ''}`}>
                    {/* Current Filter Results */}
                    <div className={`flex items-center gap-3 p-4 bg-white/15 rounded-2xl backdrop-blur-sm border border-white/20 ${isRtl ? 'flex-row-reverse' : ''}`}>
                      <div className="p-3 bg-white/25 rounded-xl">
                        <Star className="h-5 w-5 text-white" />
                      </div>
                      <div className={isRtl ? 'text-right' : ''}>
                        <p className={`text-white/80 text-sm font-medium ${isRtl ? 'font-arabic' : ''}`}>
                          {isRtl ? 'المفلترة' : 'Filtered'}
                        </p>
                        <p className="text-white font-bold text-lg">
                          {filteredSpecifications.length}/{specifications.length}
                        </p>
                      </div>
                    </div>

                    {/* Live Compliance Rate */}
                    <div className={`flex items-center gap-3 p-4 bg-white/15 rounded-2xl backdrop-blur-sm border border-white/20 ${isRtl ? 'flex-row-reverse' : ''}`}>
                      <div className="p-3 bg-gradient-to-r from-emerald-500 to-green-500 rounded-xl shadow-lg">
                        <CheckCircle2 className="h-5 w-5 text-white" />
                      </div>
                      <div className={isRtl ? 'text-right' : ''}>
                        <p className={`text-white/80 text-sm font-medium ${isRtl ? 'font-arabic' : ''}`}>
                          {isRtl ? 'معدل الامتثال المباشر' : 'Live Compliance'}
                        </p>
                        <p className="text-white font-bold text-lg">
                          {domainMetrics.averagePercentage.toFixed(1)}%
                        </p>
                      </div>
                    </div>

                    {/* Assessment Completion */}
                    <div className={`flex items-center gap-3 p-4 bg-white/15 rounded-2xl backdrop-blur-sm border border-white/20 ${isRtl ? 'flex-row-reverse' : ''}`}>
                      <div className="p-3 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-xl shadow-lg">
                        <Target className="h-5 w-5 text-white" />
                      </div>
                      <div className={isRtl ? 'text-right' : ''}>
                        <p className={`text-white/80 text-sm font-medium ${isRtl ? 'font-arabic' : ''}`}>
                          {isRtl ? 'الإنجاز' : 'Completion'}
                        </p>
                        <p className="text-white font-bold text-lg">
                          {domainMetrics.totalSpecs > 0 ? 
                            `${((domainMetrics.ratedSpecs / domainMetrics.totalSpecs) * 100).toFixed(0)}%` : 
                            '0%'
                          }
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Enhanced Filter Controls */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {/* Search */}
                  <div className="relative">
                    <Search className={`absolute top-4 h-5 w-5 text-white/70 ${isRtl ? 'right-4' : 'left-4'}`} />
                    <Input
                      placeholder={isRtl ? 'البحث في المواصفات...' : t('search')}
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className={`h-14 bg-white/20 border-white/30 text-white placeholder:text-white/70 backdrop-blur-sm rounded-2xl text-lg font-medium ${isRtl ? 'pr-12 text-right font-arabic' : 'pl-12'}`}
                    />
                  </div>

                  {/* Control Filter */}
                  <Select value={controlFilter} onValueChange={setControlFilter}>
                    <SelectTrigger className={`h-14 bg-white/20 border-white/30 text-white backdrop-blur-sm rounded-2xl text-lg font-medium ${isRtl ? 'text-right font-arabic' : ''}`}>
                      <SelectValue placeholder={isRtl ? 'اختر التحكم' : t('control')} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">{isRtl ? 'الكل' : t('all')}</SelectItem>
                      {controls.map((control: Control) => (
                        <SelectItem key={control.id} value={control.id}>
                          <span className={isRtl ? 'font-arabic' : ''}>
                            {control.name?.[locale as keyof typeof control.name] || control.name?.en || control.id}
                          </span>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>

                  {/* Sort */}
                  <Select value={sortBy} onValueChange={setSortBy}>
                    <SelectTrigger className={`h-14 bg-white/20 border-white/30 text-white backdrop-blur-sm rounded-2xl text-lg font-medium ${isRtl ? 'text-right font-arabic' : ''}`}>
                      <SelectValue placeholder={isRtl ? 'ترتيب حسب' : t('sortBy')} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="name">
                        <span className={isRtl ? 'font-arabic' : ''}>{isRtl ? 'الاسم (أ-ي)' : t('titleAsc')}</span>
                      </SelectItem>
                      <SelectItem value="level">
                        <span className={isRtl ? 'font-arabic' : ''}>{isRtl ? 'المستوى' : t('level')}</span>
                      </SelectItem>
                      <SelectItem value="control">
                        <span className={isRtl ? 'font-arabic' : ''}>{isRtl ? 'التحكم' : t('control')}</span>
                      </SelectItem>
                      <SelectItem value="id">
                        <span className={isRtl ? 'font-arabic' : ''}>{isRtl ? 'معرف المواصفة' : 'Specification ID'}</span>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardHeader>
          </Card>
        </motion.div>

        {/* Performance Dashboard */}
        {domainMetrics.totalSpecs > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.35 }}
            className="mb-8 px-6 max-w-7xl mx-auto"
          >
            <Card className="border-0 shadow-2xl bg-gradient-to-br from-white via-gray-50 to-white overflow-hidden rounded-3xl">
              <CardContent className="p-8">
                <div className={`flex items-center justify-between mb-8 ${isRtl ? 'flex-row-reverse' : ''}`}>
                  <div className={`flex items-center gap-4 ${isRtl ? 'flex-row-reverse' : ''}`}>
                    <div className="p-3 bg-gradient-to-r from-[#003874] to-[#2D8DC6] rounded-2xl shadow-lg">
                      <Layers className="h-6 w-6 text-white" />
                    </div>
                    <div className={isRtl ? 'text-right' : ''}>
                      <h3 className={`text-2xl font-bold text-gray-900 mb-1 ${isRtl ? 'font-arabic' : ''}`}>
                        {isRtl ? 'لوحة أداء المجال' : 'Domain Performance Dashboard'}
                      </h3>
                      <p className={`text-gray-600 ${isRtl ? 'font-arabic' : ''}`}>
                        {isRtl ? 'نظرة عامة شاملة على حالة الامتثال' : 'Comprehensive compliance status overview'}
                      </p>
                    </div>
                  </div>
                  
                                     <div className={`flex items-center gap-3 p-4 bg-gradient-to-r from-emerald-500 to-green-500 rounded-2xl shadow-lg ${isRtl ? 'flex-row-reverse' : ''}`}>
                     <CheckCircle2 className="h-6 w-6 text-white" />
                     <div className={isRtl ? 'text-right' : ''}>
                       <p className="text-white/90 text-sm font-medium">
                         {isRtl ? 'النسبة الإجمالية' : 'Overall Score'}
                       </p>
                       <p className="text-white font-bold text-xl">
                         {domainMetrics.averagePercentage.toFixed(1)}%
                       </p>
                       <p className="text-white/70 text-xs">
                         {isRtl ? `شامل جميع ${domainMetrics.totalSpecs} مواصفة` : `Includes all ${domainMetrics.totalSpecs} specs`}
                       </p>
                     </div>
                   </div>
                </div>

                {/* Real-time Metrics Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {/* Compliance Progress */}
                  <div className="p-6 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl border border-blue-100 shadow-lg">
                    <div className={`flex items-center gap-4 mb-4 ${isRtl ? 'flex-row-reverse' : ''}`}>
                      <div className="p-3 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-xl shadow-lg">
                        <Target className="h-5 w-5 text-white" />
                      </div>
                      <div className={isRtl ? 'text-right' : ''}>
                        <h4 className={`font-bold text-gray-900 ${isRtl ? 'font-arabic' : ''}`}>
                          {isRtl ? 'تقدم الامتثال' : 'Progress'}
                        </h4>
                        <p className="text-blue-600 font-bold text-lg">
                          {domainMetrics.totalSpecs > 0 ? 
                            `${((domainMetrics.ratedSpecs / domainMetrics.totalSpecs) * 100).toFixed(0)}%` : 
                            '0%'
                          }
                        </p>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className={`flex justify-between text-sm ${isRtl ? 'flex-row-reverse' : ''}`}>
                        <span className={`text-gray-600 ${isRtl ? 'font-arabic' : ''}`}>
                          {isRtl ? 'مكتمل' : 'Completed'}
                        </span>
                        <span className="font-semibold">{domainMetrics.ratedSpecs}</span>
                      </div>
                      <div className={`flex justify-between text-sm ${isRtl ? 'flex-row-reverse' : ''}`}>
                        <span className={`text-gray-600 ${isRtl ? 'font-arabic' : ''}`}>
                          {isRtl ? 'متبقي' : 'Remaining'}
                        </span>
                        <span className="font-semibold">{domainMetrics.totalSpecs - domainMetrics.ratedSpecs}</span>
                      </div>
                    </div>
                  </div>

                  {/* Filtered View Stats */}
                  <div className="p-6 bg-gradient-to-br from-purple-50 to-pink-50 rounded-2xl border border-purple-100 shadow-lg">
                    <div className={`flex items-center gap-4 mb-4 ${isRtl ? 'flex-row-reverse' : ''}`}>
                      <div className="p-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl shadow-lg">
                        <Filter className="h-5 w-5 text-white" />
                      </div>
                      <div className={isRtl ? 'text-right' : ''}>
                        <h4 className={`font-bold text-gray-900 ${isRtl ? 'font-arabic' : ''}`}>
                          {isRtl ? 'العرض المفلتر' : 'Filtered View'}
                        </h4>
                        <p className="text-purple-600 font-bold text-lg">
                          {filteredSpecifications.length}
                        </p>
                      </div>
                    </div>
                                           <div className="space-y-2">
                         <div className={`flex justify-between text-sm ${isRtl ? 'flex-row-reverse' : ''}`}>
                           <span className={`text-gray-600 ${isRtl ? 'font-arabic' : ''}`}>
                             {isRtl ? 'من الإجمالي' : 'of Total'}
                           </span>
                           <span className="font-semibold">{domainMetrics.totalSpecs}</span>
                         </div>
                         <div className="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
                           <div 
                             className="h-full bg-gradient-to-r from-purple-500 to-pink-500 rounded-full transition-all duration-300"
                             style={{ 
                               width: `${domainMetrics.totalSpecs > 0 ? (filteredSpecifications.length / domainMetrics.totalSpecs) * 100 : 0}%` 
                             }}
                           />
                         </div>
                       </div>
                  </div>

                  {/* Top Compliance Status */}
                  <div className="p-6 bg-gradient-to-br from-emerald-50 to-green-50 rounded-2xl border border-emerald-100 shadow-lg">
                    <div className={`flex items-center gap-4 mb-4 ${isRtl ? 'flex-row-reverse' : ''}`}>
                      <div className="p-3 bg-gradient-to-r from-emerald-500 to-green-500 rounded-xl shadow-lg">
                        <CheckCircle2 className="h-5 w-5 text-white" />
                      </div>
                      <div className={isRtl ? 'text-right' : ''}>
                        <h4 className={`font-bold text-gray-900 ${isRtl ? 'font-arabic' : ''}`}>
                          {isRtl ? 'الحالة السائدة' : 'Top Status'}
                        </h4>
                                                 <p className="text-emerald-600 font-bold text-sm">
                           {(() => {
                             // Filter out notCompleted and empty statuses for "top status"
                             const validStatuses = Object.entries(domainMetrics.distributionByStatus)
                               .filter(([_status, count]) => _status && _status !== 'notCompleted' && _status !== 'Not Completed' && _status !== '' && count > 0);
                             
                             if (validStatuses.length === 0) {
                               return isRtl ? 'لا توجد تقييمات' : 'No ratings yet';
                             }
                             
                             const maxStatus = validStatuses
                               .reduce((max, [status, count]) => count > max.count ? { status, count } : max, { status: '', count: 0 });
                             return getComplianceConfig(maxStatus.status).label;
                           })()}
                         </p>
                       </div>
                     </div>
                     <div className="text-sm text-gray-600">
                       <span className="font-semibold">
                         {(() => {
                           // Filter out notCompleted and empty statuses for count
                           const validStatuses = Object.entries(domainMetrics.distributionByStatus)
                             .filter(([_status, count]) => _status && _status !== 'notCompleted' && _status !== 'Not Completed' && _status !== '' && count > 0);
                           
                           if (validStatuses.length === 0) {
                             return `0 ${isRtl ? 'مواصفة' : 'specifications'}`;
                           }
                           
                           const maxStatus = validStatuses
                             .reduce((max, [status, count]) => count > max.count ? { status, count } : max, { status: '', count: 0 });
                           return `${maxStatus.count} ${isRtl ? 'مواصفة' : 'specifications'}`;
                         })()}
                       </span>
                     </div>
                  </div>

                  {/* Risk Indicator */}
                  <div className="p-6 bg-gradient-to-br from-orange-50 to-red-50 rounded-2xl border border-orange-100 shadow-lg">
                    <div className={`flex items-center gap-4 mb-4 ${isRtl ? 'flex-row-reverse' : ''}`}>
                      <div className="p-3 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl shadow-lg">
                        <AlertCircle className="h-5 w-5 text-white" />
                      </div>
                      <div className={isRtl ? 'text-right' : ''}>
                        <h4 className={`font-bold text-gray-900 ${isRtl ? 'font-arabic' : ''}`}>
                          {isRtl ? 'مؤشر المخاطر' : 'Risk Level'}
                        </h4>
                        <p className={`font-bold text-lg ${
                          domainMetrics.averagePercentage >= 80 ? 'text-green-600' :
                          domainMetrics.averagePercentage >= 60 ? 'text-yellow-600' :
                          domainMetrics.averagePercentage >= 40 ? 'text-orange-600' : 'text-red-600'
                        }`}>
                          {domainMetrics.averagePercentage >= 80 ? (isRtl ? 'منخفض' : 'Low') :
                           domainMetrics.averagePercentage >= 60 ? (isRtl ? 'متوسط' : 'Medium') :
                           domainMetrics.averagePercentage >= 40 ? (isRtl ? 'عالي' : 'High') : 
                           (isRtl ? 'حرج' : 'Critical')}
                        </p>
                      </div>
                    </div>
                    <div className="text-sm text-gray-600">
                      <span className={isRtl ? 'font-arabic' : ''}>
                        {isRtl ? 'بناءً على نسبة الامتثال' : 'Based on compliance rate'}
                      </span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}

        {/* Enhanced Specifications Table */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          variants={contentVariants}
          className="w-full px-0"
        >
          <Card className="border-0 shadow-2xl bg-white/80 backdrop-blur-sm overflow-hidden rounded-none md:rounded-3xl md:mx-6">
            <CardContent className="p-0">
              <div className="overflow-x-auto w-full">
                <Table className="w-full table-auto">
                  <TableHeader>
                    <TableRow className="bg-gradient-to-r from-[#003874]/5 via-[#2D8DC6]/5 to-[#48D3A5]/5 hover:from-[#003874]/10 hover:via-[#2D8DC6]/10 hover:to-[#48D3A5]/10 border-b-2 border-gray-200/50">
                      <TableHead className={`font-bold text-gray-900 text-base py-8 px-6 w-[12%] ${isRtl ? 'text-right' : ''}`}>
                        <div className={`flex items-center gap-3 ${isRtl ? 'flex-row-reverse' : ''}`}>
                          <div className="p-2 bg-gradient-to-r from-[#003874] to-[#2D8DC6] rounded-lg shadow-lg">
                            <FileText className="h-4 w-4 text-white" />
                          </div>
                          <span className={`text-sm font-bold ${isRtl ? 'font-arabic' : ''}`}>{isRtl ? 'معرف المواصفة' : 'Specification ID'}</span>
                        </div>
                      </TableHead>
                      <TableHead className={`font-bold text-gray-900 text-base py-8 px-6 w-[20%] ${isRtl ? 'text-right' : ''}`}>
                        <div className={`flex items-center gap-3 ${isRtl ? 'flex-row-reverse' : ''}`}>
                          <div className="p-2 bg-gradient-to-r from-[#2D8DC6] to-[#48D3A5] rounded-lg shadow-lg">
                            <Target className="h-4 w-4 text-white" />
                          </div>
                          <span className={`text-sm font-bold ${isRtl ? 'font-arabic' : ''}`}>{isRtl ? 'عنوان المواصفة' : t('specificationTitle')}</span>
                        </div>
                      </TableHead>
                      <TableHead className={`font-bold text-gray-900 text-base py-8 px-6 w-[18%] ${isRtl ? 'text-right' : ''}`}>
                        <div className={`flex items-center gap-3 ${isRtl ? 'flex-row-reverse' : ''}`}>
                          <div className="p-2 bg-gradient-to-r from-[#48D3A5] to-[#003874] rounded-lg shadow-lg">
                            <Settings className="h-4 w-4 text-white" />
                          </div>
                          <span className={`text-sm font-bold ${isRtl ? 'font-arabic' : ''}`}>{isRtl ? 'التحكم' : t('control')}</span>
                        </div>
                      </TableHead>
                      <TableHead className={`font-bold text-gray-900 text-base py-8 px-6 w-[25%] ${isRtl ? 'text-right' : ''}`}>
                        <div className={`flex items-center gap-3 ${isRtl ? 'flex-row-reverse' : ''}`}>
                          <div className="p-2 bg-gradient-to-r from-[#003874] to-[#48D3A5] rounded-lg shadow-lg">
                            <Layers className="h-4 w-4 text-white" />
                          </div>
                          <span className={`text-sm font-bold ${isRtl ? 'font-arabic' : ''}`}>{isRtl ? 'الوصف' : 'Description'}</span>
                        </div>
                      </TableHead>
                      <TableHead className={`font-bold text-gray-900 text-base py-8 px-6 w-[15%] ${isRtl ? 'text-right' : ''}`}>
                        <div className={`flex items-center gap-3 ${isRtl ? 'flex-row-reverse' : ''}`}>
                          <div className="p-2 bg-gradient-to-r from-[#2D8DC6] to-[#003874] rounded-lg shadow-lg">
                            <Shield className="h-4 w-4 text-white" />
                          </div>
                          <span className={`text-sm font-bold ${isRtl ? 'font-arabic' : ''}`}>{isRtl ? 'حالة الامتثال' : t('complianceStatus')}</span>
                        </div>
                      </TableHead>
                      <TableHead className={`font-bold text-gray-900 text-base py-8 px-6 w-[10%] ${isRtl ? 'text-right' : ''}`}>
                        <div className={`flex items-center gap-3 ${isRtl ? 'flex-row-reverse' : ''}`}>
                          <div className="p-2 bg-gradient-to-r from-[#48D3A5] to-[#2D8DC6] rounded-lg shadow-lg">
                            <Eye className="h-4 w-4 text-white" />
                          </div>
                          <span className={`text-sm font-bold ${isRtl ? 'font-arabic' : ''}`}>{isRtl ? 'الإجراءات' : 'Actions'}</span>
                        </div>
                      </TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredSpecifications.map((spec: SpecificationWithControl, index: number) => {
                      const statusConfig = getComplianceConfig(spec.complianceStatus || '');
                      const StatusIcon = statusConfig.icon;

                      return (
                        <motion.tr
                          key={spec.id}
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: 0.05 * index }}
                          className="hover:bg-gradient-to-r hover:from-blue-50/50 hover:via-indigo-50/30 hover:to-purple-50/50 transition-all duration-500 border-b border-gray-100/50 group"
                        >
                          <TableCell className="font-mono text-sm text-gray-600 py-8 px-6 w-[12%]">
                            <div className={`flex items-center gap-3 ${isRtl ? 'flex-row-reverse' : ''}`}>
                              <div className="w-3 h-3 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full shadow-lg"></div>
                              <span className="font-semibold text-gray-800 group-hover:text-blue-700 transition-colors text-xs">
                                {spec.id}
                              </span>
                            </div>
                          </TableCell>
                          
                          <TableCell className={`py-8 px-6 w-[20%] ${isRtl ? 'text-right' : ''}`}>
                            <div>
                              <p className={`font-bold text-gray-900 text-base leading-tight mb-2 group-hover:text-blue-800 transition-colors ${isRtl ? 'font-arabic' : ''}`}>
                                {spec.name?.[locale as keyof typeof spec.name] || 
                                 spec.name?.en || 
                                 spec.id}
                              </p>
                              <p className={`text-gray-500 text-xs font-medium ${isRtl ? 'font-arabic' : ''}`}>
                                {isRtl ? 'مواصفة' : 'Specification'}
                              </p>
                            </div>
                          </TableCell>

                          <TableCell className={`py-8 px-6 w-[18%] ${isRtl ? 'text-right' : ''}`}>
                            <div className={`flex items-center gap-3 ${isRtl ? 'flex-row-reverse' : ''}`}>
                              <div className="p-2 bg-gradient-to-r from-[#003874] to-[#2D8DC6] rounded-xl shadow-lg flex-shrink-0">
                                <Settings className="h-4 w-4 text-white" />
                              </div>
                              <div className="min-w-0">
                                <p className={`font-bold text-gray-900 text-sm mb-1 group-hover:text-blue-800 transition-colors truncate ${isRtl ? 'font-arabic' : ''}`}>
                                  {spec.control?.name?.[locale as keyof typeof spec.control.name] || 
                                   spec.control?.name?.en || 
                                   spec.controlId || 
                                   (isRtl ? 'تحكم غير معروف' : 'Unknown Control')}
                                </p>
                                <p className={`text-gray-500 text-xs truncate ${isRtl ? 'font-arabic' : ''}`}>
                                  {isRtl ? 'معرف التحكم:' : 'ID:'} {spec.controlId}
                                </p>
                              </div>
                            </div>
                          </TableCell>

                          <TableCell className={`py-8 px-6 w-[25%] ${isRtl ? 'text-right' : ''}`}>
                            <p className={`text-gray-700 text-sm leading-relaxed group-hover:text-gray-800 transition-colors ${isRtl ? 'font-arabic' : ''}`}>
                              {spec.description?.[locale as keyof typeof spec.description] || 
                               spec.description?.en || 
                               (isRtl ? 'لا يوجد وصف متاح' : 'No description available')}
                            </p>
                          </TableCell>

                          <TableCell className="py-8 px-6 w-[15%]">
                            <div className="space-y-2">
                              <Badge className={`${statusConfig.color} px-3 py-2 text-xs font-bold border-2 shadow-lg flex items-center gap-2 w-fit rounded-xl transition-all duration-300 hover:scale-105`}>
                                <StatusIcon className="h-3 w-3 flex-shrink-0" />
                                <span className={`truncate ${isRtl ? 'font-arabic' : ''}`}>{statusConfig.label}</span>
                              </Badge>
                              <div className="flex items-center gap-2">
                                <div className="text-xs font-semibold text-gray-600">
                                  {statusConfig.value || getCompliancePercentage(spec.complianceStatus || 'notCompleted', assessmentCriteria)}%
                                </div>
                                <div className="flex-1 h-1.5 bg-gray-200 rounded-full overflow-hidden">
                                  <div 
                                    className={`h-full bg-gradient-to-r ${statusConfig.gradient || 'from-gray-400 to-gray-500'} rounded-full transition-all duration-300`}
                                    style={{ 
                                      width: `${statusConfig.value || getCompliancePercentage(spec.complianceStatus || 'notCompleted', assessmentCriteria)}%` 
                                    }}
                                  />
                                </div>
                              </div>
                            </div>
                          </TableCell>

                          <TableCell className="py-8 px-6 w-[10%]">
                            <div className={`flex items-center justify-center ${isRtl ? 'flex-row-reverse' : ''}`}>
                              <Button 
                                variant="outline" 
                                size="sm"
                                onClick={() => router.push(`/${locale}/maturity-assessment/${projectId}/${assessmentId}/${domainName}/specification/${spec.id}`)}
                                className={`h-10 px-4 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 hover:border-blue-300 transition-all duration-300 rounded-xl font-semibold shadow-sm hover:shadow-lg text-xs ${isRtl ? 'flex-row-reverse font-arabic' : ''}`}
                              >
                                <Eye className={`h-3 w-3 ${isRtl ? 'ml-1' : 'mr-1'}`} />
                                <span className="hidden lg:inline">{isRtl ? 'عرض' : 'View'}</span>
                              </Button>
                            </div>
                          </TableCell>
                        </motion.tr>
                      );
                    })}
                  </TableBody>
                </Table>
              </div>

              {filteredSpecifications.length === 0 && (
                <div className="text-center py-24">
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.5 }}
                  >
                    <FileText className="h-24 w-24 mx-auto mb-8 text-gray-300" />
                    <h3 className={`text-3xl font-bold text-gray-900 mb-4 ${isRtl ? 'font-arabic' : ''}`}>
                      {isRtl ? 'لا توجد مواصفات' : t('noSpecifications')}
                    </h3>
                    <p className={`text-gray-600 text-xl mb-8 max-w-md mx-auto ${isRtl ? 'font-arabic' : ''}`}>
                      {searchTerm || controlFilter !== 'all'
                        ? (isRtl ? 'لا توجد مواصفات تطابق معايير البحث الخاصة بك' : 'No specifications match your search criteria')
                        : (isRtl ? 'لا توجد أي مواصفات محددة لهذا المجال حتى الآن' : t('noSpecificationsDescription'))
                      }
                    </p>
                    {(searchTerm || controlFilter !== 'all') && (
                      <Button
                        variant="outline"
                        onClick={() => {
                          setSearchTerm('');
                          setControlFilter('all');
                        }}
                        className={`${isRtl ? 'font-arabic' : ''} bg-gradient-to-r from-[#003874] to-[#2D8DC6] text-white border-0 hover:shadow-lg`}
                      >
                        {isRtl ? 'مسح المرشحات' : 'Clear Filters'}
                      </Button>
                    )}
                  </motion.div>
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </motion.div>
  );
} 
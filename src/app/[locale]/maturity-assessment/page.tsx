"use client";

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { usePathname } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { Plus, AlertCircle, Loader2, FileText } from 'lucide-react';
import { HeroHeader } from '@/components/shared/HeroHeader';
import { Button } from '@/components/ui/button';
import { AssessmentFormModal } from '@/components/ui/assessment-cards/AssessmentFormModal';
import { AssessmentCard } from '@/components/ui/assessment-cards/AssessmentCard';
import { useFrameworks } from '@/hooks/useFrameworks';
import { useAssessments } from '@/hooks/useAssessments';
import { toast } from 'sonner';

// Animation variants
const pageVariants = {
    hidden: { opacity: 0 },
    visible: {
        opacity: 1,
        transition: {
            duration: 0.6,
            staggerChildren: 0.1
        }
    }
};

const gridVariants = {
    hidden: { opacity: 0 },
    visible: {
        opacity: 1,
        transition: {
            staggerChildren: 0.15,
            delayChildren: 0.2
        }
    }
};

const emptyStateVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
        opacity: 1,
        y: 0,
        transition: {
            duration: 0.6,
            ease: "easeOut"
        }
    }
};

export default function ComplianceAssessmentPage() {
    const pathname = usePathname();
    const locale = pathname.split('/')[1] as 'en' | 'ar';
    const isRtl = locale === 'ar';

    // Get translations
    const t = useTranslations('ComplianceAssessment');

    // State management
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [currentProjectId, setCurrentProjectId] = useState<string | null>(null);

    // Custom hooks
    const { frameworks, loading: frameworksLoading } = useFrameworks();
    const { assessments, loading: assessmentsLoading, refetch: refetchAssessments } = useAssessments(currentProjectId);

    // Get current project ID from localStorage
    useEffect(() => {
        const projectId = localStorage.getItem('currentProjectId');
        setCurrentProjectId(projectId);
    }, []);

    const handleAssessmentCreated = () => {
        refetchAssessments();
        toast.success(t('successCreate'));
    };

    const openModal = () => {
        if (!currentProjectId) {
            toast.error(t('projectRequired'));
            return;
        }
        setIsModalOpen(true);
    };

    return (
        <motion.div
            initial="hidden"
            animate="visible"
            variants={pageVariants}
            className="min-h-screen bg-gradient-to-br from-gray-50/50 via-blue-50/30 to-emerald-50/50"
        >
            {/* Hero Header with Action Button */}
            <HeroHeader
                title={t('pageTitle')}
                description={t('description')}
                isRTL={isRtl}
                backgroundClassName="bg-gradient-to-r from-[#003874] via-[#2D8DC6] to-[#48D3A5]"
            >
                {/* Add Assessment Button in Hero */}
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.4 }}
                    className={`flex gap-4 ${isRtl ? 'flex-row-reverse' : ''}`}
                >
                    <Button
                        onClick={openModal}
                        size="lg"
                        className="bg-white/20 backdrop-blur-md text-white hover:bg-white/30 transition-all duration-300 border border-white/30 hover:border-white/50 shadow-lg hover:shadow-xl px-8 py-3 text-lg"
                        disabled={!currentProjectId}
                    >
                        <Plus className={`h-6 w-6 ${isRtl ? 'ml-3' : 'mr-3'}`} />
                        {t('addAssessmentButton')}
                    </Button>
                </motion.div>
            </HeroHeader>

            {/* Enhanced Main Content */}
            <div className="container mx-auto px-6 py-16 relative">
                {/* Background decorative elements */}
                <div className="absolute inset-0 overflow-hidden pointer-events-none">
                    <div className="absolute top-20 left-10 w-72 h-72 bg-gradient-to-br from-[#48D3A5]/5 to-transparent rounded-full blur-3xl" />
                    <div className="absolute bottom-20 right-10 w-96 h-96 bg-gradient-to-br from-[#003874]/5 to-transparent rounded-full blur-3xl" />
                </div>
                {/* Project Warning */}
                {!currentProjectId && (
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="mb-8"
                    >
                        <div className="bg-yellow-50 border border-yellow-200 rounded-xl p-6 flex items-center gap-4">
                            <AlertCircle className="h-6 w-6 text-yellow-600 flex-shrink-0" />
                            <div>
                                <h3 className="font-semibold text-yellow-800 mb-1">Project Required</h3>
                                <p className="text-yellow-700">
                                    {t('projectRequired')}
                                </p>
                            </div>
                        </div>
                    </motion.div>
                )}

                {/* Loading State */}
                {assessmentsLoading && currentProjectId && (
                    <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        className="flex items-center justify-center py-16"
                    >
                        <div className="text-center">
                            <Loader2 className="h-12 w-12 animate-spin text-[#003874] mx-auto mb-4" />
                            <p className="text-gray-600 text-lg">Loading assessments...</p>
                        </div>
                    </motion.div>
                )}

                {/* Assessments Section */}
                {!assessmentsLoading && currentProjectId && (
                    <>
                        {/* Section Header */}
                        <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ duration: 0.5, delay: 0.2 }}
                            className={`mb-8 ${isRtl ? 'text-right' : ''}`}
                        >
                            <h2 className={`text-3xl font-bold text-gray-900 mb-2 ${isRtl ? 'font-arabic' : ''}`}>
                                {t('assessments')}
                            </h2>
                            <p className={`text-gray-600 text-lg ${isRtl ? 'font-arabic' : ''}`}>
                                Manage your compliance assessments and track progress
                            </p>
                        </motion.div>

                        {/* Enhanced Assessments Grid */}
                        {assessments.length > 0 ? (
                            <motion.div
                                variants={gridVariants}
                                className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-10 lg:gap-12"
                            >
                                {assessments.map((assessment) => (
                                    <AssessmentCard
                                        key={assessment.id}
                                        assessment={assessment}
                                        locale={locale}
                                        projectId={currentProjectId}
                                    />
                                ))}
                            </motion.div>
                        ) : (
                            /* Enhanced Empty State */
                            <motion.div
                                variants={emptyStateVariants}
                                className="text-center py-20"
                            >
                                <div className="max-w-lg mx-auto">
                                    <div className="mb-8">
                                        <div className="relative w-32 h-32 bg-gradient-to-br from-[#003874]/10 via-[#2D8DC6]/10 to-[#48D3A5]/10 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-2xl">
                                            <FileText className="h-16 w-16 text-[#003874]" />
                                            {/* Floating rings */}
                                            <div className="absolute inset-0 rounded-3xl border-2 border-[#48D3A5]/20 scale-110 animate-pulse" />
                                            <div className="absolute inset-0 rounded-3xl border border-[#2D8DC6]/30 scale-125 animate-ping" />
                                        </div>
                                    </div>
                                    <h3 className={`text-3xl font-black text-gray-900 mb-4 ${isRtl ? 'font-arabic' : ''}`}>
                                        {t('noAssessments')}
                                    </h3>
                                    <p className={`text-gray-600 mb-8 leading-relaxed text-lg ${isRtl ? 'font-arabic' : ''}`}>
                                        {t('noAssessmentsDescription')}
                                    </p>
                                    <Button
                                        onClick={openModal}
                                        size="lg"
                                        className="bg-gradient-to-r from-[#003874] via-[#2D8DC6] to-[#48D3A5] hover:from-[#002a5c] hover:via-[#2680b8] hover:to-[#41c093] transition-all duration-500 px-10 py-4 text-lg font-bold shadow-2xl hover:shadow-3xl hover:scale-105"
                                    >
                                        <Plus className={`h-6 w-6 ${isRtl ? 'ml-3' : 'mr-3'}`} />
                                        {t('addAssessmentButton')}
                                    </Button>
                                </div>
                            </motion.div>
                        )}
                    </>
                )}
            </div>

            {/* Assessment Form Modal */}
            <AssessmentFormModal
                isOpen={isModalOpen}
                onClose={() => setIsModalOpen(false)}
                onSuccess={handleAssessmentCreated}
                projectId={currentProjectId}
                frameworks={frameworks}
                frameworksLoading={frameworksLoading}
                locale={locale}
            />
        </motion.div>
    );
}

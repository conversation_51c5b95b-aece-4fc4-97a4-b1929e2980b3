{"name": "nextjs-15-template", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@google/generative-ai": "^0.24.1", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toast": "^1.2.13", "@radix-ui/react-tooltip": "^1.2.7", "@types/lodash": "^4.17.16", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "exceljs": "^4.4.0", "firebase": "^11.7.3", "framer-motion": "^12.12.1", "lodash": "^4.17.21", "lucide-react": "^0.479.0", "next": "15.2.4", "next-intl": "^4.1.0", "next-themes": "^0.4.6", "react": "19.0.0", "react-day-picker": "^9.7.0", "react-dom": "19.0.0", "react-hook-form": "^7.56.4", "react-schemaorg": "^2.0.0", "recharts": "^2.15.3", "schema-dts": "^1.1.5", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.28"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@tailwindcss/postcss": "^4.1.6", "@types/exceljs": "^0.5.3", "@types/node": "^20.17.46", "@types/react": "19.0.12", "@types/react-dom": "19.0.4", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "eslint": "^9.26.0", "eslint-config-next": "15.2.3", "eslint-plugin-unused-imports": "^4.1.4", "tailwindcss": "^4.1.6", "typescript": "^5.8.3"}, "overrides": {"@types/react": "19.0.12", "@types/react-dom": "19.0.4"}}
# Compliance Assessment Data Services Documentation

## Overview
This document outlines the data structures and services used in the Compliance Assessment system. Each service manages specific types of data with defined schemas and relationships.

---

## 1. Framework Data Service (`useDomainSpecifications`)

### Purpose
Manages the hierarchical framework structure including domains, controls, and specifications.

### Data Sources
- `frameworks/{frameworkId}/domains`
- `frameworks/{frameworkId}/domains/{domainId}/controls`
- `frameworks/{frameworkId}/domains/{domainId}/controls/{controlId}/specifications`

### Data Structure

#### Domain Data
```json
{
  "id": "governance",
  "name": {
    "en": "Governance and Risk Management",
    "ar": "الحوكمة وإدارة المخاطر"
  },
  "description": {
    "en": "Policies and procedures for organizational governance",
    "ar": "السياسات والإجراءات للحوكمة التنظيمية"
  },
  "score": 75,
  "maxScore": 100,
  "specifications": []
}
```

#### Control Data
```json
{
  "id": "GRC-001",
  "name": {
    "en": "Risk Assessment Framework",
    "ar": "إطار تقييم المخاطر"
  },
  "description": {
    "en": "Systematic approach to identifying and assessing risks",
    "ar": "نهج منهجي لتحديد وتقييم المخاطر"
  }
}
```

#### Specification Data
```json
{
  "id": "SPEC-GRC-001-01",
  "name": {
    "en": "Risk Register Maintenance",
    "ar": "صيانة سجل المخاطر"
  },
  "description": {
    "en": "Regular updates and reviews of organizational risk register",
    "ar": "التحديثات والمراجعات المنتظمة لسجل المخاطر التنظيمية"
  },
  "domainId": "governance",
  "controlId": "GRC-001",
  "control": {
    "id": "GRC-001",
    "name": {
      "en": "Risk Assessment Framework",
      "ar": "إطار تقييم المخاطر"
    }
  },
  "maturityLevel": 3,
  "percentageValue": 75,
  "complianceStatus": "Partially Compliant",
  "currentRating": 75,
  "subSpecifications": [
    {
      "id": "SUB-SPEC-001",
      "name": {
        "en": "Quarterly Risk Review",
        "ar": "مراجعة المخاطر الفصلية"
      },
      "description": {
        "en": "Conduct comprehensive risk assessment every quarter",
        "ar": "إجراء تقييم شامل للمخاطر كل ربع سنة"
      },
      "versionHistory": [
        {
          "version": "1.2",
          "date": "2024-01-15"
        }
      ],
      "updatedAt": "2024-01-15T10:30:00Z"
    }
  ]
}
```

---

## 2. Assessment Criteria Service (`useAssessmentCriteria`)

### Purpose
Manages rating criteria, compliance levels, and domain weights for assessments.

### Data Source
- `assessmentCriteria/{frameworkId}`

### Data Structure

#### Assessment Criteria Data
```json
{
  "id": "iso27001-2022",
  "name": {
    "en": "ISO 27001:2022 Assessment Criteria",
    "ar": "معايير تقييم ISO 27001:2022"
  },
  "type": "compliance",
  "version": "1.0",
  "levels": {
    "level1": {
      "value": 0,
      "label": {
        "en": "Non-Compliant",
        "ar": "غير ملتزم"
      },
      "description": {
        "en": "No evidence of implementation",
        "ar": "لا يوجد دليل على التنفيذ"
      },
      "color": "#ef4444"
    },
    "level2": {
      "value": 25,
      "label": {
        "en": "Partially Compliant",
        "ar": "ملتزم جزئياً"
      },
      "description": {
        "en": "Some implementation but significant gaps",
        "ar": "بعض التنفيذ ولكن توجد فجوات كبيرة"
      },
      "color": "#f59e0b"
    },
    "level3": {
      "value": 75,
      "label": {
        "en": "Largely Compliant",
        "ar": "ملتزم إلى حد كبير"
      },
      "description": {
        "en": "Good implementation with minor gaps",
        "ar": "تنفيذ جيد مع فجوات طفيفة"
      },
      "color": "#3b82f6"
    },
    "level4": {
      "value": 100,
      "label": {
        "en": "Fully Compliant",
        "ar": "ملتزم بالكامل"
      },
      "description": {
        "en": "Complete implementation and evidence",
        "ar": "تنفيذ كامل وأدلة"
      },
      "color": "#10b981"
    }
  },
  "domainWeights": [
    {
      "domainId": "governance",
      "weight": 25
    },
    {
      "domainId": "security-controls",
      "weight": 40
    },
    {
      "domainId": "incident-management",
      "weight": 20
    },
    {
      "domainId": "business-continuity",
      "weight": 15
    }
  ],
  "createdAt": "2024-01-01T00:00:00Z",
  "updatedAt": "2024-01-15T10:30:00Z"
}
```

---

## 3. Ratings Data Service (Direct Firebase Calls)

### Purpose
Manages individual specification ratings and compliance status.

### Data Sources
- Primary: `projects/{projectId}/ComplianceAssessment/{assessmentId}/ratings/{specificationId}`
- Fallback: `projects/{projectId}/ratings` (legacy)

### Data Structure

#### Individual Rating Data
```json
{
  "ratingType": "compliance",
  "specificationId": "SPEC-GRC-001-01",
  "complianceStatus": "Partially Compliant",
  "targetStatus": "Compliant",
  "currentRating": 75,
  "targetRating": 100,
  "comments": "Risk register is maintained but needs more frequent updates. Quarterly reviews are conducted but documentation could be improved.",
  "domainName": {
    "en": "Governance and Risk Management",
    "ar": "الحوكمة وإدارة المخاطر"
  },
  "controlName": {
    "en": "Risk Assessment Framework",
    "ar": "إطار تقييم المخاطر"
  },
  "evidence": [
    {
      "type": "document",
      "name": "Risk Register Q1 2024",
      "url": "/documents/risk-register-q1-2024.pdf"
    }
  ],
  "assessmentDate": "2024-01-15T14:30:00Z",
  "updatedAt": "2024-01-15T14:30:00Z",
  "updatedBy": "user123",
  "createdAt": "2024-01-10T09:00:00Z",
  "createdBy": "user123"
}
```

#### Bulk Ratings Collection
```json
{
  "SPEC-GRC-001-01": {
    "complianceStatus": "Partially Compliant",
    "currentRating": 75,
    "comments": "Good progress, needs improvement"
  },
  "SPEC-GRC-001-02": {
    "complianceStatus": "Fully Compliant",
    "currentRating": 100,
    "comments": "Excellent implementation"
  },
  "SPEC-SEC-002-01": {
    "complianceStatus": "Non-Compliant",
    "currentRating": 0,
    "comments": "Not implemented yet"
  }
}
```

---

## 4. Assessment Metadata Service (Direct Firebase Calls)

### Purpose
Manages assessment configuration and metadata.

### Data Source
- `projects/{projectId}/ComplianceAssessment/{assessmentId}`

### Data Structure

#### Assessment Document
```json
{
  "id": "assessment-2024-q1",
  "name": {
    "en": "Q1 2024 ISO 27001 Assessment",
    "ar": "تقييم ISO 27001 للربع الأول 2024"
  },
  "frameworkId": "iso27001-2022",
  "projectId": "project-abc-123",
  "status": "in-progress",
  "type": "compliance",
  "startDate": "2024-01-01T00:00:00Z",
  "targetCompletionDate": "2024-03-31T23:59:59Z",
  "assessor": {
    "id": "user123",
    "name": "John Smith",
    "email": "<EMAIL>"
  },
  "domainAverages": {
    "Governance and Risk Management": {
      "average": 78.5,
      "name": {
        "en": "Governance and Risk Management",
        "ar": "الحوكمة وإدارة المخاطر"
      },
      "totalSpecifications": 12,
      "ratedSpecifications": 10,
      "domainId": "governance",
      "weight": 25
    },
    "Security Controls": {
      "average": 65.2,
      "name": {
        "en": "Security Controls",
        "ar": "الضوابط الأمنية"
      },
      "totalSpecifications": 25,
      "ratedSpecifications": 20,
      "domainId": "security-controls",
      "weight": 40
    }
  },
  "overallScore": 71.8,
  "completionPercentage": 83.3,
  "createdAt": "2024-01-01T00:00:00Z",
  "updatedAt": "2024-01-15T14:30:00Z"
}
```

---

## 5. Domain Averages Calculation Service

### Purpose
Calculates and maintains domain-level compliance averages and overall assessment scores.

### Data Processing

#### Input Data Sources
1. All ratings from `projects/{projectId}/ComplianceAssessment/{assessmentId}/ratings`
2. All specifications from framework structure
3. Domain weights from assessment criteria

#### Calculation Logic
```json
{
  "domainCalculation": {
    "totalSpecifications": 12,
    "ratedSpecifications": 10,
    "unratedSpecifications": 2,
    "ratingSum": 850,
    "average": 70.8,
    "weightedScore": 17.7
  },
  "overallCalculation": {
    "totalWeightedScore": 71.8,
    "totalWeight": 100,
    "completionRate": 83.3
  }
}
```

#### Output Data Structure
```json
{
  "domainAverages": {
    "Governance and Risk Management": {
      "average": 78.5,
      "name": {
        "en": "Governance and Risk Management",
        "ar": "الحوكمة وإدارة المخاطر"
      },
      "totalSpecifications": 12,
      "ratedSpecifications": 10,
      "domainId": "governance",
      "weight": 25,
      "weightedContribution": 19.625
    }
  },
  "overallScore": 71.8,
  "completionPercentage": 83.3
}
```

---

## 6. Legacy Data Support

### Purpose
Maintains backward compatibility with older data structures.

### Legacy Rating Structure
```json
{
  "specifications_SPEC-GRC-001-01": {
    "ratingType": "compliance",
    "complianceStatus": "Partially Compliant",
    "currentRating": 75,
    "assessmentDate": "2024-01-15T14:30:00Z"
  }
}
```

---

## Data Relationships

### Framework Hierarchy
```
Framework
├── Domains
│   ├── Controls
│   │   ├── Specifications
│   │   │   └── Sub-Specifications
```

### Assessment Structure
```
Project
├── ComplianceAssessment
│   ├── Assessment Metadata
│   ├── Ratings Collection
│   │   └── Individual Ratings
│   └── Domain Averages
```

### Cross-References
- **Assessment** → **Framework** (via frameworkId)
- **Rating** → **Specification** (via specificationId)
- **Domain Average** → **Domain Weight** (via domainId)
- **Specification** → **Control** → **Domain** (hierarchical)

---

## Data Flow Summary

1. **Framework Data** flows from `frameworks` collection through `useDomainSpecifications`
2. **Assessment Criteria** flows from `assessmentCriteria` collection through `useAssessmentCriteria`
3. **Ratings** are stored/retrieved directly from `projects/.../ratings` collections
4. **Domain Averages** are calculated and stored in assessment documents
5. **Weights** are applied from assessment criteria to calculate weighted scores

This architecture ensures data consistency, supports multilingual content, and maintains audit trails for compliance requirements. 
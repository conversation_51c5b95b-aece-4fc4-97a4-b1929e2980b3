/**
 * Shadow Tokens
 * 
 * This file contains all shadow tokens used in the design system
 */

export const SHADOWS = {
    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    DEFAULT: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1)',
    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1)',
    '2xl': '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
    inner: 'inset 0 2px 4px 0 rgba(0, 0, 0, 0.05)',
    none: 'none',
};

// Specific shadow use cases from the button.tsx and other components
export const SHADOW_USE_CASES = {
    button: SHADOWS.sm,
    buttonHover: SHADOWS.md,
    card: SHADOWS.md,
    dropdown: SHADOWS.lg,
    modal: SHADOWS.xl,
};



/**
* Animation Keyframes
* 
* This file contains keyframes definitions used in the design system
* Includes CSS keyframes for Tailwind and Framer Motion keyframe objects
*/

// CSS keyframes for use with Tailwind animations
export const CSS_KEYFRAMES = {
    // Accordion keyframes from tailwind.config.ts
    accordionDown: {
        from: {
            height: '0',
        },
        to: {
            height: 'var(--radix-accordion-content-height)',
        },
    },
    accordionUp: {
        from: {
            height: 'var(--radix-accordion-content-height)',
        },
        to: {
            height: '0',
        },
    },
    // Common animations
    fadeIn: {
        from: {
            opacity: '0',
        },
        to: {
            opacity: '1',
        },
    },
    fadeOut: {
        from: {
            opacity: '1',
        },
        to: {
            opacity: '0',
        },
    },
    slideInFromTop: {
        from: {
            opacity: '0',
            transform: 'translateY(-10px)',
        },
        to: {
            opacity: '1',
            transform: 'translateY(0)',
        },
    },
    slideInFromBottom: {
        from: {
            opacity: '0',
            transform: 'translateY(10px)',
        },
        to: {
            opacity: '1',
            transform: 'translateY(0)',
        },
    },
    slideInFromLeft: {
        from: {
            opacity: '0',
            transform: 'translateX(-10px)',
        },
        to: {
            opacity: '1',
            transform: 'translateX(0)',
        },
    },
    slideInFromRight: {
        from: {
            opacity: '0',
            transform: 'translateX(10px)',
        },
        to: {
            opacity: '1',
            transform: 'translateX(0)',
        },
    },
    pulse: {
        '0%, 100%': {
            opacity: '1',
        },
        '50%': {
            opacity: '0.5',
        },
    },
    spin: {
        to: {
            transform: 'rotate(360deg)',
        },
    },
    ping: {
        '75%, 100%': {
            transform: 'scale(2)',
            opacity: '0',
        },
    },
    bounce: {
        '0%, 100%': {
            transform: 'translateY(-25%)',
            animationTimingFunction: 'cubic-bezier(0.8, 0, 1, 1)',
        },
        '50%': {
            transform: 'translateY(0)',
            animationTimingFunction: 'cubic-bezier(0, 0, 0.2, 1)',
        },
    },
    scale: {
        '0%, 100%': {
            transform: 'scale(1)',
        },
        '50%': {
            transform: 'scale(1.05)',
        },
    },
};

// Framer Motion keyframes for direct use with motion components
export const FRAMER_KEYFRAMES = {
    float: {
        y: [0, -10, 0],
        transition: {
            duration: 3,
            repeat: Infinity,
            repeatType: "reverse",
            ease: "easeInOut",
        },
    },
    pulse: {
        scale: [1, 1.05, 1],
        opacity: [0.7, 1, 0.7],
        transition: {
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut",
        },
    },
    orb: {
        scale: [1, 1.2, 1],
        x: [0, 100, 0],
        y: [0, 50, 0],
        transition: {
            duration: 20,
            repeat: Infinity,
            repeatType: "reverse",
        },
    },
    rotate: {
        rotate: 360,
        transition: {
            duration: 8,
            repeat: Infinity,
            ease: "linear",
        },
    },
    // Button animations
    buttonHover: {
        scale: 1.05,
    },
    buttonTap: {
        scale: 0.95,
    },
};


/**
* Animation Variants for Framer Motion
* 
* These variants can be used directly with Framer Motion components
*/

export const FADE_IN = {
    hidden: { opacity: 0 },
    visible: {
        opacity: 1,
        transition: {
            duration: 0.5
        }
    },
    exit: {
        opacity: 0,
        transition: {
            duration: 0.3
        }
    }
};

export const SLIDE_UP = {
    hidden: { opacity: 0, y: 20 },
    visible: {
        opacity: 1,
        y: 0,
        transition: {
            duration: 0.5,
            ease: "easeOut"
        }
    },
    exit: {
        opacity: 0,
        y: -20,
        transition: {
            duration: 0.3,
            ease: "easeIn"
        }
    }
};

export const SLIDE_DOWN = {
    hidden: { opacity: 0, y: -20 },
    visible: {
        opacity: 1,
        y: 0,
        transition: {
            duration: 0.5,
            ease: "easeOut"
        }
    },
    exit: {
        opacity: 0,
        y: 20,
        transition: {
            duration: 0.3,
            ease: "easeIn"
        }
    }
};

export const SLIDE_LEFT = {
    hidden: { opacity: 0, x: 20 },
    visible: {
        opacity: 1,
        x: 0,
        transition: {
            duration: 0.5,
            ease: "easeOut"
        }
    },
    exit: {
        opacity: 0,
        x: -20,
        transition: {
            duration: 0.3,
            ease: "easeIn"
        }
    }
};

export const SLIDE_RIGHT = {
    hidden: { opacity: 0, x: -20 },
    visible: {
        opacity: 1,
        x: 0,
        transition: {
            duration: 0.5,
            ease: "easeOut"
        }
    },
    exit: {
        opacity: 0,
        x: 20,
        transition: {
            duration: 0.3,
            ease: "easeIn"
        }
    }
};

export const SCALE_UP = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: {
        opacity: 1,
        scale: 1,
        transition: {
            duration: 0.5,
            ease: "easeOut"
        }
    },
    exit: {
        opacity: 0,
        scale: 0.8,
        transition: {
            duration: 0.3,
            ease: "easeIn"
        }
    }
};

// For container with multiple children that stagger their animation
export const STAGGER_CONTAINER = {
    hidden: { opacity: 0 },
    visible: {
        opacity: 1,
        transition: {
            staggerChildren: 0.1,
            delayChildren: 0.2
        }
    },
    exit: {
        opacity: 0,
        transition: {
            staggerChildren: 0.05,
            staggerDirection: -1
        }
    }
};

// For card elements in a grid
export const CARD_VARIANTS = {
    hidden: { opacity: 0, y: 20 },
    visible: {
        opacity: 1,
        y: 0,
        transition: {
            duration: 0.5
        }
    },
    hover: {
        scale: 1.02,
        transition: {
            duration: 0.2
        }
    }
};

// Hero section decorative elements
export const HERO_ORB_VARIANTS = {
    topRight: {
        initial: { x: 100, y: -100 },
        animate: {
            x: 0,
            y: 0,
            scale: [1, 1.2, 1],
            rotate: [0, 45, 0],
            transition: {
                duration: 20,
                repeat: Infinity,
                repeatType: "reverse"
            }
        }
    },
    bottomLeft: {
        initial: { y: 50 },
        animate: {
            y: [0, 20, 0],
            scale: [1, 1.1, 1],
            transition: {
                duration: 8,
                repeat: Infinity,
                repeatType: "reverse"
            }
        }
    },
    middle: {
        initial: { opacity: 0.7 },
        animate: {
            scale: [1, 1.1, 1],
            opacity: [0.7, 0.9, 0.7],
            transition: {
                duration: 12,
                repeat: Infinity,
                repeatType: "reverse"
            }
        }
    }
};

/**
* Transition Presets
* 
* Common transition configurations for animations
*/

// CSS transitions as strings
export const CSS_TRANSITIONS = {
    fast: 'all 0.15s ease',
    default: 'all 0.2s ease',
    slow: 'all 0.3s ease',
    veryFast: 'all 0.1s ease',
    verySlow: 'all 0.5s ease',
    // Component-specific transitions from button.tsx
    button: 'transition-all duration-200',
    buttonHover: 'transition-all duration-200 hover:shadow-md',
    buttonWithScale: 'transition-all duration-200 hover:scale-105 active:scale-95',
    // Custom transitions
    backgroundOnly: 'background-color 0.2s ease',
    transformOnly: 'transform 0.2s ease',
    opacityOnly: 'opacity 0.2s ease',
};

// Framer Motion transition objects
export const FRAMER_TRANSITIONS = {
    default: {
        type: 'tween',
        duration: 0.2,
        ease: 'easeOut',
    },
    easeIn: {
        type: 'tween',
        duration: 0.2,
        ease: 'easeIn',
    },
    easeOut: {
        type: 'tween',
        duration: 0.2,
        ease: 'easeOut',
    },
    easeInOut: {
        type: 'tween',
        duration: 0.3,
        ease: 'easeInOut',
    },
    spring: {
        type: 'spring',
        stiffness: 400,
        damping: 30,
    },
    springSoft: {
        type: 'spring',
        stiffness: 200,
        damping: 25,
    },
    springGentle: {
        type: 'spring',
        stiffness: 100,
        damping: 20,
    },
    stagger: {
        staggerChildren: 0.07,
        delayChildren: 0.2,
    },
    staggerFast: {
        staggerChildren: 0.05,
        delayChildren: 0.1,
    },
    staggerSlow: {
        staggerChildren: 0.1,
        delayChildren: 0.3,
    },
}; 
/** @type {import('tailwindcss').Config} */
import tailwindAnimate from "tailwindcss-animate";

const tailwindConfig = {
  content: ["./src/**/*.{js,ts,jsx,tsx}"],
  theme: {
    extend: {
      fontFamily: {
        sans: ["Rubik", "system-ui", "sans-serif"],
        rubik: ["Rubik", "system-ui", "sans-serif"],
        cairo: ["Cairo", "system-ui", "sans-serif"],
      },
      colors: {
        custom: {
          primary: "#31326A",
          secondary: "#302C64",
          white: "#FFFFFF",
          lightPurple: "#ADAAC3", 
        },
        // AWX Colors
        awx: {
          blue: "#003874",
          green: "#48D3A5",
          lightBlue: "#2D8DC6",
          lime: "#80D447",
          darkGreen: "#29B070",
          gray: "#EBEDE5",
          slate: "#7D859C",
          teal: "#006156",
          purple: "#311146",
        },
        // System colors are handled through CSS variables for theming support
        // These are defined in theme.ts and applied globally
      },
      keyframes: {
        // Accordion animations
        accordionDown: {
          from: { height: '0' },
          to: { height: 'var(--radix-accordion-content-height)' },
        },
        accordionUp: {
          from: { height: 'var(--radix-accordion-content-height)' },
          to: { height: '0' },
        },
        // Basic animations
        fadeIn: {
          from: { opacity: '0' },
          to: { opacity: '1' },
        },
        fadeOut: {
          from: { opacity: '1' },
          to: { opacity: '0' },
        },
        slideInFromTop: {
          from: { opacity: '0', transform: 'translateY(-10px)' },
          to: { opacity: '1', transform: 'translateY(0)' },
        },
        slideInFromBottom: {
          from: { opacity: '0', transform: 'translateY(10px)' },
          to: { opacity: '1', transform: 'translateY(0)' },
        },
        slideInFromLeft: {
          from: { opacity: '0', transform: 'translateX(-10px)' },
          to: { opacity: '1', transform: 'translateX(0)' },
        },
        slideInFromRight: {
          from: { opacity: '0', transform: 'translateX(10px)' },
          to: { opacity: '1', transform: 'translateX(0)' },
        },
      },
      animation: {
        accordionDown: 'accordionDown 0.2s ease-out',
        accordionUp: 'accordionUp 0.2s ease-out',
        fadeIn: 'fadeIn 0.3s ease-out',
        fadeOut: 'fadeOut 0.3s ease-out',
        slideInFromTop: 'slideInFromTop 0.3s ease-out',
        slideInFromBottom: 'slideInFromBottom 0.3s ease-out',
        slideInFromLeft: 'slideInFromLeft 0.3s ease-out',
        slideInFromRight: 'slideInFromRight 0.3s ease-out',
      },
    },
  },
  plugins: [tailwindAnimate],
};

export default tailwindConfig; 
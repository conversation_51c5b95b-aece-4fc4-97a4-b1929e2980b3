rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Helper function to check if user is authenticated
    function isAuthenticated() {
      return request.auth != null;
    }
    
    // Helper functions to check user roles
    function isAdmin() {
      return isAuthenticated() && get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'Admin';
    }
    
    function isConsultant() {
      return isAuthenticated() && get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'Consultant';
    }
    
    function isClient() {
      return isAuthenticated() && get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'Client';
    }
    
    // Allow any authenticated user to read any document (for debugging - remove in production)
    match /{document=**} {
      allow read: if isAuthenticated();
    }
    
    // Allow authenticated users to create documents in the "users" collection.
    // The {userId} wildcard means this rule applies to any document in the "users" collection.
    // The `request.auth != null` condition checks if the user making the request is authenticated.
    match /users/{userId} {
      allow create: if isAuthenticated();
      
      // Allow a user to read their own data
      allow read: if isAuthenticated() && request.auth.uid == userId;
      
      // Allow admins to read all user data
      allow read, list: if isAdmin();
      
      // Allow admins to update or delete any user
      allow update, delete: if isAdmin();
    }
    
    // Rules for organizations collection - Admin only
    match /organizations/{organizationId} {
      // Allow any authenticated user to read organizations
      allow read: if isAuthenticated();
      
      // Only allow admin users to create, update, and delete organizations
      allow create: if isAdmin();
      allow update: if isAdmin();
      allow delete: if isAdmin();
    }

    // Rules for frameworks collection - Admin only
    match /frameworks/{frameworkId} {
      // Allow any authenticated user to read frameworks
      allow read: if isAuthenticated();
      
      // Only allow admin users to create, update, and delete frameworks
      allow create: if isAdmin();
      allow update: if isAdmin();
      allow delete: if isAdmin();
      
      // Rules for domains subcollection within frameworks
      match /domains/{domainId} {
        // Allow any authenticated user to read domains
        allow read: if isAuthenticated();
        
        // Only allow admin users to create, update, and delete domains
        allow create: if isAdmin();
        allow update: if isAdmin();
        allow delete: if isAdmin();
        
        // Rules for controls subcollection within domains
        match /controls/{controlId} {
          // Allow any authenticated user to read controls
          allow read: if isAuthenticated();
          
          // Only allow admin users to create, update, and delete controls
          allow create: if isAdmin();
          allow update: if isAdmin();
          allow delete: if isAdmin();
          
          // Rules for specifications subcollection within controls
          match /specifications/{specificationId} {
            // Allow any authenticated user to read specifications
            allow read: if isAuthenticated();
            
            // Only allow admin users to create, update, and delete specifications
            allow create, update, delete: if isAdmin() || isConsultant();
          }
        }
      }
    }

    // Rules for assessmentCriteria collection
    match /assessmentCriteria/{frameworkId} {
      // Allow any authenticated user to read criteria
      allow read: if isAuthenticated();
      
      // Only allow admin users to create, update, and delete criteria
      allow create: if isAdmin();
      allow update: if isAdmin();
      allow delete: if isAdmin();
    }

    // Rules for projects collection
    match /projects/{projectId} {
      // Allow any authenticated user to read projects
      allow read: if isAuthenticated();
      
      // Only allow admin users to create, update, and delete projects
      allow create: if isAdmin();
      allow update: if isAdmin();
      allow delete: if isAdmin();
      
      // Rules for ComplianceAssessment subcollection - NEW
      match /ComplianceAssessment/{assessmentId} {
        // Allow authenticated users to read compliance assessments
        allow read: if isAuthenticated();
        
        // Allow consultants and admins to have full access to compliance assessments
        allow create, update, delete: if isConsultant() || isAdmin();
        
        // Rules for any nested collections within ComplianceAssessment
        match /{document=**} {
          allow read: if isAuthenticated();
          allow create, update, delete: if isConsultant() || isAdmin();
        }
      }
      
      // Rules for ratings subcollection
      match /ratings/{ratingId} {
        allow read: if isAuthenticated();
        allow create, update, delete: if isConsultant() || isAdmin();
      }
      
      // Rules for SWOT analysis at project level
      match /swot/{domainId} {
        allow read: if isAuthenticated();
        allow create, update, delete: if isConsultant() || isAdmin();
      }
      
      // Rules for maturityAssessment subcollection (LEGACY - to be phased out)
      match /maturityAssessment/{assessmentId} {
        // Allow authenticated users to read assessments
        allow read: if isAuthenticated();
        
        // Allow consultants and admins to have full access to assessments
        allow create, update, delete: if isConsultant() || isAdmin();
        
        // Rules for domains within maturityAssessment
        match /domains/{domainId} {
          allow read: if isAuthenticated();
          allow create, update, delete: if isConsultant() || isAdmin();
          
          // Rules for ratings in domains
          match /ratings/{ratingId} {
            allow read: if isAuthenticated();
            allow create, update, delete: if isConsultant() || isAdmin();
          }
          
          // Rules for controls within domains
          match /controls/{controlId} {
            allow read: if isAuthenticated();
            allow create, update, delete: if isConsultant() || isAdmin();
            
            // Rules for ratings in controls
            match /ratings/{ratingId} {
              allow read: if isAuthenticated();
              allow create, update, delete: if isConsultant() || isAdmin();
            }
            
            // Rules for specifications within controls
            match /specifications/{specificationId} {
              allow read: if isAuthenticated();
              allow create, update, delete: if isConsultant() || isAdmin();
              
              // Rules for ratings in specifications
              match /ratings/{ratingId} {
                allow read: if isAuthenticated();
                allow create, update, delete: if isConsultant() || isAdmin();
              }
            }
          }
          
          // Rules for SWOT analysis in domains
          match /swot/{analysisId} {
            allow read: if isAuthenticated();
            allow create, update, delete: if isConsultant() || isAdmin();
          }
        }
      }
      
      // Rules for assessments subcollection
      match /assessments/{assessmentId} {
        allow read: if isAuthenticated();
        allow create, update, delete: if isConsultant() || isAdmin();
        
        // Rules for domains in assessments
        match /domains/{domainId} {
          allow read: if isAuthenticated();
          allow create, update, delete: if isConsultant() || isAdmin();
          
          // Rules for SWOT analysis in domains
          match /swot/{analysisId} {
            allow read: if isAuthenticated();
            allow create, update, delete: if isConsultant() || isAdmin();
          }
        }
        
        // Direct SWOT path under assessments
        match /swot/{domainId} {
          allow read: if isAuthenticated();
          allow create, update, delete: if isConsultant() || isAdmin();
        }
      }
      
      // Rules for meta subcollection
      match /meta/{document=**} {
        allow read: if isAuthenticated();
        allow create, update, delete: if isConsultant() || isAdmin();
      }
      
      // Rules for assetTypes subcollection
      match /assetTypes/{assetTypeId} {
        allow read: if isAuthenticated();
        allow create, update, delete: if isConsultant() || isAdmin();
      }

      // Rules for data assets subcollections within projects
      match /systems/{systemId} {
        allow read: if isAuthenticated();
        allow create, update, delete: if isConsultant() || isAdmin();
      }

      match /databases/{databaseId} {
        allow read: if isAuthenticated();
        allow create, update, delete: if isConsultant() || isAdmin();
      }

      match /datasets/{datasetId} {
        allow read: if isAuthenticated();
        allow create, update, delete: if isConsultant() || isAdmin();
      }

      match /structuredFiles/{fileId} {
        allow read: if isAuthenticated();
        allow create, update, delete: if isConsultant() || isAdmin();
      }

      match /apis/{apiId} {
        allow read: if isAuthenticated();
        allow create, update, delete: if isConsultant() || isAdmin();
      }

      match /dashboards/{dashboardId} {
        allow read: if isAuthenticated();
        allow create, update, delete: if isConsultant() || isAdmin();
      }
    }

    // Rules for domains collection - Admin only
    match /domains/{domainId} {
      // Allow any authenticated user to read domains
      allow read: if isAuthenticated();
      
      // Only allow admin users to create, update, and delete domains
      allow create: if isAdmin();
      allow update: if isAdmin();
      allow delete: if isAdmin();
      
      // Rules for controls within domains
      match /controls/{controlId} {
        allow read: if isAuthenticated();
        allow create, update, delete: if isAdmin();
        
        // Rules for specifications within controls
        match /specifications/{specificationId} {
          allow read: if isAuthenticated();
          allow create, update, delete: if isAdmin() || isConsultant();
        }
      }
    }

    // Rules for specifications collection
    match /npc/document/domains/{domainId}/controls/{controlId}/specifications/{specificationId} {
      allow read: if isAuthenticated();
      allow create, update, delete: if isAdmin() || isConsultant();
    }

    // Legacy rules for specifications collection (to be removed after migration)
    match /npc/document/domains/{domainId}/controls/{controlId}/specifications/{specificationId} {
      allow read: if isAuthenticated();
      allow create, update, delete: if isAdmin() || isConsultant();
    }
    
    // Rules for top-level specifications collection (to be removed after migration)
    match /specifications/{specificationId} {
      allow read: if isAuthenticated();
      allow create, update, delete: if isAdmin() || isConsultant();
    }

    // Rules for top-level ratings collection 
    match /ratings/{ratingId} {
      allow read: if isAuthenticated();
      allow create, update, delete: if isConsultant() || isAdmin();
    }

    // Rules for standalone maturityAssessment collection (for backward compatibility)
    match /maturityAssessment/{assessmentId} {
      // Allow authenticated users to read assessment
      allow read: if isAuthenticated();
      
      // Allow consultants and admins to have full access
      allow create, update, delete: if isConsultant() || isAdmin();
      
      // Rules for domains within standalone maturityAssessment
      match /domains/{domainId} {
        allow read: if isAuthenticated();
        allow create, update, delete: if isConsultant() || isAdmin();
        
        // Rules for ratings in domains (standalone)
        match /ratings/{ratingId} {
          allow read: if isAuthenticated();
          allow create, update, delete: if isConsultant() || isAdmin();
        }
        
        // Rules for controls within domains
        match /controls/{controlId} {
          allow read: if isAuthenticated();
          allow create, update, delete: if isConsultant() || isAdmin();
          
          // Rules for ratings in controls (standalone)
          match /ratings/{ratingId} {
            allow read: if isAuthenticated();
            allow create, update, delete: if isConsultant() || isAdmin();
          }
          
          // Rules for specifications within controls
          match /specifications/{specificationId} {
            allow read: if isAuthenticated();
            allow create, update, delete: if isConsultant() || isAdmin();
            
            // Rules for ratings in specifications (standalone)
            match /ratings/{ratingId} {
              allow read: if isAuthenticated();
              allow create, update, delete: if isConsultant() || isAdmin();
            }
          }
        }
        
        // Rules for SWOT analysis in domains (standalone)
        match /swot/{analysisId} {
          allow read: if isAuthenticated();
          allow create, update, delete: if isConsultant() || isAdmin();
        }
      }
    }

    // Rules for dataAssets collection
    match /dataAssets/{assetId} {
      // Allow any authenticated user to read data assets
      allow read: if isAuthenticated();
      
      // Allow consultants and admins to create, update, and delete data assets
      allow create, update, delete: if isConsultant() || isAdmin();
    }

    // Rules for dataAssetTypes collection
    match /dataAssetTypes/{typeId} {
      // Allow any authenticated user to read asset types
      allow read: if isAuthenticated();
      
      // Only allow consultants and admins to create, update, and delete asset types
      allow create, update, delete: if isConsultant() || isAdmin();
    }

    // Rules for dataAssetCategories collection
    match /dataAssetCategories/{categoryId} {
      // Allow any authenticated user to read categories
      allow read: if isAuthenticated();
      
      // Only allow consultants and admins to create, update, and delete categories
      allow create, update, delete: if isConsultant() || isAdmin();
    }
  }
}


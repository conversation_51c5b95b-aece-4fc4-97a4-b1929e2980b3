# Hooks Usage Documentation

This document provides an overview of all the custom hooks used in the Framework Assessment Application, detailing their purpose, data sources, and usage patterns.

## 📋 Table of Contents

1. [useAssessmentCriteria](#useassessmentcriteria)
2. [useAssessmentDetails](#useassessmentdetails)
3. [useAssessments](#useassessments)
4. [useAuthSession](#useauthsession)
5. [useDomainRatings](#usedomainratings)
6. [useDomainSpecifications](#usedomainspecifications)
7. [useFrameworks](#useframeworks)
8. [useRoleGuard](#useroleguard)

---

## useAssessmentCriteria

### Purpose
Fetches assessment criteria configuration for a specific framework, including rating types, levels, and domain weights.

### Data Sources
- **Firestore Collection**: `assessmentCriteria/{frameworkId}`
- **Document Structure**: Assessment criteria configuration document

### What It Fetches
- Assessment criteria type (maturity, percentage, compliance)
- Rating levels and their configurations
- Domain weights for weighted assessments
- Validation rules for domain weights

### Key Features
- Domain weight utilities (get, format, validate)
- Assessment criteria validation
- Localized formatting support

### Used In
- `maturity-assessment/[projectId]/[assessmentId]/[domainName]/page.tsx`
- `maturity-assessment/[projectId]/[assessmentId]/[domainName]/specification/[specificationId]/page.tsx`

### Return Values
```typescript
{
  assessmentCriteria: AssessmentCriteria | null,
  loading: boolean,
  error: string | null,
  getDomainWeight: (domainId: string) => number | null,
  getAllDomainWeights: () => Array<{ domainId: string; weight: number }>,
  formatDomainWeight: (weight: number | null, locale?: string, showPercentage?: boolean) => string,
  getDomainWeightConfig: (weight: number | null, locale?: string) => object,
  validateDomainWeights: (allowPartialWeights?: boolean) => object,
  hasDomainWeights: boolean,
  totalDomainWeight: number
}
```

---

## useAssessmentDetails

### Purpose
Fetches detailed information about a specific compliance assessment, including associated framework and domains.

### Data Sources
- **Firestore Collection**: `projects/{projectId}/ComplianceAssessment/{assessmentId}`
- **Related Collections**: 
  - `frameworks/{frameworkId}`
  - `frameworks/{frameworkId}/domains`
  - `npc/document/domains` (for NPC framework)

### What It Fetches
- Assessment metadata (name, description, status, dates)
- Associated framework information
- Domain list (lazy-loaded for performance)
- Framework-specific domain structures

### Key Features
- Lazy loading of domains for better performance
- Support for multiple framework structures
- Optimized for compliance assessment workflows

### Used In
- `maturity-assessment/[projectId]/[assessmentId]/page.tsx`

### Return Values
```typescript
{
  assessment: Assessment | null,
  framework: Framework | null,
  domains: Domain[],
  loading: boolean,
  error: string | null,
  domainsLoaded: boolean,
  loadDomains: () => Promise<void>
}
```

---

## useAssessments

### Purpose
Fetches all compliance assessments for a specific project with their associated framework information.

### Data Sources
- **Firestore Collection**: `projects/{projectId}/ComplianceAssessment`
- **Related Collections**: `frameworks/{frameworkId}` (for framework names)

### What It Fetches
- List of all assessments in a project
- Assessment metadata (name, description, status, timestamps)
- Associated framework names for each assessment
- Ordered by creation date (newest first)

### Key Features
- Automatic framework name resolution
- Error handling for missing frameworks
- Refetch capability for real-time updates

### Used In
- `maturity-assessment/page.tsx`

### Return Values
```typescript
{
  assessments: Assessment[],
  loading: boolean,
  error: string | null,
  refetch: () => Promise<void>
}
```

---

## useAuthSession

### Purpose
Provides access to the authenticated user's session data and authentication utilities.

### Data Sources
- **Context**: AuthContext (user session state)
- **Authentication Provider**: Firebase Auth

### What It Provides
- Current user session information
- Authentication status
- User role and permissions
- Organization and project assignments
- Logout functionality

### Key Features
- Structured session data access
- Loading and initialization states
- Error handling for auth failures
- Locale and role management

### Used In
- `components/shared/Sidebar.tsx`
- `components/shared/ProtectedRoute.tsx`
- `components/shared/LayoutWrapper.tsx`

### Return Values
```typescript
{
  session: User | null,
  isLoading: boolean,
  isInitialized: boolean,
  isAuthenticated: boolean,
  error: any,
  logout: () => void,
  sessionData: {
    uid: string,
    email: string,
    role: string,
    assignedProjectIds: string[],
    organizationId: string,
    locale: string,
    name: string
  } | null
}
```

---

## useDomainRatings

### Purpose
Fetches and calculates ratings for all specifications within a specific domain.

### Data Sources
- **Firestore Collections**: 
  - `projects/{projectId}/ratings`
  - `frameworks/{frameworkId}/domains/{domainId}/specifications`
  - `frameworks/{frameworkId}/domains/{domainId}/controls/{controlId}/specifications`

### What It Fetches
- Individual specification ratings
- Domain-level rating calculations
- Rating values across different assessment types
- Specification metadata for rating context

### Key Features
- Automatic specification discovery
- Multi-level rating aggregation
- Support for different rating types (maturity, percentage, compliance)
- Client-side filtering for performance

### Used In
- `useDomainRatingData` (as a dependency - but this hook was removed)

### Return Values
```typescript
{
  isLoading: boolean,
  error: Error | null,
  specRatings: DomainRating[],
  avgRating: number | null
}
```

---

## useDomainSpecifications

### Purpose
Fetches all specifications within a domain, including their ratings and control associations.

### Data Sources
- **Firestore Collections**:
  - `projects/{projectId}/ComplianceAssessment/{assessmentId}`
  - `frameworks/{frameworkId}/domains/{domainId}/controls/{controlId}/specifications`
  - `projects/{projectId}/ComplianceAssessment/{assessmentId}/ratings`
  - `projects/{projectId}/ratings` (fallback)

### What It Fetches
- Domain information and metadata
- All specifications within the domain
- Control structures and hierarchies
- Current ratings for each specification
- Assessment progress and completion status

### Key Features
- Multi-framework support (standard and NPC)
- Rating integration from multiple sources
- Control-specification relationship mapping
- Fallback rating structure support

### Used In
- `maturity-assessment/[projectId]/[assessmentId]/[domainName]/page.tsx`
- `maturity-assessment/[projectId]/[assessmentId]/[domainName]/specification/[specificationId]/page.tsx`

### Return Values
```typescript
{
  domain: Domain | null,
  specifications: Specification[],
  controls: Control[],
  loading: boolean,
  error: string | null
}
```

---

## useFrameworks

### Purpose
Fetches all available assessment frameworks in the system.

### Data Sources
- **Firestore Collection**: `frameworks`

### What It Fetches
- Complete list of available frameworks
- Framework metadata (names, descriptions, types)
- Framework configuration details

### Key Features
- Simple framework listing
- Error handling for fetch failures
- Loading state management

### Used In
- `maturity-assessment/page.tsx`

### Return Values
```typescript
{
  frameworks: Framework[],
  loading: boolean,
  error: string | null
}
```

---

## useRoleGuard

### Purpose
Protects routes based on user roles and handles unauthorized access.

### Data Sources
- **Context**: AuthContext (for user role information)
- **Router**: Next.js navigation

### What It Provides
- Role-based access control
- Automatic redirection for unauthorized users
- Loading states during authentication checks
- Route protection logic

### Key Features
- Support for multiple role types (Client, Consultant, Both)
- Automatic redirection to login/unauthorized pages
- Integration with Next.js routing
- Locale-aware redirections

### Used In
- `page.tsx` (root page)
- `data-assets/page.tsx`
- `project-selection/page.tsx`

### Return Values
```typescript
{
  isAuthorized: boolean,
  isLoading: boolean,
  user: User | null
}
```

---

## 🗑️ Removed Hooks

The following hooks were identified as unused and have been removed from the codebase:

### ~~useDomainRatingData~~ (REMOVED)
- **Reason**: No imports found in any component
- **Purpose**: Was designed to process domain rating data with assessment criteria
- **Status**: Deleted to clean up unused code

### ~~useMaturityAssessment~~ (REMOVED)
- **Reason**: No imports found in any component  
- **Purpose**: Was designed to handle maturity assessment operations
- **Status**: Deleted to clean up unused code

---

## 📊 Hook Usage Summary

| Hook | Used In Components | Primary Purpose | Data Sources |
|------|-------------------|-----------------|--------------|
| useAssessmentCriteria | 2 components | Assessment configuration | assessmentCriteria collection |
| useAssessmentDetails | 1 component | Assessment metadata | ComplianceAssessment collection |
| useAssessments | 1 component | Assessment listing | ComplianceAssessment collection |
| useAuthSession | 3 components | User authentication | AuthContext |
| useDomainRatings | 0 components (internal) | Domain rating calculation | ratings collection |
| useDomainSpecifications | 2 components | Domain content | Multiple framework collections |
| useFrameworks | 1 component | Framework listing | frameworks collection |
| useRoleGuard | 3 components | Access control | AuthContext + Router |

---

## 🔄 Data Flow Patterns

### Assessment Workflow
1. `useFrameworks` → Lists available frameworks
2. `useAssessments` → Shows assessments for selected project
3. `useAssessmentDetails` → Loads specific assessment details
4. `useDomainSpecifications` → Fetches domain content for assessment
5. `useAssessmentCriteria` → Provides rating configuration

### Authentication Flow
1. `useAuthSession` → Manages user session
2. `useRoleGuard` → Protects routes based on user role

### Rating System
1. `useDomainRatings` → Calculates domain-level ratings
2. `useAssessmentCriteria` → Provides rating configuration and validation

---

*Last Updated: December 2024*
*Total Active Hooks: 8*
*Removed Unused Hooks: 2* 
{"Index": {"boilerplateName": "Your Project Name", "title": "Welcome to Your Project", "description": "A powerful starting point for your next multilingual web application."}, "Sidebar": {"homePage": "Home Page", "maturityAssessment": "Compliance Assessment", "transformationRoadmap": "Transformation Roadmap", "dataAssets": "Data Assets", "logicalArchitecture": "Logical Architecture", "reporting": "Reporting", "feedback": "<PERSON><PERSON><PERSON>", "projectSelection": "Project Selection", "switchProjects": "Switch between your assigned projects"}, "Auth": {"loginTitle": "Welcome Back", "email": "Email", "password": "Password", "login": "Log In", "loading": "Loading...", "loggingIn": "Logging In...", "redirecting": "Redirecting...", "dontHaveAccount": "Don't have an account?", "contactAdmin": "Contact your administrator for access.", "validation": {"emailRequired": "Email is required", "passwordRequired": "Password is required"}, "invalidCredentials": "Invalid email or password", "accountInactive": "Your account is inactive", "serverError": "Server error occurred", "tooManyAttempts": "Too many attempts. Try again later", "userNotInDatabase": "User not found in database", "networkError": "Network connection error", "accessDenied": "Access Denied", "invalidRole": "Invalid role for portal access", "invalidRoleForPortal": "You don't have the required role to access this portal", "backToLogin": "Back to Login", "unauthorizedAccess": "You do not have permission to access this area or your account is inactive", "returnToLogin": "Return to Login"}, "Home": {"heroTitle": "DevFlow AI", "heroSubtitle": "An integrated platform using AI to streamline and accelerate development processes and project management.", "getStarted": "Get Started"}, "Dashboard": {"clientTitle": "Client Dashboard", "consultantTitle": "Consultant Dashboard", "welcomeMessage": "Welcome", "assignedProjects": "Assigned Projects", "noProjects": "No assigned projects yet", "select": "Select", "complianceDone": "Compliance Assessment Done", "complianceNotDone": "Compliance Not Done", "score": "Score", "status": "Status", "deadline": "Deadline", "startDate": "Start Date", "organization": "Organization", "language": "Language", "onHolding": "On Hold", "inProgress": "In Progress", "completed": "Completed", "selectProject": "Select a project to continue", "framework": "Framework", "noDomainsFound": "No domains found for this framework"}, "ComplianceAssessment": {"pageTitle": "Compliance Assessment", "description": "View and manage the compliance assessment for your project.", "addAssessment": "Create New Assessment", "addAssessmentButton": "Add Assessment", "assessmentName": "Assessment Name", "assessmentNameEn": "Assessment Name (English)", "assessmentNameAr": "Assessment Name (Arabic)", "assessmentDescription": "Assessment Description", "framework": "Framework", "selectFramework": "Select a framework", "frameworkRequired": "Framework is required", "assessmentNameRequired": "Assessment name is required", "assessmentDescriptionRequired": "Assessment description is required", "createAssessment": "Create Assessment", "cancel": "Cancel", "saving": "Creating...", "successCreate": "Assessment created successfully", "errorCreate": "Error creating assessment", "noFrameworks": "No frameworks available", "loadingFrameworks": "Loading frameworks...", "assessments": "Assessments", "noAssessments": "No Assessments", "noAssessmentsDescription": "You haven't created any assessments yet. Click the button above to create your first assessment.", "viewAssessment": "View Assessment", "projectRequired": "Please select a project to create assessments"}, "ProjectSelection": {"welcome": "Welcome to", "subtitle": "Select a project to begin your development journey", "noProjects": "You don't have any assigned projects yet", "loading": "Loading your projects...", "clientWelcome": "Client Project Portal", "consultantWelcome": "Consultant Project Portal", "viewProject": "Open Project", "status": "Status", "deadline": "Deadline", "startDate": "Start Date", "onHolding": "On Hold", "inProgress": "In Progress", "completed": "Completed", "createProject": "Create New Project", "comingSoon": "Coming Soon", "clientDescription": "Access and monitor your ongoing projects. Select a project to view details and progress.", "consultantDescription": "Manage your client projects. The projects that you have", "comingSoonModules": "Our other modules are currently in development and will be available soon.", "workInProgress": "Work in Progress", "applicationPlanning": "Application Planning", "applicationPlanningDesc": "Plan your application architecture, user stories, data models, and workflows using AI-powered tools.", "strategyModule": "Strategy Drafting", "strategyModuleDesc": "Plan your project strategy, business requirements, and market analysis with AI assistance.", "marketingStrategy": "Marketing Strategy", "marketingStrategyDesc": "Design and manage impactful marketing campaigns with analytics-driven insights and optimization.", "framework": "Framework"}, "heroHeader": {"welcomeTitle": "Welcome to Our Platform", "welcomeSubtitle": "An innovative solution for your business needs", "getStarted": "Get Started", "learnMore": "Learn More", "dashboardTitle": "Dashboard Overview", "dashboardSubtitle": "Monitor and manage your project progress", "maturityTitle": "Compliance Assessment", "maturitySubtitle": "Evaluate and enhance your organization's compliance capabilities", "roadmapTitle": "Transformation Roadmap", "roadmapSubtitle": "Plan and track your organization's digital transformation journey", "dataTitle": "Data Assets", "dataSubtitle": "Manage and optimize your organization's data resources", "architectureTitle": "Logical Architecture", "architectureSubtitle": "Design and analyze your system's logical structure", "reportingTitle": "Reporting Dashboard", "reportingSubtitle": "Access insights and analytics about your projects", "feedbackTitle": "Feedback Center", "feedbackSubtitle": "Share your thoughts and help us improve"}, "error": {"title": "Something went wrong!", "sorry": "Sorry, an unexpected error occurred.", "tryAgain": "Try again", "returnHome": "Return home"}, "Metadata": {"title": "Your Project Name - Multilingual Web Application", "description": "A multilingual web application supporting English and Arabic languages with modern UI and best practices.", "keywords": "Next.js, i18n, internationalization, Arabic, English, multilingual"}, "AssessmentDetails": {"backButton": "Back to Assessments", "project": "Project", "status": "Status", "createdAt": "Created At", "lastUpdatedAt": "Last Updated At", "overviewTitle": "Overview", "overview": "Overview", "domainDetails": "Domain Details", "overallMaturity": "Overall Maturity", "topPerformers": "Top Performers", "areasForImprovement": "Areas for Improvement", "swotAnalysis": "SWOT Analysis", "strengths": "Strengths", "weaknesses": "Weaknesses", "opportunities": "Opportunities", "threats": "Threats", "visualizations": "Visualizations", "maturityRadar": "Maturity Radar Chart", "domainDistribution": "Domain Scores Distribution", "maturityTrend": "Maturity Trend Analysis", "domainsTitle": "Detailed Domain Analysis", "score": "Score", "targetScore": "Target Score", "domains": "Domains", "notFound": "Assessment not found.", "noDescriptionAvailable": "No description available.", "noData": "No data", "chartPlaceholderText": "Chart will be displayed here", "projectName": "Project Name", "assessmentTitle": "Assessment Title", "assessmentDescription": "Assessment Description", "domainName": "Domain Name", "assessmentInformation": "Assessment Information", "assessmentName": "Assessment Name", "framework": "Framework", "created": "Created", "lastUpdated": "Last Updated", "description": "Description", "domainSummary": "Domain Summary", "specifications": "Specifications", "totalDomains": "Total Domains", "totalSpecifications": "Total Specifications", "averageScore": "Average Score", "expandAll": "Expand All", "collapseAll": "Collapse All", "searchDomains": "Search domains...", "sortBy": "Sort by", "sortByName": "Name", "sortByScore": "Score", "sortBySpecifications": "Specifications", "domainsFound": "domains found", "noDomainsFound": "No domains found", "noDomainsAvailable": "No domains are available for this assessment", "noDomainMatch": "No domains match your search term", "clearSearch": "Clear search", "noSpecificationsFound": "No specifications found for this domain", "notAssessed": "Not assessed"}, "SWOT": {"generateSWOT": "Generate SWOT Analysis", "viewSWOT": "View SWOT Analysis", "viewSWOTAnalysis": "View SWOT Analysis", "swotAnalysis": "SWOT Analysis", "swotTitle": "SWOT Analysis for", "strengthsTitle": "Strengths", "weaknessesTitle": "Weaknesses", "relatedSpecifications": "Related Specifications", "noStrengths": "No strengths identified", "noWeaknesses": "No weaknesses identified", "loading": "Loading SWOT analysis...", "generating": "Generating SWOT analysis...", "generatedSuccess": "SWOT Analysis generated successfully!", "errorGenerating": "Error generating SWOT analysis", "specDistribution": "Distribution across Specifications", "controlDistribution": "Distribution across Controls", "goBack": "Back to Domain", "priority": "Priority", "high": "High", "medium": "Medium", "low": "Low", "specification": "Specification", "specifications": "Specifications", "swotCompletion": "SWOT Analysis Completion", "completed": "Completed", "notCompleted": "Not Completed"}, "DomainDetails": {"title": "Domain Specifications", "backToDomains": "Back to Domains", "backToAssessment": "Back to Assessment", "backToDomain": "Back to Domain Details", "specificationTitle": "Specification Title", "specificationStatus": "Specification Status", "noSpecifications": "No specifications found for this domain.", "noSpecificationsDescription": "This domain does not have any specifications defined yet.", "level": "Level", "viewDetails": "View Details", "swotAnalysis": "SWOT Analysis", "updateCompliance": "Update Compliance", "sortBy": "Sort by", "filterBy": "Filter by", "showFilters": "Show Filters", "hideFilters": "Hide Filters", "filters": "Filters", "clearFilters": "Clear Filters", "search": "Search", "control": "Control", "all": "All", "level1": "Level 1", "level2": "Level 2", "level3": "Level 3", "level4": "Level 4", "level5": "Level 5", "titleAsc": "Title (A-Z)", "titleDesc": "Title (Z-A)", "complianceStatus": "Compliance Status", "compliant": "Compliant", "partiallyCompliant": "Partially Compliant", "nonCompliant": "Non-Compliant", "notApplicable": "Not Applicable", "percentageValue": "Completion Percentage", "percentComplete": "% Complete", "dataRating": "Data Rating", "swotStatus": "SWOT Status", "assessmentDate": "Assessment Date", "notCompleted": "Not Completed", "completed": "Completed", "from": "From", "to": "To", "dataRatings": {"High": "High", "Medium": "Medium", "Low": "Low", "Moderate": "Moderate", "Documented": "Documented"}}, "SWOTAnalysis": {"backToDomain": "Back to Domain", "swotAnalysis": "SWOT Analysis for", "totalSWOTItems": "Total SWOT Items", "strengthsVsWeaknesses": "Strengths vs Weaknesses", "strengths": "Strengths", "weaknesses": "Weaknesses", "relatedSpecifications": "Related Specifications", "noStrengths": "No strengths identified", "noWeaknesses": "No weaknesses identified", "noDataAvailable": "No data available", "distributionAcrossControls": "Distribution across Controls", "distributionAcrossSpecifications": "Distribution across Specifications", "regenerateSwot": "Regenerate SWOT Analysis", "deleteSwot": "Delete SWOT Analysis", "deletionWarning": "Are you sure you want to delete this SWOT analysis? This action cannot be undone.", "confirm": "Delete", "cancel": "Cancel"}, "SpecificationDetails": {"backToDomain": "Back to Domain", "overview": "Overview", "subSpecifications": "Sub-Specifications", "deliverables": "Deliverables", "owner": "Owner", "department": "Department", "assessmentDate": "Assessment Date", "lastUpdated": "Last Updated", "dataRating": "Data Rating", "swotStatus": "SWOT Analysis", "editRating": "Edit Rating", "saveRating": "Save Rating", "cancel": "Cancel", "saving": "Saving...", "complianceStatus": "Compliance Status", "complianceAssessment": "Compliance Assessment", "selectComplianceStatus": "Select compliance status", "compliant": "Compliant", "nonCompliant": "Non-Compliant", "partiallyCompliant": "Partially Compliant", "notApplicable": "Not Applicable", "percentageCompletion": "Completion Percentage", "completionPercentage": "Completion Percentage", "percentageDescription": "Set the percentage of completion for this specification", "comments": "Comments", "commentsPlaceholder": "Add any comments or notes about this rating", "ratingSaved": "Rating saved successfully", "errorSaving": "Error saving rating", "unauthorizedAction": "You are not authorized to perform this action", "noSpecification": "Specification not found", "noDomain": "Domain not found", "noProject": "Project not found", "errorFetching": "Error fetching data", "onlyComplianceSupported": "This system only supports compliance-based assessments. Please contact your administrator.", "priority": "Priority", "notRated": "Not Rated", "showDetails": "Show Details", "hideDetails": "Hide Details", "lowRating": "Low Rating", "mediumRating": "Medium Rating", "highRating": "High Rating", "statusLabels": {"Completed": "Completed", "In Progress": "In Progress", "Not Started": "Not Started"}, "dataRatings": {"High": "High", "Medium": "Medium", "Low": "Low", "undefined": "Not Rated"}, "complianceDescriptions": {"Compliant": "Fully meets all requirements and standards", "Non-Compliant": "Does not meet requirements and standards", "Partially-Compliant": "Meets some but not all requirements", "Not-Applicable": "Requirements do not apply to this specification"}, "completed": "Completed", "notCompleted": "Not Completed", "viewDocument": "View Document", "noSubSpecifications": "No Sub-Specifications Found", "noSubSpecificationsDescription": "This specification does not have any sub-specifications defined yet", "noDeliverables": "No Deliverables Found", "noDeliverablesDescription": "This specification does not have any deliverables defined yet", "subSpecificationsDescription": "Related sub-specifications that are part of this specification", "deliverablesDescription": "Deliverables and documentation associated with this specification", "completedOn": "Completed on", "dueBy": "Due by", "noDateSet": "No date set", "objectives": "Objectives", "implementation": "Implementation", "challenges": "Challenges", "benefits": "Benefits", "metrics": "Metrics", "basicInformation": "Basic Information", "number": "Number", "name": "Name", "description": "Description", "capabilityLevel": "Capability Level", "dependency": "Dependency", "versionHistory": "Version History", "specificationDetails": "Specification Details", "noDependency": "No dependency information available", "subSpecificationsFound": "Sub-Specifications Found", "viewInSubspecsTab": "View details in the Sub-Specifications tab", "noVersionHistory": "No version history available", "viewDetails": "View Details", "subSpecificationStates": {"title": "Sub-Specification Status", "completed": "Completed", "notCompleted": "Not Completed", "noted": "Noted", "selectState": "Select state", "stateRequired": "State is required"}, "subSpecificationsRating": "Rate Sub-Specifications", "subSpecificationsRatingDescription": "Please rate each sub-specification to complete the assessment", "saveSubSpecificationsRating": "Save Sub-Specifications Rating", "subSpecificationsRatingSaved": "Sub-specifications rating saved successfully", "errorSavingSubSpecifications": "Error saving sub-specifications rating", "domainComplianceAssessment": "Domain Compliance Assessment", "basedOn": "Based on"}, "Ratings": {"complianceStatus": "Compliance Status", "complianceStatusOptions": {"compliant": "Compliant", "partiallyCompliant": "Partially Compliant", "nonCompliant": "Non-Compliant", "notApplicable": "Not Applicable"}, "show": "Show", "details": "Details"}, "DataAssets": {"pageTitle": "Data Assets", "description": "Manage and explore your organization's data assets", "addAssetType": "Add Asset Type", "viewAssets": "View Details", "noAssetTypes": "No Asset Types", "noAssetTypesDescription": "You haven't created any asset types yet. Click the button below to create your first asset type.", "consultantOnly": "Only consultants can create new asset types.", "readOnlyMode": "You are in read-only mode. Only consultants can modify asset types.", "untitled": "Untitled Asset", "noDescription": "No description available", "successCreate": "Asset type created successfully", "successEdit": "Asset type updated successfully", "successDelete": "Asset type deleted successfully", "errorOccurred": "An error occurred", "tryAgain": "Please try again", "deleteAssetType": "Delete Asset Type", "deleteConfirmation": "Are you sure you want to delete this asset type?", "deleteConfirmationDescription": "This action cannot be undone.", "cancel": "Cancel", "delete": "Delete", "saving": "Saving...", "projectRequired": "Please select a project to view asset types.", "assetCards": {"systems": {"title": "Systems", "description": "Manage and monitor your enterprise systems and infrastructure", "features": "Integration capabilities, monitoring, and system health tracking"}, "databases": {"title": "Databases", "description": "Centralized database management and optimization tools", "features": "Performance monitoring, backup management, and query optimization"}, "datasets": {"title": "Datasets", "description": "Comprehensive data collection and analysis platform", "features": "Data validation, transformation, and quality assessment"}}}, "Datasets": {"title": "Datasets", "subtitle": "Comprehensive dataset management and data governance tools", "overview": "Overview", "inventory": "Inventory", "overviewTitle": "Dataset Overview", "overviewDescription": "Get comprehensive insights into your data assets, quality monitoring, and usage analytics.", "inventoryTitle": "Dataset Inventory", "inventoryDescription": "Explore and manage your complete dataset catalog with detailed metadata and lineage information.", "datasetName": "Dataset Name", "description": "Description", "owner": "Owner", "size": "Size", "format": "Format", "updateFrequency": "Update Frequency", "sensitivity": "Sensitivity", "lastUpdated": "Last Updated", "databasesUsed": "Databases Used", "systemsThatUseIt": "Systems That Use It", "searchDatasets": "Search datasets...", "addDataset": "Add Dataset", "noDatasets": "No datasets found", "noDatasetsDescription": "Get started by adding your first dataset to the inventory.", "datasetNamePlaceholder": "Enter dataset name (e.g., Customer Data)", "descriptionPlaceholder": "Brief description of the dataset's purpose", "ownerPlaceholder": "Enter dataset owner", "sizePlaceholder": "e.g., 10GB, 500MB", "formatPlaceholder": "e.g., CSV, JSON, XML", "datasetCreatedSuccess": "Dataset created successfully!", "datasetUpdatedSuccess": "Dataset updated successfully!", "datasetDeletedSuccess": "Dataset deleted successfully!", "datasetCreationError": "Failed to create dataset", "datasetUpdateError": "Failed to update dataset", "datasetDeleteError": "Failed to delete dataset", "addingDatasetInstructions": "Fill in the required fields below to add a new dataset to your inventory.", "editingDatasetInstructions": "You can edit any field by clicking on it. Changes will be saved automatically."}, "Systems": {"title": "Systems", "subtitle": "Manage and monitor your enterprise systems and infrastructure", "overview": "Overview", "inventory": "Inventory", "overviewTitle": "Systems Overview", "overviewDescription": "Get comprehensive insights into your system performance, health monitoring, and integration capabilities.", "inventoryTitle": "Systems Inventory", "inventoryDescription": "Explore and manage your complete systems catalog with detailed specifications and configurations.", "systemIndex": "System Index", "systemName": "System Name", "description": "Description", "systemDomain": "System Domain", "ownerDepartment": "Owner Department", "status": "Status", "searchSystems": "Search systems...", "addSystem": "Add System", "noSystems": "No systems found", "noSystemsDescription": "Get started by adding your first system to the inventory.", "systemNamePlaceholder": "Enter system name (e.g., CRM System)", "descriptionPlaceholder": "Brief description of the system's purpose", "systemDomainPlaceholder": "Enter system domain (e.g., Sales, Finance)", "ownerDepartmentPlaceholder": "Enter the owning department", "systemCreatedSuccess": "System created successfully!", "systemUpdatedSuccess": "System updated successfully!", "systemDeletedSuccess": "System deleted successfully!", "systemCreationError": "Failed to create system", "systemUpdateError": "Failed to update system", "systemDeleteError": "Failed to delete system", "addingSystemInstructions": "Fill in the required fields below to add a new system to your inventory. System index will be automatically generated.", "editingSystemInstructions": "You can edit any field by clicking on it. Changes will be saved automatically.", "analytics": {"title": "System Analytics", "kpis": {"totalSystems": "Total Systems", "activeSystems": "Active Systems", "systemHealth": "System Health", "riskSystems": "Risk Systems", "systemsByDomain": "Systems by Domain", "systemsByDepartment": "Systems by Department", "systemsByStatus": "Systems by Status"}, "charts": {"statusDistribution": "Status Distribution", "domainDistribution": "Domain Distribution", "departmentDistribution": "Department Distribution", "systemGrowth": "System Growth Over Time", "healthMetrics": "System Health Metrics"}, "noData": "No data available", "noChartData": "Insufficient data for chart visualization", "loadingCharts": "Loading system analytics...", "statusLabels": {"Active": "Active", "Inactive": "Inactive", "Retired": "Retired", "Under Review": "Under Review"}, "healthLabels": {"excellent": "Excellent", "good": "Good", "warning": "Warning", "critical": "Critical"}, "insights": {"totalSystemsDesc": "Total number of systems in your inventory", "activeSystemsDesc": "Systems currently operational and active", "systemHealthDesc": "Overall system health percentage based on status", "riskSystemsDesc": "Systems requiring attention or review"}}, "bulkUpload": {"title": "Bulk Upload Systems", "subtitle": "Upload multiple systems at once using Excel", "uploadButton": "Upload Excel File", "downloadTemplate": "Download Template", "dragDropText": "Drag and drop your Excel file here, or click to browse", "supportedFormats": "Supported formats: .xlsx, .xls", "maxFileSize": "Maximum file size: 10MB", "processing": "Processing file...", "preview": "Preview Data", "previewSubtitle": "Review the data before importing", "rowsFound": "rows found", "importButton": "Import Systems", "importAll": "Import All", "importSelected": "Import Selected", "selectAll": "Select All", "deselectAll": "Deselect All", "next": "Next", "previous": "Previous", "showing": "Showing", "of": "of", "rows": "rows", "validData": "Valid data ready for import", "hasErrors": "Some rows have errors", "fixErrors": "Please fix the errors before importing", "importSuccess": "Successfully imported", "importError": "Import failed", "duplicateFound": "Duplicate system names found", "missingRequired": "Missing required fields", "invalidFormat": "Invalid file format", "fileTooLarge": "File too large", "noDataFound": "No valid data found in file", "back": "Back", "bulkUploadTitle": "Bulk Upload", "templateColumns": {"systemName": "System Name", "description": "Description", "systemDomain": "System Domain", "ownerDepartment": "Owner Department", "status": "Status"}, "validation": {"required": "Required field", "duplicate": "Duplicate name", "invalidStatus": "Invalid status value", "tooLong": "Text too long"}}}, "Databases": {"title": "Databases", "subtitle": "Centralized database management and optimization tools", "overview": "Overview", "inventory": "Inventory", "overviewTitle": "Database Overview", "overviewDescription": "Get comprehensive insights into your database performance, health monitoring, and optimization capabilities.", "inventoryTitle": "Database Inventory", "inventoryDescription": "Explore and manage your complete database catalog with detailed specifications and configurations.", "databaseName": "Database Name", "databaseSystem": "Database System", "dbms": "DBMS", "technology": "Technology", "storageDevice": "Storage Device", "databaseLocation": "Database Location", "description": "Description", "status": "Status", "searchDatabases": "Search databases...", "addDatabase": "Add Database", "noDatabases": "No databases found", "noDatabasesDescription": "Get started by adding your first database to the inventory.", "databaseNamePlaceholder": "Enter database name (e.g., Customer Database)", "descriptionPlaceholder": "Brief description of the database's purpose", "technologyPlaceholder": "Enter technology (e.g., Cloud, On-premise)", "storageDevicePlaceholder": "Enter storage device (e.g., SSD, HDD)", "databaseLocationPlaceholder": "Enter database location (e.g., Data Center A)", "noLinkedSystem": "No Linked System", "databaseCreatedSuccess": "Database created successfully!", "databaseUpdatedSuccess": "Database updated successfully!", "databaseDeletedSuccess": "Database deleted successfully!", "databaseCreationError": "Failed to create database", "databaseUpdateError": "Failed to update database", "databaseDeleteError": "Failed to delete database", "addingDatabaseInstructions": "Fill in the required fields below to add a new database to your inventory.", "editingDatabaseInstructions": "You can edit any field by clicking on it. Changes will be saved automatically.", "analytics": {"title": "Database Analytics", "kpis": {"totalDatabases": "Total Databases", "onlineDatabases": "Online Databases", "databaseHealth": "Database Health", "riskDatabases": "Risk Databases", "databasesByDbms": "Databases by DBMS", "databasesByTechnology": "Databases by Technology", "databasesByStatus": "Databases by Status"}, "charts": {"statusDistribution": "Status Distribution", "dbmsDistribution": "DBMS Distribution", "technologyDistribution": "Technology Distribution", "databaseGrowth": "Database Growth Over Time", "healthMetrics": "Database Health Metrics"}, "noData": "No data available", "noChartData": "Insufficient data for chart visualization", "loadingCharts": "Loading database analytics...", "statusLabels": {"Online": "Online", "Offline": "Offline", "Maintenance": "Maintenance", "Archived": "Archived"}, "healthLabels": {"excellent": "Excellent", "good": "Good", "warning": "Warning", "critical": "Critical"}, "insights": {"totalDatabasesDesc": "Total number of databases in your inventory", "onlineDatabasesDesc": "Databases currently operational and online", "databaseHealthDesc": "Overall database health percentage based on status", "riskDatabasesDesc": "Databases requiring attention or review"}}, "bulkUpload": {"title": "Bulk Upload Databases", "subtitle": "Upload multiple databases at once using Excel", "uploadButton": "Upload Excel File", "downloadTemplate": "Download Template", "dragDropText": "Drag and drop your Excel file here, or click to browse", "supportedFormats": "Supported formats: .xlsx, .xls", "maxFileSize": "Maximum file size: 10MB", "processing": "Processing file...", "preview": "Preview Data", "previewSubtitle": "Review the data before importing", "rowsFound": "rows found", "importButton": "Import Databases", "importAll": "Import All", "importSelected": "Import Selected", "selectAll": "Select All", "deselectAll": "Deselect All", "next": "Next", "previous": "Previous", "showing": "Showing", "of": "of", "rows": "rows", "validData": "Valid data ready for import", "hasErrors": "Some rows have errors", "fixErrors": "Please fix the errors before importing", "importSuccess": "Successfully imported", "importError": "Import failed", "duplicateFound": "Duplicate database names found", "missingRequired": "Missing required fields", "invalidFormat": "Invalid file format", "fileTooLarge": "File too large", "noDataFound": "No valid data found in file", "back": "Back", "bulkUploadTitle": "Bulk Upload", "templateColumns": {"databaseName": "Database Name", "description": "Description", "databaseSystem": "Database System", "dbms": "DBMS", "technology": "Technology", "storageDevice": "Storage Device", "databaseLocation": "Database Location", "status": "Status"}, "validation": {"required": "Required field", "duplicate": "Duplicate name", "invalidStatus": "Invalid status value", "invalidDbms": "Invalid DBMS value", "tooLong": "Text too long"}}}}